# This file is dual licensed under the terms of the Apache License, Version
# 2.0, and the BSD License. See the LICENSE file in the root of this repository
# for complete details.

from __future__ import annotations

from cryptography.hazmat._oid import (
    AttributeOID,
    AuthorityInformationAccessOID,
    CertificatePoliciesOID,
    CRLEntryExtensionOID,
    ExtendedKeyUsageOID,
    ExtensionOID,
    NameOID,
    ObjectIdentifier,
    OCSPExtensionOID,
    OtherNameFormOID,
    PublicKeyAlgorithmOID,
    SignatureAlgorithmOID,
    SubjectInformationAccessOID,
)

__all__ = [
    "AttributeOID",
    "AuthorityInformationAccessOID",
    "CRLEntryExtensionOID",
    "CertificatePoliciesOID",
    "ExtendedKeyUsageOID",
    "ExtensionOID",
    "NameOID",
    "OCSPExtensionOID",
    "ObjectIdentifier",
    "OtherNameFormOID",
    "PublicKeyAlgorithmOID",
    "SignatureAlgorithmOID",
    "SubjectInformationAccessOID",
]
