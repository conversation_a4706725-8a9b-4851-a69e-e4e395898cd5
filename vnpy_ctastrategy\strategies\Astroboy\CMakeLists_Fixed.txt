# CMakeLists.txt for Astroboy Signal Generator - Fixed Version
# 阿童木信号生成器 - 修复版本构建配置

cmake_minimum_required(VERSION 3.16)
project(AstroboyFixed LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 编译选项
option(ASTROBOY_ENABLE_CUDA "Enable CUDA support" OFF)
option(ASTROBOY_ENABLE_TESTING "Enable unit testing" OFF)
option(ASTROBOY_BUILD_SHARED "Build shared library" ON)

# 设置构建类型
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Windows特定设置
if(WIN32)
    # 设置运行时库为静态链接，避免依赖问题
    set(CMAKE_MSVC_RUNTIME_LIBRARY "MultiThreaded$<$<CONFIG:Debug>:Debug>")
    
    # 编译器优化选项
    if(CMAKE_BUILD_TYPE STREQUAL "Release")
        set(CMAKE_CXX_FLAGS_RELEASE "/O2 /DNDEBUG /MT")
    elseif(CMAKE_BUILD_TYPE STREQUAL "Debug")
        set(CMAKE_CXX_FLAGS_DEBUG "/Od /Zi /MTd")
    endif()
    
    # 添加Windows特定定义
    add_definitions(-DWIN32 -D_WINDOWS -D_USRDLL -DASTROBOY_EXPORTS)
    add_definitions(-D_CRT_SECURE_NO_WARNINGS)
endif()

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/simple_build/include
    ${CMAKE_CURRENT_SOURCE_DIR}/include
)

# 源文件
set(ASTROBOY_SOURCES
    simple_build/src/common/atomboy_signal.cpp
)

# 头文件
set(ASTROBOY_HEADERS
    simple_build/include/atomboy_signal.h
)

# 创建共享库
if(ASTROBOY_BUILD_SHARED)
    add_library(astroboy_fixed SHARED ${ASTROBOY_SOURCES} ${ASTROBOY_HEADERS})
    
    # 设置DLL导出
    if(WIN32)
        set_target_properties(astroboy_fixed PROPERTIES
            OUTPUT_NAME "astroboy_fixed"
            SUFFIX ".dll"
            WINDOWS_EXPORT_ALL_SYMBOLS ON
        )
    endif()
else()
    add_library(astroboy_fixed STATIC ${ASTROBOY_SOURCES} ${ASTROBOY_HEADERS})
endif()

# 链接库
if(WIN32)
    target_link_libraries(astroboy_fixed)
endif()

# 设置输出目录
set_target_properties(astroboy_fixed PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
    ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
)

# 安装规则
install(TARGETS astroboy_fixed
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

install(FILES ${ASTROBOY_HEADERS}
    DESTINATION include
)

# 输出配置信息
message(STATUS "")
message(STATUS "=== Astroboy Fixed Configuration ===")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Build shared: ${ASTROBOY_BUILD_SHARED}")
message(STATUS "CUDA support: ${ASTROBOY_ENABLE_CUDA}")
message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "====================================")
message(STATUS "")
