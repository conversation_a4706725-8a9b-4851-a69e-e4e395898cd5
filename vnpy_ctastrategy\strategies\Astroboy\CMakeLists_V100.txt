# CMakeLists.txt for Astroboy V100 GPU Accelerated Signal Generator
# 阿童木V100 GPU加速信号生成器构建配置

cmake_minimum_required(VERSION 3.18)
project(AstroboyV100 LANGUAGES CXX CUDA)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置CUDA标准
set(CMAKE_CUDA_STANDARD 17)
set(CMAKE_CUDA_STANDARD_REQUIRED ON)

# 编译选项
option(ASTROBOY_ENABLE_CUDA "Enable CUDA support for V100 acceleration" ON)
option(ASTROBOY_ENABLE_TESTING "Enable unit testing" ON)
option(ASTROBOY_ENABLE_BENCHMARKS "Enable performance benchmarks" ON)
option(ASTROBOY_ENABLE_PYTHON_BINDINGS "Enable Python bindings" ON)

# 设置构建类型
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# 编译器优化选项
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    set(CMAKE_CXX_FLAGS_RELEASE "-O3 -march=native -mtune=native -flto -DNDEBUG")
    set(CMAKE_CUDA_FLAGS_RELEASE "-O3 -DNDEBUG")
elseif(CMAKE_BUILD_TYPE STREQUAL "Debug")
    set(CMAKE_CXX_FLAGS_DEBUG "-O0 -g -fsanitize=address -fno-omit-frame-pointer")
    set(CMAKE_CUDA_FLAGS_DEBUG "-O0 -g -G")
endif()

# 查找依赖包
find_package(CUDA REQUIRED)
find_package(CUDAToolkit REQUIRED)

# 检查CUDA架构
if(ASTROBOY_ENABLE_CUDA)
    # V100支持的CUDA架构
    set(CMAKE_CUDA_ARCHITECTURES "70;75;80;86")
    
    # 检查CUDA版本
    if(CUDA_VERSION VERSION_LESS "11.0")
        message(WARNING "CUDA version ${CUDA_VERSION} may not fully support V100 features. Recommend CUDA 11.0+")
    endif()
    
    # 启用CUDA编译定义
    add_definitions(-DASTROBOY_ENABLE_CUDA)
    
    message(STATUS "CUDA support enabled")
    message(STATUS "CUDA version: ${CUDA_VERSION}")
    message(STATUS "CUDA architectures: ${CMAKE_CUDA_ARCHITECTURES}")
else()
    message(STATUS "CUDA support disabled")
endif()

# 查找其他依赖
find_package(Threads REQUIRED)

# 可选依赖
find_package(OpenMP)
if(OpenMP_CXX_FOUND)
    message(STATUS "OpenMP found: ${OpenMP_CXX_VERSION}")
endif()

# Python绑定依赖
if(ASTROBOY_ENABLE_PYTHON_BINDINGS)
    find_package(Python3 COMPONENTS Interpreter Development REQUIRED)
    find_package(pybind11 REQUIRED)
    message(STATUS "Python bindings enabled")
endif()

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${CUDA_INCLUDE_DIRS}
)

# 源文件
set(ASTROBOY_CORE_SOURCES
    src/common/atomboy_signal.cpp
    src/signal/signal_generator.cpp
    src/signal/technical_indicators.cpp
    src/signal/signal_fusion.cpp
    src/ml/ml_signal_source.cpp
    src/utils/memory_pool.c
    src/utils/thread_pool.cpp
    src/utils/async_logger.cpp
)

# V100 GPU加速源文件
if(ASTROBOY_ENABLE_CUDA)
    set(ASTROBOY_GPU_SOURCES
        src/gpu/v100_gpu_accelerator.cpp
        src/gpu/cuda_kernels.cu
        src/gpu/gpu_memory_manager.cu
        src/gpu/gpu_technical_indicators.cu
        src/gpu/gpu_feature_engineering.cu
    )
endif()

# 扩展模块源文件
set(ASTROBOY_EXTENDED_SOURCES
    src/ml/ml_onnx_integration.c
    src/ml/ml_xgboost_integration.c
    src/ml/ml_model_trainer.c
    src/ml/ml_inference_optimizer.c
    src/rl/reinforcement_learning.cpp
    src/rl/position_manager.cpp
    src/signal/fundamental_signal_source.cpp
    src/signal/sentiment_signal_source.cpp
    src/signal/multi_dimensional_signal.cpp
    src/utils/deception_detector.cpp
    src/utils/memory_leak_detector.c
    src/utils/memory_mapped_file.cpp
    src/utils/simd_utils.cpp
)

# 创建核心库
add_library(astroboy_core STATIC ${ASTROBOY_CORE_SOURCES})

# 创建GPU加速库
if(ASTROBOY_ENABLE_CUDA)
    add_library(astroboy_gpu STATIC ${ASTROBOY_GPU_SOURCES})
    
    # 设置CUDA属性
    set_target_properties(astroboy_gpu PROPERTIES
        CUDA_SEPARABLE_COMPILATION ON
        CUDA_RESOLVE_DEVICE_SYMBOLS ON
    )
    
    # 链接CUDA库
    target_link_libraries(astroboy_gpu
        CUDA::cudart
        CUDA::cublas
        CUDA::curand
        CUDA::cufft
        CUDA::cusparse
    )
endif()

# 创建扩展库
add_library(astroboy_extended STATIC ${ASTROBOY_EXTENDED_SOURCES})

# 创建主库
if(ASTROBOY_ENABLE_CUDA)
    add_library(astroboy SHARED 
        $<TARGET_OBJECTS:astroboy_core>
        $<TARGET_OBJECTS:astroboy_gpu>
        $<TARGET_OBJECTS:astroboy_extended>
    )
    target_link_libraries(astroboy astroboy_core astroboy_gpu astroboy_extended)
else()
    add_library(astroboy SHARED 
        $<TARGET_OBJECTS:astroboy_core>
        $<TARGET_OBJECTS:astroboy_extended>
    )
    target_link_libraries(astroboy astroboy_core astroboy_extended)
endif()

# 链接系统库
target_link_libraries(astroboy
    Threads::Threads
    ${CMAKE_DL_LIBS}
)

# OpenMP支持
if(OpenMP_CXX_FOUND)
    target_link_libraries(astroboy OpenMP::OpenMP_CXX)
endif()

# 设置库属性
set_target_properties(astroboy PROPERTIES
    VERSION 2.0.0
    SOVERSION 2
    OUTPUT_NAME "astroboy_v100"
)

# Windows特定设置
if(WIN32)
    set_target_properties(astroboy PROPERTIES
        WINDOWS_EXPORT_ALL_SYMBOLS ON
        RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
        LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
        ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
    )
endif()

# Python绑定
if(ASTROBOY_ENABLE_PYTHON_BINDINGS)
    pybind11_add_module(astroboy_python
        src/python/python_bindings.cpp
        src/python/numpy_interface.cpp
    )
    
    target_link_libraries(astroboy_python PRIVATE astroboy)
    
    set_target_properties(astroboy_python PROPERTIES
        OUTPUT_NAME "astroboy_v100"
        SUFFIX "${PYTHON_MODULE_EXTENSION}"
    )
endif()

# 测试
if(ASTROBOY_ENABLE_TESTING)
    enable_testing()
    
    # 查找Google Test
    find_package(GTest)
    if(GTest_FOUND)
        add_subdirectory(tests)
        message(STATUS "Unit testing enabled")
    else()
        message(WARNING "Google Test not found, unit testing disabled")
    endif()
endif()

# 基准测试
if(ASTROBOY_ENABLE_BENCHMARKS)
    # 查找Google Benchmark
    find_package(benchmark)
    if(benchmark_FOUND)
        add_subdirectory(benchmarks)
        message(STATUS "Performance benchmarks enabled")
    else()
        message(WARNING "Google Benchmark not found, benchmarks disabled")
    endif()
endif()

# 安装规则
install(TARGETS astroboy
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

install(DIRECTORY include/
    DESTINATION include
    FILES_MATCHING PATTERN "*.h" PATTERN "*.hpp"
)

if(ASTROBOY_ENABLE_PYTHON_BINDINGS)
    install(TARGETS astroboy_python
        DESTINATION ${Python3_SITEARCH}
    )
endif()

# 打包配置
set(CPACK_PACKAGE_NAME "AstroboyV100")
set(CPACK_PACKAGE_VERSION "2.0.0")
set(CPACK_PACKAGE_DESCRIPTION "Astroboy V100 GPU Accelerated Signal Generator")
set(CPACK_PACKAGE_VENDOR "Quantitative Trading Expert")

include(CPack)

# 输出配置信息
message(STATUS "")
message(STATUS "=== Astroboy V100 Configuration Summary ===")
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "CUDA support: ${ASTROBOY_ENABLE_CUDA}")
if(ASTROBOY_ENABLE_CUDA)
    message(STATUS "CUDA version: ${CUDA_VERSION}")
    message(STATUS "CUDA architectures: ${CMAKE_CUDA_ARCHITECTURES}")
endif()
message(STATUS "OpenMP support: ${OpenMP_CXX_FOUND}")
message(STATUS "Python bindings: ${ASTROBOY_ENABLE_PYTHON_BINDINGS}")
message(STATUS "Unit testing: ${ASTROBOY_ENABLE_TESTING}")
message(STATUS "Benchmarks: ${ASTROBOY_ENABLE_BENCHMARKS}")
message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "===========================================")
message(STATUS "")
