#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
阿童木V100 GPU加速信号生成器 Python包装器
提供高性能的GPU加速量化交易信号生成功能
"""

import os
import sys
import ctypes
import numpy as np
from typing import Dict, List, Optional, Union, Any, Tuple
from datetime import datetime
import time
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("AstroboyV100")

class V100PerformanceStats:
    """V100性能统计"""
    
    def __init__(self):
        self.total_gpu_time = 0.0
        self.total_cpu_time = 0.0
        self.gpu_operations = 0
        self.cpu_operations = 0
        self.speedup_ratio = 1.0
        self.gpu_memory_used = 0.0
        self.gpu_utilization = 0.0
    
    def update(self, gpu_time: float, cpu_time: float):
        """更新性能统计"""
        self.total_gpu_time += gpu_time
        self.total_cpu_time += cpu_time
        self.gpu_operations += 1 if gpu_time > 0 else 0
        self.cpu_operations += 1 if cpu_time > 0 else 0
        
        if self.total_gpu_time > 0:
            self.speedup_ratio = self.total_cpu_time / self.total_gpu_time
    
    def __str__(self) -> str:
        return (f"V100性能统计:\n"
                f"GPU操作: {self.gpu_operations}次, 总时间: {self.total_gpu_time:.4f}s\n"
                f"CPU操作: {self.cpu_operations}次, 总时间: {self.total_cpu_time:.4f}s\n"
                f"加速比: {self.speedup_ratio:.2f}x\n"
                f"GPU内存使用: {self.gpu_memory_used/1024/1024:.2f}MB\n"
                f"GPU利用率: {self.gpu_utilization*100:.1f}%")

class GPUPriceData:
    """GPU优化的价格数据结构"""
    
    def __init__(self, open_price: float, high: float, low: float, 
                 close: float, volume: float, timestamp: int):
        self.open = float(open_price)
        self.high = float(high)
        self.low = float(low)
        self.close = float(close)
        self.volume = float(volume)
        self.timestamp = int(timestamp)
    
    def to_array(self) -> np.ndarray:
        """转换为numpy数组"""
        return np.array([self.open, self.high, self.low, self.close, self.volume, self.timestamp], 
                       dtype=np.float32)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Union[float, int]]) -> 'GPUPriceData':
        """从字典创建"""
        return cls(
            data.get('open', 0.0),
            data.get('high', 0.0),
            data.get('low', 0.0),
            data.get('close', 0.0),
            data.get('volume', 0.0),
            data.get('timestamp', 0)
        )

class V100IndicatorConfig:
    """V100指标配置"""
    
    def __init__(self):
        self.ma_periods = [5, 10, 20, 50, 200]
        self.rsi_period = 14
        self.macd_fast = 12
        self.macd_slow = 26
        self.macd_signal = 9
        self.bollinger_period = 20
        self.bollinger_std_mult = 2.0
        self.kdj_period = 9
        self.kdj_k_period = 3
        self.kdj_d_period = 3
        self.volatility_periods = [10, 20, 50]

class V100IndicatorResults:
    """V100指标计算结果"""
    
    def __init__(self):
        self.ma_results = []          # 多周期MA结果
        self.rsi_results = []         # RSI结果
        self.macd_dif = []           # MACD DIF
        self.macd_dea = []           # MACD DEA
        self.macd_histogram = []     # MACD柱状图
        self.bb_upper = []           # 布林带上轨
        self.bb_middle = []          # 布林带中轨
        self.bb_lower = []           # 布林带下轨
        self.kdj_k = []              # KDJ K值
        self.kdj_d = []              # KDJ D值
        self.kdj_j = []              # KDJ J值
        self.volatility_results = [] # 波动率结果
        
        # 性能信息
        self.computation_time = 0.0
        self.used_gpu = False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'ma_results': self.ma_results,
            'rsi_results': self.rsi_results,
            'macd': {
                'dif': self.macd_dif,
                'dea': self.macd_dea,
                'histogram': self.macd_histogram
            },
            'bollinger_bands': {
                'upper': self.bb_upper,
                'middle': self.bb_middle,
                'lower': self.bb_lower
            },
            'kdj': {
                'k': self.kdj_k,
                'd': self.kdj_d,
                'j': self.kdj_j
            },
            'volatility_results': self.volatility_results,
            'performance': {
                'computation_time': self.computation_time,
                'used_gpu': self.used_gpu
            }
        }

class AstroboyV100SignalGenerator:
    """
    阿童木V100 GPU加速信号生成器
    提供高性能的量化交易信号生成功能
    """
    
    def __init__(self, dll_path: Optional[str] = None, enable_gpu: bool = True):
        """
        初始化V100信号生成器
        
        Args:
            dll_path: DLL路径，如果为None则自动查找
            enable_gpu: 是否启用GPU加速
        """
        self.dll_path = dll_path
        self.enable_gpu = enable_gpu
        self._lib = None
        self._gpu_available = False
        self._v100_available = False
        self.performance_stats = V100PerformanceStats()
        
        # 加载DLL
        self._load_dll()
        
        # 初始化GPU
        if self.enable_gpu:
            self._initialize_gpu()
    
    def _load_dll(self):
        """加载DLL"""
        if self.dll_path is None:
            # 自动查找DLL
            current_dir = os.path.dirname(os.path.abspath(__file__))
            possible_paths = [
                os.path.join(current_dir, "astroboy_v100.dll"),
                os.path.join(current_dir, "simple_build", "astroboy_v100.dll"),
                os.path.join(current_dir, "build", "bin", "Release", "astroboy_v100.dll"),
                os.path.join(current_dir, "build", "lib", "Release", "astroboy_v100.dll"),
            ]
            
            for path in possible_paths:
                if os.path.exists(path):
                    self.dll_path = path
                    break
        
        if not self.dll_path or not os.path.exists(self.dll_path):
            raise FileNotFoundError(f"找不到阿童木V100 DLL: {self.dll_path}")
        
        try:
            logger.info(f"加载阿童木V100 DLL: {self.dll_path}")
            self._lib = ctypes.CDLL(self.dll_path)
            self._setup_function_prototypes()
            logger.info("阿童木V100信号生成器加载成功")
        except Exception as e:
            raise RuntimeError(f"加载DLL失败: {str(e)}")
    
    def _setup_function_prototypes(self):
        """设置函数原型"""
        # 基本功能
        self._lib.getVersion.restype = ctypes.c_char_p
        self._lib.getVersion.argtypes = []
        
        self._lib.printHello.restype = None
        self._lib.printHello.argtypes = []
        
        # GPU管理
        self._lib.initializeV100.restype = ctypes.c_bool
        self._lib.initializeV100.argtypes = []
        
        self._lib.isV100Available.restype = ctypes.c_bool
        self._lib.isV100Available.argtypes = []
        
        self._lib.getGPUInfo.restype = ctypes.c_char_p
        self._lib.getGPUInfo.argtypes = []
        
        # 性能统计
        self._lib.getPerformanceStats.restype = ctypes.c_char_p
        self._lib.getPerformanceStats.argtypes = []
        
        self._lib.resetPerformanceStats.restype = None
        self._lib.resetPerformanceStats.argtypes = []
        
        # 技术指标计算
        self._lib.calculateAllIndicatorsV100.restype = ctypes.c_bool
        self._lib.calculateAllIndicatorsV100.argtypes = [
            ctypes.POINTER(ctypes.c_float),  # price_data
            ctypes.c_int,                    # data_size
            ctypes.POINTER(ctypes.c_int),    # config
            ctypes.POINTER(ctypes.c_float),  # results
            ctypes.POINTER(ctypes.c_double)  # computation_time
        ]
        
        # 特征工程
        self._lib.extractFeaturesV100.restype = ctypes.c_bool
        self._lib.extractFeaturesV100.argtypes = [
            ctypes.POINTER(ctypes.c_float),  # price_data
            ctypes.c_int,                    # data_size
            ctypes.POINTER(ctypes.c_int),    # config
            ctypes.POINTER(ctypes.c_float),  # features
            ctypes.POINTER(ctypes.c_int)     # feature_count
        ]
    
    def _initialize_gpu(self):
        """初始化GPU"""
        try:
            if hasattr(self._lib, 'initializeV100'):
                self._gpu_available = self._lib.initializeV100()
                if self._gpu_available:
                    self._v100_available = self._lib.isV100Available()
                    gpu_info = self._lib.getGPUInfo().decode('utf-8')
                    logger.info(f"GPU初始化成功:\n{gpu_info}")
                    
                    if self._v100_available:
                        logger.info("检测到V100 GPU，启用高性能加速模式")
                    else:
                        logger.warning("未检测到V100 GPU，使用通用GPU加速模式")
                else:
                    logger.warning("GPU初始化失败，使用CPU模式")
            else:
                logger.warning("DLL不支持GPU功能，使用CPU模式")
        except Exception as e:
            logger.error(f"GPU初始化异常: {str(e)}")
            self._gpu_available = False
            self._v100_available = False
    
    def get_version(self) -> str:
        """获取版本信息"""
        version = self._lib.getVersion()
        return version.decode('utf-8')
    
    def get_gpu_info(self) -> str:
        """获取GPU信息"""
        if self._gpu_available:
            gpu_info = self._lib.getGPUInfo()
            return gpu_info.decode('utf-8')
        else:
            return "GPU不可用"
    
    def is_v100_available(self) -> bool:
        """检查V100是否可用"""
        return self._v100_available
    
    def is_gpu_enabled(self) -> bool:
        """检查GPU是否启用"""
        return self._gpu_available and self.enable_gpu
    
    def calculate_all_indicators(self, price_data: List[GPUPriceData], 
                               config: Optional[V100IndicatorConfig] = None) -> V100IndicatorResults:
        """
        计算所有技术指标（V100加速）
        
        Args:
            price_data: 价格数据列表
            config: 指标配置
            
        Returns:
            指标计算结果
        """
        if not price_data:
            raise ValueError("价格数据不能为空")
        
        if config is None:
            config = V100IndicatorConfig()
        
        results = V100IndicatorResults()
        
        # 准备数据
        data_size = len(price_data)
        price_array = np.zeros((data_size, 6), dtype=np.float32)
        
        for i, data in enumerate(price_data):
            price_array[i] = data.to_array()
        
        # 准备配置数组
        config_array = np.array([
            len(config.ma_periods),
            config.rsi_period,
            config.macd_fast,
            config.macd_slow,
            config.macd_signal,
            config.bollinger_period,
            config.kdj_period,
            config.kdj_k_period,
            config.kdj_d_period,
            len(config.volatility_periods)
        ] + config.ma_periods + config.volatility_periods, dtype=np.int32)
        
        # 准备结果数组
        max_results_size = data_size * (len(config.ma_periods) + 10)  # 估算结果大小
        results_array = np.zeros(max_results_size, dtype=np.float32)
        computation_time = ctypes.c_double(0.0)
        
        # 调用DLL函数
        start_time = time.time()
        
        success = self._lib.calculateAllIndicatorsV100(
            price_array.ctypes.data_as(ctypes.POINTER(ctypes.c_float)),
            ctypes.c_int(data_size),
            config_array.ctypes.data_as(ctypes.POINTER(ctypes.c_int)),
            results_array.ctypes.data_as(ctypes.POINTER(ctypes.c_float)),
            ctypes.byref(computation_time)
        )
        
        end_time = time.time()
        total_time = end_time - start_time
        
        if not success:
            raise RuntimeError("技术指标计算失败")
        
        # 解析结果
        self._parse_indicator_results(results_array, config, results, data_size)
        
        # 更新性能统计
        results.computation_time = computation_time.value
        results.used_gpu = self._gpu_available
        
        if self._gpu_available:
            self.performance_stats.update(computation_time.value, 0.0)
        else:
            self.performance_stats.update(0.0, total_time)
        
        logger.info(f"技术指标计算完成 - 用时: {total_time:.4f}s, GPU加速: {results.used_gpu}")
        
        return results
    
    def _parse_indicator_results(self, results_array: np.ndarray, 
                               config: V100IndicatorConfig,
                               results: V100IndicatorResults,
                               data_size: int):
        """解析指标结果"""
        offset = 0
        
        # 解析MA结果
        for i, period in enumerate(config.ma_periods):
            ma_data = results_array[offset:offset + data_size].tolist()
            results.ma_results.append(ma_data)
            offset += data_size
        
        # 解析RSI结果
        results.rsi_results = results_array[offset:offset + data_size].tolist()
        offset += data_size
        
        # 解析MACD结果
        results.macd_dif = results_array[offset:offset + data_size].tolist()
        offset += data_size
        results.macd_dea = results_array[offset:offset + data_size].tolist()
        offset += data_size
        results.macd_histogram = results_array[offset:offset + data_size].tolist()
        offset += data_size
        
        # 解析布林带结果
        results.bb_upper = results_array[offset:offset + data_size].tolist()
        offset += data_size
        results.bb_middle = results_array[offset:offset + data_size].tolist()
        offset += data_size
        results.bb_lower = results_array[offset:offset + data_size].tolist()
        offset += data_size
        
        # 解析KDJ结果
        results.kdj_k = results_array[offset:offset + data_size].tolist()
        offset += data_size
        results.kdj_d = results_array[offset:offset + data_size].tolist()
        offset += data_size
        results.kdj_j = results_array[offset:offset + data_size].tolist()
        offset += data_size
        
        # 解析波动率结果
        for i, period in enumerate(config.volatility_periods):
            vol_data = results_array[offset:offset + data_size].tolist()
            results.volatility_results.append(vol_data)
            offset += data_size
    
    def extract_features(self, price_data: List[GPUPriceData], 
                        lookback_period: int = 50) -> np.ndarray:
        """
        提取特征（V100加速）
        
        Args:
            price_data: 价格数据
            lookback_period: 回看周期
            
        Returns:
            特征向量
        """
        if len(price_data) < lookback_period:
            raise ValueError(f"数据长度不足，需要至少{lookback_period}条数据")
        
        # 准备数据
        data_size = len(price_data)
        price_array = np.zeros((data_size, 6), dtype=np.float32)
        
        for i, data in enumerate(price_data):
            price_array[i] = data.to_array()
        
        # 准备配置
        config_array = np.array([lookback_period], dtype=np.int32)
        
        # 准备结果数组
        max_features = 100  # 最大特征数
        features_array = np.zeros(max_features, dtype=np.float32)
        feature_count = ctypes.c_int(0)
        
        # 调用DLL函数
        success = self._lib.extractFeaturesV100(
            price_array.ctypes.data_as(ctypes.POINTER(ctypes.c_float)),
            ctypes.c_int(data_size),
            config_array.ctypes.data_as(ctypes.POINTER(ctypes.c_int)),
            features_array.ctypes.data_as(ctypes.POINTER(ctypes.c_float)),
            ctypes.byref(feature_count)
        )
        
        if not success:
            raise RuntimeError("特征提取失败")
        
        # 返回有效特征
        return features_array[:feature_count.value]
    
    def get_performance_stats(self) -> V100PerformanceStats:
        """获取性能统计"""
        if hasattr(self._lib, 'getPerformanceStats'):
            stats_str = self._lib.getPerformanceStats().decode('utf-8')
            logger.info(f"DLL性能统计:\n{stats_str}")
        
        return self.performance_stats
    
    def reset_performance_stats(self):
        """重置性能统计"""
        self.performance_stats = V100PerformanceStats()
        if hasattr(self._lib, 'resetPerformanceStats'):
            self._lib.resetPerformanceStats()
    
    def benchmark_performance(self, price_data: List[GPUPriceData], 
                            iterations: int = 10) -> Dict[str, float]:
        """
        性能基准测试
        
        Args:
            price_data: 测试数据
            iterations: 测试次数
            
        Returns:
            性能测试结果
        """
        logger.info(f"开始性能基准测试 - 数据量: {len(price_data)}, 测试次数: {iterations}")
        
        # 重置统计
        self.reset_performance_stats()
        
        # GPU测试
        gpu_times = []
        if self._gpu_available:
            for i in range(iterations):
                start_time = time.time()
                results = self.calculate_all_indicators(price_data)
                end_time = time.time()
                gpu_times.append(end_time - start_time)
        
        # 计算统计
        benchmark_results = {
            'data_size': len(price_data),
            'iterations': iterations,
            'gpu_available': self._gpu_available,
            'v100_available': self._v100_available,
        }
        
        if gpu_times:
            benchmark_results.update({
                'gpu_avg_time': np.mean(gpu_times),
                'gpu_min_time': np.min(gpu_times),
                'gpu_max_time': np.max(gpu_times),
                'gpu_std_time': np.std(gpu_times),
            })
        
        # 获取最终性能统计
        final_stats = self.get_performance_stats()
        benchmark_results['performance_stats'] = str(final_stats)
        
        logger.info(f"性能基准测试完成:\n{benchmark_results}")
        
        return benchmark_results
    
    def __del__(self):
        """析构函数"""
        if self._lib and hasattr(self._lib, 'cleanup'):
            try:
                self._lib.cleanup()
            except:
                pass
