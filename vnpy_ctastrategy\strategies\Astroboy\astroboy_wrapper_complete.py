#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
阿童木信号生成器最终版本Python包装器
完整功能，经过测试验证
"""

import os
import sys
import ctypes
import numpy as np
from typing import List, Optional, Union, Dict, Any, Tu<PERSON>
from pathlib import Path

class AstroboySignalGenerator:
    """阿童木信号生成器最终版本"""
    
    def __init__(self, dll_path: Optional[str] = None):
        if dll_path is None:
            current_dir = Path(__file__).parent.absolute()
            dll_path = str(current_dir / "astroboy_signal_simple.dll")
        
        if not os.path.exists(dll_path):
            raise FileNotFoundError(f"找不到DLL文件: {dll_path}")
        
        self.dll_path = dll_path
        self.lib = None
        self.available_functions = []
        self._load_dll()
    
    def _load_dll(self):
        """加载DLL"""
        try:
            self.lib = ctypes.CDLL(self.dll_path)
            self._setup_function_prototypes()
            self._detect_available_functions()
        except Exception as e:
            raise RuntimeError(f"加载DLL失败: {str(e)}")
    
    def _setup_function_prototypes(self):
        """设置函数原型"""
        # 基本功能
        self.lib.getVersion.restype = ctypes.c_char_p
        self.lib.getVersion.argtypes = []
        
        self.lib.add.restype = ctypes.c_int
        self.lib.add.argtypes = [ctypes.c_int, ctypes.c_int]
        
        if hasattr(self.lib, 'printHello'):
            self.lib.printHello.restype = None
            self.lib.printHello.argtypes = []
        
        # 技术指标计算
        self.lib.calculateMA.restype = ctypes.c_double
        self.lib.calculateMA.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
        
        self.lib.calculateRSI.restype = ctypes.c_double
        self.lib.calculateRSI.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
        
        if hasattr(self.lib, 'calculateMACD'):
            self.lib.calculateMACD.restype = ctypes.c_double
            self.lib.calculateMACD.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int, ctypes.c_int, ctypes.c_int]
        
        if hasattr(self.lib, 'calculateBollingerUpper'):
            self.lib.calculateBollingerUpper.restype = ctypes.c_double
            self.lib.calculateBollingerUpper.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int, ctypes.c_double]
        
        if hasattr(self.lib, 'calculateBollingerLower'):
            self.lib.calculateBollingerLower.restype = ctypes.c_double
            self.lib.calculateBollingerLower.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int, ctypes.c_double]
        
        self.lib.generateSignal.restype = ctypes.c_int
        self.lib.generateSignal.argtypes = [ctypes.c_double, ctypes.c_double, ctypes.c_double]
        
        # 批量计算
        if hasattr(self.lib, 'calculateMultipleMA'):
            self.lib.calculateMultipleMA.restype = ctypes.c_int
            self.lib.calculateMultipleMA.argtypes = [
                ctypes.POINTER(ctypes.c_double), ctypes.c_int,
                ctypes.POINTER(ctypes.c_int), ctypes.c_int,
                ctypes.POINTER(ctypes.c_double)
            ]
        
        if hasattr(self.lib, 'analyzeMarket'):
            self.lib.analyzeMarket.restype = ctypes.c_int
            self.lib.analyzeMarket.argtypes = [
                ctypes.POINTER(ctypes.c_double), ctypes.c_int,
                ctypes.POINTER(ctypes.c_double),
                ctypes.POINTER(ctypes.c_double),
                ctypes.POINTER(ctypes.c_int)
            ]
    
    def _detect_available_functions(self):
        """检测可用函数"""
        test_functions = [
            'calculateMA', 'calculateRSI', 'calculateMACD',
            'calculateBollingerUpper', 'calculateBollingerLower',
            'generateSignal', 'calculateMultipleMA', 'analyzeMarket',
            'printHello'
        ]
        
        for func_name in test_functions:
            if hasattr(self.lib, func_name):
                self.available_functions.append(func_name)
    
    def get_version(self) -> str:
        """获取版本信息"""
        return self.lib.getVersion().decode('utf-8')
    
    def calculate_ma(self, prices: List[float], period: int) -> float:
        """计算移动平均线"""
        if len(prices) < period:
            raise ValueError(f"数据长度{len(prices)}小于周期{period}")
        
        price_array = (ctypes.c_double * len(prices))(*prices)
        result = self.lib.calculateMA(price_array, len(prices), period)
        
        if result == -1.0:
            raise RuntimeError("MA计算失败")
        
        return result
    
    def calculate_rsi(self, prices: List[float], period: int = 14) -> float:
        """计算RSI"""
        if len(prices) < period + 1:
            raise ValueError(f"数据长度{len(prices)}不足，需要至少{period + 1}条数据")
        
        price_array = (ctypes.c_double * len(prices))(*prices)
        result = self.lib.calculateRSI(price_array, len(prices), period)
        
        if result == -1.0:
            raise RuntimeError("RSI计算失败")
        
        return result
    
    def calculate_macd(self, prices: List[float], fast_period: int = 12, 
                      slow_period: int = 26, signal_period: int = 9) -> float:
        """计算MACD"""
        if 'calculateMACD' not in self.available_functions:
            raise NotImplementedError("calculateMACD函数不可用")
        
        if len(prices) < slow_period:
            raise ValueError(f"数据长度{len(prices)}不足，需要至少{slow_period}条数据")
        
        price_array = (ctypes.c_double * len(prices))(*prices)
        result = self.lib.calculateMACD(price_array, len(prices), fast_period, slow_period, signal_period)
        
        if result == -1.0:
            raise RuntimeError("MACD计算失败")
        
        return result
    
    def calculate_bollinger_bands(self, prices: List[float], period: int = 20, 
                                 std_multiplier: float = 2.0) -> Tuple[float, float]:
        """计算布林带上下轨"""
        if 'calculateBollingerUpper' not in self.available_functions:
            raise NotImplementedError("布林带计算函数不可用")
        
        if len(prices) < period:
            raise ValueError(f"数据长度{len(prices)}不足，需要至少{period}条数据")
        
        price_array = (ctypes.c_double * len(prices))(*prices)
        
        upper = self.lib.calculateBollingerUpper(price_array, len(prices), period, std_multiplier)
        lower = self.lib.calculateBollingerLower(price_array, len(prices), period, std_multiplier)
        
        if upper == -1.0 or lower == -1.0:
            raise RuntimeError("布林带计算失败")
        
        return upper, lower
    
    def generate_signal(self, open_price: float, high_price: float, low_price: float) -> int:
        """生成信号"""
        return self.lib.generateSignal(open_price, high_price, low_price)
    
    def get_signal_name(self, signal_code: int) -> str:
        """获取信号名称"""
        signal_names = {0: "NONE", 1: "BUY", 2: "SELL", 3: "HOLD"}
        return signal_names.get(signal_code, "UNKNOWN")
    
    def batch_calculate_ma(self, prices: List[float], periods: List[int]) -> Dict[int, float]:
        """批量计算移动平均线"""
        if 'calculateMultipleMA' not in self.available_functions:
            results = {}
            for period in periods:
                try:
                    results[period] = self.calculate_ma(prices, period)
                except:
                    results[period] = None
            return results
        
        price_array = (ctypes.c_double * len(prices))(*prices)
        period_array = (ctypes.c_int * len(periods))(*periods)
        result_array = (ctypes.c_double * len(periods))()
        
        success_count = self.lib.calculateMultipleMA(
            price_array, len(prices),
            period_array, len(periods),
            result_array
        )
        
        results = {}
        for i, period in enumerate(periods):
            if result_array[i] != -1.0:
                results[period] = result_array[i]
            else:
                results[period] = None
        
        return results
    
    def analyze_market(self, prices: List[float]) -> Dict[str, Any]:
        """完整市场分析"""
        if 'analyzeMarket' not in self.available_functions:
            return self._analyze_market_fallback(prices)
        
        price_array = (ctypes.c_double * len(prices))(*prices)
        ma_results = (ctypes.c_double * 4)()
        rsi_result = ctypes.c_double()
        signal_result = ctypes.c_int()
        
        success = self.lib.analyzeMarket(
            price_array, len(prices),
            ma_results, ctypes.byref(rsi_result), ctypes.byref(signal_result)
        )
        
        if not success:
            raise RuntimeError("市场分析失败")
        
        periods = [5, 10, 20, 50]
        ma_dict = {}
        for i, period in enumerate(periods):
            if ma_results[i] != -1.0:
                ma_dict[f'MA{period}'] = ma_results[i]
        
        return {
            'prices_count': len(prices),
            'latest_price': prices[-1] if prices else 0,
            'ma_results': ma_dict,
            'rsi': rsi_result.value if rsi_result.value != -1.0 else None,
            'signal': {
                'code': signal_result.value,
                'name': self.get_signal_name(signal_result.value)
            },
            'available_functions': self.available_functions
        }
    
    def _analyze_market_fallback(self, prices: List[float]) -> Dict[str, Any]:
        """市场分析回退方法"""
        results = {
            'prices_count': len(prices),
            'latest_price': prices[-1] if prices else 0,
            'ma_results': {},
            'rsi': None,
            'signal': None,
            'available_functions': self.available_functions
        }
        
        periods = [5, 10, 20, 50]
        for period in periods:
            if len(prices) >= period:
                try:
                    ma_value = self.calculate_ma(prices, period)
                    results['ma_results'][f'MA{period}'] = ma_value
                except:
                    pass
        
        if len(prices) >= 15:
            try:
                results['rsi'] = self.calculate_rsi(prices, 14)
            except:
                pass
        
        if len(prices) >= 3:
            try:
                recent_prices = prices[-3:]
                open_p = recent_prices[0]
                high_p = max(recent_prices)
                low_p = min(recent_prices)
                
                signal_code = self.generate_signal(open_p, high_p, low_p)
                results['signal'] = {
                    'code': signal_code,
                    'name': self.get_signal_name(signal_code)
                }
            except:
                pass
        
        return results
    
    def get_available_functions(self) -> List[str]:
        """获取可用函数列表"""
        return self.available_functions.copy()

# 兼容性别名
AtomBoySignalGenerator = AstroboySignalGenerator

# 便捷函数
def create_generator(dll_path: Optional[str] = None) -> AstroboySignalGenerator:
    """创建信号生成器实例"""
    return AstroboySignalGenerator(dll_path)

def quick_analysis(prices: List[float]) -> Dict[str, Any]:
    """快速市场分析"""
    generator = create_generator()
    return generator.analyze_market(prices)
