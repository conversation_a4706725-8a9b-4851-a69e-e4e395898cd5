#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
阿童木信号生成器最终版本Python包装器
自动生成于编译测试过程
"""

import os
import sys
import ctypes
import numpy as np
from typing import List, Optional, Union, Dict, Any
from pathlib import Path

class AstroboySignalGenerator:
    """阿童木信号生成器最终版本"""
    
    def __init__(self, dll_path: Optional[str] = None):
        if dll_path is None:
            # 使用测试通过的DLL路径
            dll_path = r"C:\veighna_studio\Lib\site-packages\vnpy_ctastrategy\strategies\Astroboy\astroboy_working.dll"
        
        if not os.path.exists(dll_path):
            raise FileNotFoundError(f"找不到DLL文件: {dll_path}")
        
        self.dll_path = dll_path
        self.lib = None
        self.available_functions = []
        self._load_dll()
    
    def _load_dll(self):
        """加载DLL"""
        try:
            # 使用绝对路径加载DLL
            self.lib = ctypes.CDLL(self.dll_path)
            self._setup_function_prototypes()
            self._detect_available_functions()
        except Exception as e:
            raise RuntimeError(f"加载DLL失败: {str(e)}")
    
    def _setup_function_prototypes(self):
        """设置函数原型"""
        # 基本功能
        self.lib.getVersion.restype = ctypes.c_char_p
        self.lib.getVersion.argtypes = []
        
        self.lib.add.restype = ctypes.c_int
        self.lib.add.argtypes = [ctypes.c_int, ctypes.c_int]
        
        if hasattr(self.lib, 'printHello'):
            self.lib.printHello.restype = None
            self.lib.printHello.argtypes = []
        
        # 技术指标计算
        if hasattr(self.lib, 'calculateMA'):
            self.lib.calculateMA.restype = ctypes.c_double
            self.lib.calculateMA.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
        
        if hasattr(self.lib, 'calculateRSI'):
            self.lib.calculateRSI.restype = ctypes.c_double
            self.lib.calculateRSI.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
        
        if hasattr(self.lib, 'generateSignal'):
            self.lib.generateSignal.restype = ctypes.c_int
            self.lib.generateSignal.argtypes = [ctypes.c_double, ctypes.c_double, ctypes.c_double]
    
    def _detect_available_functions(self):
        """检测可用函数"""
        test_functions = [
            'calculateMA', 'calculateRSI', 'generateSignal', 
            'calculateMACD', 'calculateBollingerBands', 'calculateKDJ',
            'printHello'
        ]
        
        for func_name in test_functions:
            if hasattr(self.lib, func_name):
                self.available_functions.append(func_name)
    
    def get_version(self) -> str:
        """获取版本信息"""
        return self.lib.getVersion().decode('utf-8')
    
    def add(self, a: int, b: int) -> int:
        """加法测试"""
        return self.lib.add(a, b)
    
    def print_hello(self):
        """打印Hello"""
        if 'printHello' in self.available_functions:
            self.lib.printHello()
        else:
            print("Hello from Astroboy Signal Generator!")
    
    def calculate_ma(self, prices: List[float], period: int) -> float:
        """计算移动平均线"""
        if 'calculateMA' not in self.available_functions:
            raise NotImplementedError("calculateMA函数不可用")
        
        if len(prices) < period:
            raise ValueError(f"数据长度{len(prices)}小于周期{period}")
        
        price_array = (ctypes.c_double * len(prices))(*prices)
        return self.lib.calculateMA(price_array, len(prices), period)
    
    def calculate_rsi(self, prices: List[float], period: int = 14) -> float:
        """计算RSI"""
        if 'calculateRSI' not in self.available_functions:
            raise NotImplementedError("calculateRSI函数不可用")
        
        if len(prices) < period + 1:
            raise ValueError(f"数据长度{len(prices)}不足，需要至少{period + 1}条数据")
        
        price_array = (ctypes.c_double * len(prices))(*prices)
        return self.lib.calculateRSI(price_array, len(prices), period)
    
    def generate_signal(self, open_price: float, high_price: float, low_price: float) -> int:
        """生成信号"""
        if 'generateSignal' not in self.available_functions:
            raise NotImplementedError("generateSignal函数不可用")
        
        return self.lib.generateSignal(open_price, high_price, low_price)
    
    def get_signal_name(self, signal_code: int) -> str:
        """获取信号名称"""
        signal_names = {0: "NONE", 1: "BUY", 2: "SELL", 3: "HOLD"}
        return signal_names.get(signal_code, "UNKNOWN")
    
    def get_available_functions(self) -> List[str]:
        """获取可用函数列表"""
        return self.available_functions.copy()
    
    def analyze_market(self, prices: List[float], periods: List[int] = None) -> Dict[str, Any]:
        """市场分析"""
        if periods is None:
            periods = [5, 10, 20, 50]
        
        results = {
            'prices_count': len(prices),
            'latest_price': prices[-1] if prices else 0,
            'ma_results': {},
            'rsi': None,
            'signal': None,
            'available_functions': self.available_functions
        }
        
        # 计算多周期MA
        if 'calculateMA' in self.available_functions:
            for period in periods:
                if len(prices) >= period:
                    try:
                        ma_value = self.calculate_ma(prices, period)
                        results['ma_results'][f'MA{period}'] = ma_value
                    except Exception as e:
                        results['ma_results'][f'MA{period}'] = f"计算失败: {str(e)}"
        
        # 计算RSI
        if 'calculateRSI' in self.available_functions and len(prices) >= 15:
            try:
                results['rsi'] = self.calculate_rsi(prices, 14)
            except Exception as e:
                results['rsi'] = f"计算失败: {str(e)}"
        
        # 生成信号
        if 'generateSignal' in self.available_functions and len(prices) >= 3:
            try:
                # 使用最近3天的数据生成信号
                recent_prices = prices[-3:]
                open_p = recent_prices[0]
                high_p = max(recent_prices)
                low_p = min(recent_prices)
                
                signal_code = self.generate_signal(open_p, high_p, low_p)
                results['signal'] = {
                    'code': signal_code,
                    'name': self.get_signal_name(signal_code),
                    'input': {'open': open_p, 'high': high_p, 'low': low_p}
                }
            except Exception as e:
                results['signal'] = f"计算失败: {str(e)}"
        
        return results
    
    def batch_calculate_ma(self, prices: List[float], periods: List[int]) -> Dict[int, float]:
        """批量计算移动平均线"""
        results = {}
        
        if 'calculateMA' not in self.available_functions:
            return results
        
        for period in periods:
            if len(prices) >= period:
                try:
                    ma_value = self.calculate_ma(prices, period)
                    results[period] = ma_value
                except Exception as e:
                    results[period] = None
        
        return results

# 兼容性别名
AtomBoySignalGenerator = AstroboySignalGenerator

# 便捷函数
def create_generator(dll_path: Optional[str] = None) -> AstroboySignalGenerator:
    """创建信号生成器实例"""
    return AstroboySignalGenerator(dll_path)

def quick_analysis(prices: List[float]) -> Dict[str, Any]:
    """快速市场分析"""
    generator = create_generator()
    return generator.analyze_market(prices)
