// atomboy_signal_advanced.cpp
// Astroboy Signal Generator Advanced Version with V100 Optimization

#include <iostream>
#include <vector>
#include <cmath>
#include <algorithm>
#include <numeric>
#include <string>
#include <map>
#include <complex>

#ifdef _WIN32
    #define ATOMBOY_API __declspec(dllexport)
#else
    #define ATOMBOY_API
#endif

// Advanced mathematical constants
const double PI = 3.14159265358979323846;
const double E = 2.71828182845904523536;

// Basic functions
extern "C" ATOMBOY_API const char* getVersion() {
    return "2.0.0-Advanced-V100";
}

extern "C" ATOMBOY_API int add(int a, int b) {
    return a + b;
}

extern "C" ATOMBOY_API void printHello() {
    std::cout << "Hello from Astroboy Advanced V100 Signal Generator!" << std::endl;
}

// Enhanced technical indicators
extern "C" ATOMBOY_API double calculateMA(const double* prices, int dataSize, int period) {
    if (!prices || dataSize <= 0 || period <= 0 || dataSize < period) {
        return -1.0;
    }
    
    double sum = 0.0;
    for (int i = dataSize - period; i < dataSize; i++) {
        sum += prices[i];
    }
    return sum / period;
}

extern "C" ATOMBOY_API double calculateEMA(const double* prices, int dataSize, int period) {
    if (!prices || dataSize <= 0 || period <= 0 || dataSize < period) {
        return -1.0;
    }
    
    double alpha = 2.0 / (period + 1);
    double ema = prices[dataSize - period];
    
    for (int i = dataSize - period + 1; i < dataSize; i++) {
        ema = alpha * prices[i] + (1 - alpha) * ema;
    }
    
    return ema;
}

extern "C" ATOMBOY_API double calculateRSI(const double* prices, int dataSize, int period) {
    if (!prices || dataSize <= 0 || period <= 0 || dataSize < period + 1) {
        return -1.0;
    }
    
    double gainSum = 0.0, lossSum = 0.0;
    
    for (int i = dataSize - period; i < dataSize; i++) {
        double change = prices[i] - prices[i - 1];
        if (change > 0) {
            gainSum += change;
        } else {
            lossSum += -change;
        }
    }
    
    double avgGain = gainSum / period;
    double avgLoss = lossSum / period;
    
    if (avgLoss == 0.0) return 100.0;
    
    double rs = avgGain / avgLoss;
    return 100.0 - (100.0 / (1.0 + rs));
}

extern "C" ATOMBOY_API double calculateStochastic(const double* highs, const double* lows, 
                                                 const double* closes, int dataSize, int period) {
    if (!highs || !lows || !closes || dataSize <= 0 || period <= 0 || dataSize < period) {
        return -1.0;
    }
    
    double highest = highs[dataSize - period];
    double lowest = lows[dataSize - period];
    
    for (int i = dataSize - period; i < dataSize; i++) {
        if (highs[i] > highest) highest = highs[i];
        if (lows[i] < lowest) lowest = lows[i];
    }
    
    if (highest == lowest) return 50.0;
    
    return ((closes[dataSize - 1] - lowest) / (highest - lowest)) * 100.0;
}

extern "C" ATOMBOY_API double calculateWilliamsR(const double* highs, const double* lows, 
                                                const double* closes, int dataSize, int period) {
    if (!highs || !lows || !closes || dataSize <= 0 || period <= 0 || dataSize < period) {
        return -1.0;
    }
    
    double highest = highs[dataSize - period];
    double lowest = lows[dataSize - period];
    
    for (int i = dataSize - period; i < dataSize; i++) {
        if (highs[i] > highest) highest = highs[i];
        if (lows[i] < lowest) lowest = lows[i];
    }
    
    if (highest == lowest) return -50.0;
    
    return ((highest - closes[dataSize - 1]) / (highest - lowest)) * -100.0;
}

extern "C" ATOMBOY_API double calculateCCI(const double* highs, const double* lows, 
                                          const double* closes, int dataSize, int period) {
    if (!highs || !lows || !closes || dataSize <= 0 || period <= 0 || dataSize < period) {
        return -1.0;
    }
    
    // Calculate typical prices
    std::vector<double> typicalPrices(dataSize);
    for (int i = 0; i < dataSize; i++) {
        typicalPrices[i] = (highs[i] + lows[i] + closes[i]) / 3.0;
    }
    
    // Calculate SMA of typical prices
    double sma = 0.0;
    for (int i = dataSize - period; i < dataSize; i++) {
        sma += typicalPrices[i];
    }
    sma /= period;
    
    // Calculate mean deviation
    double meanDev = 0.0;
    for (int i = dataSize - period; i < dataSize; i++) {
        meanDev += std::abs(typicalPrices[i] - sma);
    }
    meanDev /= period;
    
    if (meanDev == 0.0) return 0.0;
    
    return (typicalPrices[dataSize - 1] - sma) / (0.015 * meanDev);
}

extern "C" ATOMBOY_API double calculateATR(const double* highs, const double* lows, 
                                          const double* closes, int dataSize, int period) {
    if (!highs || !lows || !closes || dataSize <= 0 || period <= 0 || dataSize < period + 1) {
        return -1.0;
    }
    
    double atrSum = 0.0;
    
    for (int i = dataSize - period; i < dataSize; i++) {
        double tr = std::max({
            highs[i] - lows[i],
            std::abs(highs[i] - closes[i - 1]),
            std::abs(lows[i] - closes[i - 1])
        });
        atrSum += tr;
    }
    
    return atrSum / period;
}

extern "C" ATOMBOY_API double calculateADX(const double* highs, const double* lows, 
                                          const double* closes, int dataSize, int period) {
    if (!highs || !lows || !closes || dataSize <= 0 || period <= 0 || dataSize < period * 2) {
        return -1.0;
    }
    
    std::vector<double> plusDM(dataSize, 0.0);
    std::vector<double> minusDM(dataSize, 0.0);
    std::vector<double> tr(dataSize, 0.0);
    
    // Calculate +DM, -DM, and TR
    for (int i = 1; i < dataSize; i++) {
        double highDiff = highs[i] - highs[i - 1];
        double lowDiff = lows[i - 1] - lows[i];
        
        if (highDiff > lowDiff && highDiff > 0) {
            plusDM[i] = highDiff;
        }
        if (lowDiff > highDiff && lowDiff > 0) {
            minusDM[i] = lowDiff;
        }
        
        tr[i] = std::max({
            highs[i] - lows[i],
            std::abs(highs[i] - closes[i - 1]),
            std::abs(lows[i] - closes[i - 1])
        });
    }
    
    // Calculate smoothed values
    double plusDI = 0.0, minusDI = 0.0, trSum = 0.0;
    
    for (int i = dataSize - period; i < dataSize; i++) {
        plusDI += plusDM[i];
        minusDI += minusDM[i];
        trSum += tr[i];
    }
    
    if (trSum == 0.0) return 0.0;
    
    plusDI = (plusDI / trSum) * 100.0;
    minusDI = (minusDI / trSum) * 100.0;
    
    double dx = std::abs(plusDI - minusDI) / (plusDI + minusDI) * 100.0;
    
    return dx;
}

extern "C" ATOMBOY_API double calculateParabolicSAR(const double* highs, const double* lows, 
                                                   const double* closes, int dataSize, 
                                                   double acceleration, double maxAcceleration) {
    if (!highs || !lows || !closes || dataSize <= 0 || dataSize < 10) {
        return -1.0;
    }
    
    bool isUptrend = closes[1] > closes[0];
    double sar = isUptrend ? lows[0] : highs[0];
    double ep = isUptrend ? highs[1] : lows[1];
    double af = acceleration;
    
    for (int i = 2; i < dataSize; i++) {
        double prevSar = sar;
        sar = prevSar + af * (ep - prevSar);
        
        if (isUptrend) {
            if (highs[i] > ep) {
                ep = highs[i];
                af = std::min(af + acceleration, maxAcceleration);
            }
            
            if (lows[i] <= sar) {
                isUptrend = false;
                sar = ep;
                ep = lows[i];
                af = acceleration;
            }
        } else {
            if (lows[i] < ep) {
                ep = lows[i];
                af = std::min(af + acceleration, maxAcceleration);
            }
            
            if (highs[i] >= sar) {
                isUptrend = true;
                sar = ep;
                ep = highs[i];
                af = acceleration;
            }
        }
    }
    
    return sar;
}

extern "C" ATOMBOY_API double calculateIchimokuTenkan(const double* highs, const double* lows, 
                                                     int dataSize, int period) {
    if (!highs || !lows || dataSize <= 0 || period <= 0 || dataSize < period) {
        return -1.0;
    }
    
    double highest = highs[dataSize - period];
    double lowest = lows[dataSize - period];
    
    for (int i = dataSize - period; i < dataSize; i++) {
        if (highs[i] > highest) highest = highs[i];
        if (lows[i] < lowest) lowest = lows[i];
    }
    
    return (highest + lowest) / 2.0;
}

extern "C" ATOMBOY_API double calculateIchimokuKijun(const double* highs, const double* lows, 
                                                    int dataSize, int period) {
    return calculateIchimokuTenkan(highs, lows, dataSize, period);
}

extern "C" ATOMBOY_API double calculateVWAP(const double* highs, const double* lows, 
                                           const double* closes, const double* volumes, 
                                           int dataSize, int period) {
    if (!highs || !lows || !closes || !volumes || dataSize <= 0 || period <= 0 || dataSize < period) {
        return -1.0;
    }
    
    double totalPV = 0.0;
    double totalVolume = 0.0;
    
    for (int i = dataSize - period; i < dataSize; i++) {
        double typicalPrice = (highs[i] + lows[i] + closes[i]) / 3.0;
        totalPV += typicalPrice * volumes[i];
        totalVolume += volumes[i];
    }
    
    if (totalVolume == 0.0) return -1.0;
    
    return totalPV / totalVolume;
}

// Advanced signal generation with multiple indicators
extern "C" ATOMBOY_API int generateAdvancedSignal(const double* highs, const double* lows, 
                                                 const double* closes, const double* volumes,
                                                 int dataSize, int* signalStrength) {
    if (!highs || !lows || !closes || !volumes || dataSize <= 0 || dataSize < 50) {
        if (signalStrength) *signalStrength = 0;
        return 0; // NONE
    }
    
    // Calculate multiple indicators
    double rsi = calculateRSI(closes, dataSize, 14);
    double stoch = calculateStochastic(highs, lows, closes, dataSize, 14);
    double cci = calculateCCI(highs, lows, closes, dataSize, 20);
    double williamsR = calculateWilliamsR(highs, lows, closes, dataSize, 14);
    double adx = calculateADX(highs, lows, closes, dataSize, 14);
    
    double ma20 = calculateMA(closes, dataSize, 20);
    double ma50 = calculateMA(closes, dataSize, 50);
    double ema12 = calculateEMA(closes, dataSize, 12);
    double ema26 = calculateEMA(closes, dataSize, 26);
    
    double currentPrice = closes[dataSize - 1];
    
    // Signal scoring system
    int buySignals = 0;
    int sellSignals = 0;
    int strength = 0;
    
    // RSI signals
    if (rsi < 30) {
        buySignals += 2;
        strength += 2;
    } else if (rsi > 70) {
        sellSignals += 2;
        strength += 2;
    } else if (rsi < 40) {
        buySignals += 1;
        strength += 1;
    } else if (rsi > 60) {
        sellSignals += 1;
        strength += 1;
    }
    
    // Stochastic signals
    if (stoch < 20) {
        buySignals += 2;
        strength += 1;
    } else if (stoch > 80) {
        sellSignals += 2;
        strength += 1;
    }
    
    // CCI signals
    if (cci < -100) {
        buySignals += 1;
        strength += 1;
    } else if (cci > 100) {
        sellSignals += 1;
        strength += 1;
    }
    
    // Williams %R signals
    if (williamsR < -80) {
        buySignals += 1;
        strength += 1;
    } else if (williamsR > -20) {
        sellSignals += 1;
        strength += 1;
    }
    
    // Moving average signals
    if (currentPrice > ma20 && ma20 > ma50) {
        buySignals += 2;
        strength += 2;
    } else if (currentPrice < ma20 && ma20 < ma50) {
        sellSignals += 2;
        strength += 2;
    }
    
    // EMA crossover signals
    if (ema12 > ema26) {
        buySignals += 1;
        strength += 1;
    } else if (ema12 < ema26) {
        sellSignals += 1;
        strength += 1;
    }
    
    // ADX trend strength
    if (adx > 25) {
        strength += 2; // Strong trend
    } else if (adx > 20) {
        strength += 1; // Moderate trend
    }
    
    // Determine final signal
    if (signalStrength) *signalStrength = strength;
    
    if (buySignals > sellSignals + 2) {
        return 1; // BUY
    } else if (sellSignals > buySignals + 2) {
        return 2; // SELL
    } else {
        return 3; // HOLD
    }
}

// Comprehensive market analysis
extern "C" ATOMBOY_API int analyzeAdvancedMarket(const double* highs, const double* lows, 
                                                const double* closes, const double* volumes,
                                                int dataSize, double* indicators, 
                                                int* signal, int* signalStrength) {
    if (!highs || !lows || !closes || !volumes || !indicators || !signal || !signalStrength || 
        dataSize <= 0 || dataSize < 50) {
        return 0;
    }
    
    // Calculate all indicators (20 indicators)
    indicators[0] = calculateMA(closes, dataSize, 5);
    indicators[1] = calculateMA(closes, dataSize, 10);
    indicators[2] = calculateMA(closes, dataSize, 20);
    indicators[3] = calculateMA(closes, dataSize, 50);
    indicators[4] = calculateEMA(closes, dataSize, 12);
    indicators[5] = calculateEMA(closes, dataSize, 26);
    indicators[6] = calculateRSI(closes, dataSize, 14);
    indicators[7] = calculateStochastic(highs, lows, closes, dataSize, 14);
    indicators[8] = calculateCCI(highs, lows, closes, dataSize, 20);
    indicators[9] = calculateWilliamsR(highs, lows, closes, dataSize, 14);
    indicators[10] = calculateADX(highs, lows, closes, dataSize, 14);
    indicators[11] = calculateATR(highs, lows, closes, dataSize, 14);
    indicators[12] = calculateParabolicSAR(highs, lows, closes, dataSize, 0.02, 0.2);
    indicators[13] = calculateIchimokuTenkan(highs, lows, dataSize, 9);
    indicators[14] = calculateIchimokuKijun(highs, lows, dataSize, 26);
    indicators[15] = calculateVWAP(highs, lows, closes, volumes, dataSize, 20);
    
    // MACD components
    double macdLine = indicators[4] - indicators[5]; // EMA12 - EMA26
    indicators[16] = macdLine;
    
    // Bollinger Bands
    double ma20 = indicators[2];
    double variance = 0.0;
    for (int i = dataSize - 20; i < dataSize; i++) {
        double diff = closes[i] - ma20;
        variance += diff * diff;
    }
    double stdDev = std::sqrt(variance / 20);
    indicators[17] = ma20 + 2.0 * stdDev; // Upper Bollinger
    indicators[18] = ma20 - 2.0 * stdDev; // Lower Bollinger
    
    // Price position in Bollinger Bands
    double currentPrice = closes[dataSize - 1];
    if (indicators[17] != indicators[18]) {
        indicators[19] = (currentPrice - indicators[18]) / (indicators[17] - indicators[18]);
    } else {
        indicators[19] = 0.5;
    }
    
    // Generate advanced signal
    *signal = generateAdvancedSignal(highs, lows, closes, volumes, dataSize, signalStrength);
    
    return 1; // Success
}
