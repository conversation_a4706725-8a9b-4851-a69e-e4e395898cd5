/**
 * @file atomboy_signal_fixed.cpp
 * @brief 阿童木信号生成器修复版本
 * <AUTHOR>
 */

#include <iostream>
#include <vector>
#include <cmath>
#include <algorithm>
#include <numeric>
#include <string>
#include <mutex>
#include <memory>
#include <chrono>

#ifdef _WIN32
    #define ATOMBOY_API __declspec(dllexport)
#else
    #define ATOMBOY_API
#endif

// 全局数据存储
static std::vector<double> g_priceHistory;
static std::mutex g_dataMutex;

// 错误处理
enum ErrorCode {
    SUCCESS = 0,
    INVALID_ARGUMENT = 1,
    INSUFFICIENT_DATA = 2,
    GENERAL = 3
};

static ErrorCode g_lastError = SUCCESS;
static std::string g_lastErrorMessage;

void setError(ErrorCode code, const std::string& message) {
    g_lastError = code;
    g_lastErrorMessage = message;
}

// 基本功能
extern "C" ATOMBOY_API const char* getVersion() {
    return "1.1.0-Fixed";
}

extern "C" ATOMBOY_API int add(int a, int b) {
    return a + b;
}

extern "C" ATOMBOY_API void printHello() {
    std::cout << "Hello from Astroboy Signal Generator Fixed Version!" << std::endl;
}

// 数据管理
extern "C" ATOMBOY_API void addPriceData(double price) {
    std::lock_guard<std::mutex> lock(g_dataMutex);
    g_priceHistory.push_back(price);
    
    // 保持最近1000条数据
    if (g_priceHistory.size() > 1000) {
        g_priceHistory.erase(g_priceHistory.begin());
    }
}

extern "C" ATOMBOY_API void clearPriceData() {
    std::lock_guard<std::mutex> lock(g_dataMutex);
    g_priceHistory.clear();
}

extern "C" ATOMBOY_API int getPriceDataCount() {
    std::lock_guard<std::mutex> lock(g_dataMutex);
    return static_cast<int>(g_priceHistory.size());
}

// 技术指标计算 - 使用外部数据
extern "C" ATOMBOY_API double calculateMA(const double* prices, int dataSize, int period) {
    try {
        if (!prices || dataSize <= 0 || period <= 0) {
            setError(INVALID_ARGUMENT, "Invalid parameters");
            return -1.0;
        }
        
        if (dataSize < period) {
            setError(INSUFFICIENT_DATA, "Insufficient data for calculation");
            return -1.0;
        }
        
        // 计算最后period个数据的移动平均
        double sum = 0.0;
        for (int i = dataSize - period; i < dataSize; i++) {
            sum += prices[i];
        }
        
        return sum / period;
        
    } catch (const std::exception& e) {
        setError(GENERAL, std::string("Failed to calculate MA: ") + e.what());
        return -1.0;
    }
}

extern "C" ATOMBOY_API double calculateRSI(const double* prices, int dataSize, int period) {
    try {
        if (!prices || dataSize <= 0 || period <= 0) {
            setError(INVALID_ARGUMENT, "Invalid parameters");
            return -1.0;
        }

        if (dataSize < period + 1) {
            setError(INSUFFICIENT_DATA, "Insufficient data for RSI calculation");
            return -1.0;
        }

        double gainSum = 0.0;
        double lossSum = 0.0;

        // 计算增益和损失
        for (int i = dataSize - period; i < dataSize; i++) {
            double change = prices[i] - prices[i - 1];
            if (change > 0) {
                gainSum += change;
            } else {
                lossSum += -change;
            }
        }

        double avgGain = gainSum / period;
        double avgLoss = lossSum / period;

        if (avgLoss == 0.0) {
            return 100.0;
        }

        double rs = avgGain / avgLoss;
        return 100.0 - (100.0 / (1.0 + rs));

    } catch (const std::exception& e) {
        setError(GENERAL, std::string("Failed to calculate RSI: ") + e.what());
        return -1.0;
    }
}

extern "C" ATOMBOY_API double calculateMACD(const double* prices, int dataSize, 
                                           int fastPeriod, int slowPeriod, int signalPeriod) {
    try {
        if (!prices || dataSize <= 0 || fastPeriod <= 0 || slowPeriod <= 0 || signalPeriod <= 0) {
            setError(INVALID_ARGUMENT, "Invalid parameters");
            return -1.0;
        }
        
        if (dataSize < slowPeriod) {
            setError(INSUFFICIENT_DATA, "Insufficient data for MACD calculation");
            return -1.0;
        }
        
        // 简化的MACD计算 - 返回DIF值
        double fastMA = 0.0, slowMA = 0.0;
        
        // 计算快速MA
        for (int i = dataSize - fastPeriod; i < dataSize; i++) {
            fastMA += prices[i];
        }
        fastMA /= fastPeriod;
        
        // 计算慢速MA
        for (int i = dataSize - slowPeriod; i < dataSize; i++) {
            slowMA += prices[i];
        }
        slowMA /= slowPeriod;
        
        return fastMA - slowMA;  // DIF值
        
    } catch (const std::exception& e) {
        setError(GENERAL, std::string("Failed to calculate MACD: ") + e.what());
        return -1.0;
    }
}

extern "C" ATOMBOY_API double calculateBollingerUpper(const double* prices, int dataSize, 
                                                     int period, double stdMultiplier) {
    try {
        if (!prices || dataSize <= 0 || period <= 0 || stdMultiplier <= 0) {
            setError(INVALID_ARGUMENT, "Invalid parameters");
            return -1.0;
        }
        
        if (dataSize < period) {
            setError(INSUFFICIENT_DATA, "Insufficient data for Bollinger calculation");
            return -1.0;
        }
        
        // 计算移动平均
        double sum = 0.0;
        for (int i = dataSize - period; i < dataSize; i++) {
            sum += prices[i];
        }
        double ma = sum / period;
        
        // 计算标准差
        double variance = 0.0;
        for (int i = dataSize - period; i < dataSize; i++) {
            double diff = prices[i] - ma;
            variance += diff * diff;
        }
        double stdDev = std::sqrt(variance / period);
        
        return ma + stdMultiplier * stdDev;
        
    } catch (const std::exception& e) {
        setError(GENERAL, std::string("Failed to calculate Bollinger Upper: ") + e.what());
        return -1.0;
    }
}

extern "C" ATOMBOY_API double calculateBollingerLower(const double* prices, int dataSize, 
                                                     int period, double stdMultiplier) {
    try {
        if (!prices || dataSize <= 0 || period <= 0 || stdMultiplier <= 0) {
            setError(INVALID_ARGUMENT, "Invalid parameters");
            return -1.0;
        }
        
        if (dataSize < period) {
            setError(INSUFFICIENT_DATA, "Insufficient data for Bollinger calculation");
            return -1.0;
        }
        
        // 计算移动平均
        double sum = 0.0;
        for (int i = dataSize - period; i < dataSize; i++) {
            sum += prices[i];
        }
        double ma = sum / period;
        
        // 计算标准差
        double variance = 0.0;
        for (int i = dataSize - period; i < dataSize; i++) {
            double diff = prices[i] - ma;
            variance += diff * diff;
        }
        double stdDev = std::sqrt(variance / period);
        
        return ma - stdMultiplier * stdDev;
        
    } catch (const std::exception& e) {
        setError(GENERAL, std::string("Failed to calculate Bollinger Lower: ") + e.what());
        return -1.0;
    }
}

// 信号生成
extern "C" ATOMBOY_API int generateSignal(double openPrice, double highPrice, double lowPrice) {
    try {
        if (openPrice <= 0 || highPrice <= 0 || lowPrice <= 0) {
            setError(INVALID_ARGUMENT, "Invalid price data");
            return 0;  // NONE
        }
        
        if (highPrice < lowPrice || openPrice < lowPrice || openPrice > highPrice) {
            setError(INVALID_ARGUMENT, "Inconsistent price data");
            return 0;  // NONE
        }
        
        // 简单的信号生成逻辑
        double range = highPrice - lowPrice;
        double openPosition = (openPrice - lowPrice) / range;
        
        if (range / openPrice > 0.05) {  // 大幅波动
            if (openPosition > 0.7) {
                return 2;  // SELL - 开盘价接近高点
            } else if (openPosition < 0.3) {
                return 1;  // BUY - 开盘价接近低点
            }
        } else if (range / openPrice > 0.02) {  // 中等波动
            if (openPosition > 0.8) {
                return 2;  // SELL
            } else if (openPosition < 0.2) {
                return 1;  // BUY
            }
        }
        
        return 3;  // HOLD
        
    } catch (const std::exception& e) {
        setError(GENERAL, std::string("Failed to generate signal: ") + e.what());
        return 0;  // NONE
    }
}

// 批量计算
extern "C" ATOMBOY_API int calculateMultipleMA(const double* prices, int dataSize, 
                                               const int* periods, int periodCount, 
                                               double* results) {
    try {
        if (!prices || !periods || !results || dataSize <= 0 || periodCount <= 0) {
            setError(INVALID_ARGUMENT, "Invalid parameters");
            return 0;
        }
        
        int successCount = 0;
        
        for (int i = 0; i < periodCount; i++) {
            if (periods[i] > 0 && dataSize >= periods[i]) {
                results[i] = calculateMA(prices, dataSize, periods[i]);
                if (results[i] != -1.0) {
                    successCount++;
                }
            } else {
                results[i] = -1.0;
            }
        }
        
        return successCount;
        
    } catch (const std::exception& e) {
        setError(GENERAL, std::string("Failed to calculate multiple MA: ") + e.what());
        return 0;
    }
}

// 市场分析
extern "C" ATOMBOY_API int analyzeMarket(const double* prices, int dataSize, 
                                         double* maResults, double* rsi, int* signal) {
    try {
        if (!prices || !maResults || !rsi || !signal || dataSize <= 0) {
            setError(INVALID_ARGUMENT, "Invalid parameters");
            return 0;
        }
        
        // 计算多周期MA
        int periods[] = {5, 10, 20, 50};
        int periodCount = 4;
        
        for (int i = 0; i < periodCount; i++) {
            if (dataSize >= periods[i]) {
                maResults[i] = calculateMA(prices, dataSize, periods[i]);
            } else {
                maResults[i] = -1.0;
            }
        }
        
        // 计算RSI
        if (dataSize >= 15) {
            *rsi = calculateRSI(prices, dataSize, 14);
        } else {
            *rsi = -1.0;
        }
        
        // 生成信号
        if (dataSize >= 3) {
            double openPrice = prices[dataSize - 3];
            double highPrice = *std::max_element(prices + dataSize - 3, prices + dataSize);
            double lowPrice = *std::min_element(prices + dataSize - 3, prices + dataSize);
            
            *signal = generateSignal(openPrice, highPrice, lowPrice);
        } else {
            *signal = 0;  // NONE
        }
        
        return 1;  // Success
        
    } catch (const std::exception& e) {
        setError(GENERAL, std::string("Failed to analyze market: ") + e.what());
        return 0;
    }
}

// 错误处理
extern "C" ATOMBOY_API int getLastError() {
    return static_cast<int>(g_lastError);
}

extern "C" ATOMBOY_API const char* getLastErrorMessage() {
    return g_lastErrorMessage.c_str();
}

extern "C" ATOMBOY_API void clearError() {
    g_lastError = SUCCESS;
    g_lastErrorMessage.clear();
}

// 性能测试
extern "C" ATOMBOY_API double performanceTest(int iterations) {
    try {
        auto start = std::chrono::high_resolution_clock::now();
        
        // 生成测试数据
        std::vector<double> testPrices(1000);
        for (int i = 0; i < 1000; i++) {
            testPrices[i] = 100.0 + std::sin(i * 0.1) * 10.0;
        }
        
        // 执行多次计算
        for (int i = 0; i < iterations; i++) {
            calculateMA(testPrices.data(), testPrices.size(), 20);
            calculateRSI(testPrices.data(), testPrices.size(), 14);
            generateSignal(testPrices[997], testPrices[998], testPrices[999]);
        }
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        return duration.count() / 1000.0;  // 返回毫秒
        
    } catch (const std::exception& e) {
        setError(GENERAL, std::string("Performance test failed: ") + e.what());
        return -1.0;
    }
}
