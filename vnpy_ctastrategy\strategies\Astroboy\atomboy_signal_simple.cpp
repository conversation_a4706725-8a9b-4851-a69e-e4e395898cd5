// atomboy_signal_simple.cpp
// Astroboy Signal Generator Simple Version

#include <iostream>
#include <vector>
#include <cmath>
#include <algorithm>
#include <numeric>
#include <string>

#ifdef _WIN32
    #define ATOMBOY_API __declspec(dllexport)
#else
    #define ATOMBOY_API
#endif

// Basic functions
extern "C" ATOMBOY_API const char* getVersion() {
    return "1.2.0-Simple";
}

extern "C" ATOMBOY_API int add(int a, int b) {
    return a + b;
}

extern "C" ATOMBOY_API void printHello() {
    std::cout << "Hello from Astroboy Signal Generator Simple Version!" << std::endl;
}

// Technical indicators
extern "C" ATOMBOY_API double calculateMA(const double* prices, int dataSize, int period) {
    if (!prices || dataSize <= 0 || period <= 0) {
        return -1.0;
    }
    
    if (dataSize < period) {
        return -1.0;
    }
    
    double sum = 0.0;
    for (int i = dataSize - period; i < dataSize; i++) {
        sum += prices[i];
    }
    
    return sum / period;
}

extern "C" ATOMBOY_API double calculateRSI(const double* prices, int dataSize, int period) {
    if (!prices || dataSize <= 0 || period <= 0) {
        return -1.0;
    }
    
    if (dataSize < period + 1) {
        return -1.0;
    }
    
    double gainSum = 0.0;
    double lossSum = 0.0;
    
    for (int i = dataSize - period; i < dataSize; i++) {
        double change = prices[i] - prices[i - 1];
        if (change > 0) {
            gainSum += change;
        } else {
            lossSum += -change;
        }
    }
    
    double avgGain = gainSum / period;
    double avgLoss = lossSum / period;
    
    if (avgLoss == 0.0) {
        return 100.0;
    }
    
    double rs = avgGain / avgLoss;
    return 100.0 - (100.0 / (1.0 + rs));
}

extern "C" ATOMBOY_API double calculateMACD(const double* prices, int dataSize, 
                                           int fastPeriod, int slowPeriod, int signalPeriod) {
    if (!prices || dataSize <= 0 || fastPeriod <= 0 || slowPeriod <= 0 || signalPeriod <= 0) {
        return -1.0;
    }
    
    if (dataSize < slowPeriod) {
        return -1.0;
    }
    
    double fastMA = 0.0;
    double slowMA = 0.0;
    
    // Calculate fast MA
    for (int i = dataSize - fastPeriod; i < dataSize; i++) {
        fastMA += prices[i];
    }
    fastMA /= fastPeriod;
    
    // Calculate slow MA
    for (int i = dataSize - slowPeriod; i < dataSize; i++) {
        slowMA += prices[i];
    }
    slowMA /= slowPeriod;
    
    return fastMA - slowMA;  // DIF value
}

extern "C" ATOMBOY_API double calculateBollingerUpper(const double* prices, int dataSize, 
                                                     int period, double stdMultiplier) {
    if (!prices || dataSize <= 0 || period <= 0 || stdMultiplier <= 0) {
        return -1.0;
    }
    
    if (dataSize < period) {
        return -1.0;
    }
    
    // Calculate moving average
    double sum = 0.0;
    for (int i = dataSize - period; i < dataSize; i++) {
        sum += prices[i];
    }
    double ma = sum / period;
    
    // Calculate standard deviation
    double variance = 0.0;
    for (int i = dataSize - period; i < dataSize; i++) {
        double diff = prices[i] - ma;
        variance += diff * diff;
    }
    double stdDev = std::sqrt(variance / period);
    
    return ma + stdMultiplier * stdDev;
}

extern "C" ATOMBOY_API double calculateBollingerLower(const double* prices, int dataSize, 
                                                     int period, double stdMultiplier) {
    if (!prices || dataSize <= 0 || period <= 0 || stdMultiplier <= 0) {
        return -1.0;
    }
    
    if (dataSize < period) {
        return -1.0;
    }
    
    // Calculate moving average
    double sum = 0.0;
    for (int i = dataSize - period; i < dataSize; i++) {
        sum += prices[i];
    }
    double ma = sum / period;
    
    // Calculate standard deviation
    double variance = 0.0;
    for (int i = dataSize - period; i < dataSize; i++) {
        double diff = prices[i] - ma;
        variance += diff * diff;
    }
    double stdDev = std::sqrt(variance / period);
    
    return ma - stdMultiplier * stdDev;
}

// Signal generation
extern "C" ATOMBOY_API int generateSignal(double openPrice, double highPrice, double lowPrice) {
    if (openPrice <= 0 || highPrice <= 0 || lowPrice <= 0) {
        return 0;  // NONE
    }
    
    if (highPrice < lowPrice || openPrice < lowPrice || openPrice > highPrice) {
        return 0;  // NONE
    }
    
    double range = highPrice - lowPrice;
    if (range == 0) {
        return 3;  // HOLD
    }
    
    double openPosition = (openPrice - lowPrice) / range;
    
    if (range / openPrice > 0.05) {  // Large volatility
        if (openPosition > 0.7) {
            return 2;  // SELL
        } else if (openPosition < 0.3) {
            return 1;  // BUY
        }
    } else if (range / openPrice > 0.02) {  // Medium volatility
        if (openPosition > 0.8) {
            return 2;  // SELL
        } else if (openPosition < 0.2) {
            return 1;  // BUY
        }
    }
    
    return 3;  // HOLD
}

// Batch calculations
extern "C" ATOMBOY_API int calculateMultipleMA(const double* prices, int dataSize, 
                                               const int* periods, int periodCount, 
                                               double* results) {
    if (!prices || !periods || !results || dataSize <= 0 || periodCount <= 0) {
        return 0;
    }
    
    int successCount = 0;
    
    for (int i = 0; i < periodCount; i++) {
        if (periods[i] > 0 && dataSize >= periods[i]) {
            results[i] = calculateMA(prices, dataSize, periods[i]);
            if (results[i] != -1.0) {
                successCount++;
            }
        } else {
            results[i] = -1.0;
        }
    }
    
    return successCount;
}

// Market analysis
extern "C" ATOMBOY_API int analyzeMarket(const double* prices, int dataSize, 
                                         double* maResults, double* rsi, int* signal) {
    if (!prices || !maResults || !rsi || !signal || dataSize <= 0) {
        return 0;
    }
    
    // Calculate multiple MA
    int periods[] = {5, 10, 20, 50};
    int periodCount = 4;
    
    for (int i = 0; i < periodCount; i++) {
        if (dataSize >= periods[i]) {
            maResults[i] = calculateMA(prices, dataSize, periods[i]);
        } else {
            maResults[i] = -1.0;
        }
    }
    
    // Calculate RSI
    if (dataSize >= 15) {
        *rsi = calculateRSI(prices, dataSize, 14);
    } else {
        *rsi = -1.0;
    }
    
    // Generate signal
    if (dataSize >= 3) {
        double openPrice = prices[dataSize - 3];
        double highPrice = prices[dataSize - 3];
        double lowPrice = prices[dataSize - 3];
        
        for (int i = dataSize - 3; i < dataSize; i++) {
            if (prices[i] > highPrice) highPrice = prices[i];
            if (prices[i] < lowPrice) lowPrice = prices[i];
        }
        
        *signal = generateSignal(openPrice, highPrice, lowPrice);
    } else {
        *signal = 0;  // NONE
    }
    
    return 1;  // Success
}
