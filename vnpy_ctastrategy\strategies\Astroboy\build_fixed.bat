@echo off
REM 阿童木信号生成器修复版编译脚本
REM Astroboy Signal Generator Fixed Build Script

echo ========================================
echo 阿童木信号生成器修复版编译脚本
echo Astroboy Signal Generator Fixed Build
echo ========================================

REM 设置编译环境
set BUILD_TYPE=Release
set BUILD_DIR=build_fixed

REM 检查Visual Studio环境
echo 检查Visual Studio环境...
where cl >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 尝试设置Visual Studio环境...
    
    REM 尝试不同版本的Visual Studio
    if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
        echo 使用 Visual Studio 2022 Professional
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
        echo 使用 Visual Studio 2022 Community
    ) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat"
        echo 使用 Visual Studio 2019 Professional
    ) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
        echo 使用 Visual Studio 2019 Community
    ) else (
        echo 错误: 未找到Visual Studio环境
        echo 请安装Visual Studio 2019或2022，包含C++开发工具
        pause
        exit /b 1
    )
)

REM 再次检查编译器
where cl >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 错误: Visual Studio环境设置失败
    pause
    exit /b 1
)

echo Visual Studio编译器版本:
cl 2>&1 | findstr "版本\|Version"

REM 检查CMake
echo 检查CMake...
where cmake >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 未找到CMake
    echo 请安装CMake 3.16或更高版本
    pause
    exit /b 1
)

echo CMake版本:
cmake --version

REM 创建构建目录
echo 创建构建目录...
if exist "%BUILD_DIR%" rmdir /s /q "%BUILD_DIR%"
mkdir "%BUILD_DIR%"
cd "%BUILD_DIR%"

REM 配置CMake
echo 配置CMake...
cmake -G "Visual Studio 16 2019" -A x64 ^
    -DCMAKE_BUILD_TYPE=%BUILD_TYPE% ^
    -DASTROBOY_ENABLE_CUDA=OFF ^
    -DASTROBOY_ENABLE_TESTING=OFF ^
    -DASTROBOY_BUILD_SHARED=ON ^
    -DCMAKE_INSTALL_PREFIX="%CD%\install" ^
    -f ..\CMakeLists_Fixed.txt ^
    ..

if %ERRORLEVEL% NEQ 0 (
    echo 错误: CMake配置失败
    pause
    exit /b 1
)

REM 编译项目
echo 开始编译...
cmake --build . --config %BUILD_TYPE% --parallel 4

if %ERRORLEVEL% NEQ 0 (
    echo 错误: 编译失败
    pause
    exit /b 1
)

REM 复制DLL到主目录
echo 复制DLL文件...
if exist "bin\%BUILD_TYPE%\astroboy_fixed.dll" (
    copy "bin\%BUILD_TYPE%\astroboy_fixed.dll" "..\astroboy_fixed.dll"
    echo DLL文件已复制到主目录
) else if exist "%BUILD_TYPE%\astroboy_fixed.dll" (
    copy "%BUILD_TYPE%\astroboy_fixed.dll" "..\astroboy_fixed.dll"
    echo DLL文件已复制到主目录
) else (
    echo 警告: 未找到编译生成的DLL文件
    echo 查找所有DLL文件:
    dir /s *.dll
)

cd ..

echo ========================================
echo 编译完成!
echo ========================================
echo.
echo 编译结果:
if exist "astroboy_fixed.dll" (
    echo [✓] 修复版DLL编译成功: astroboy_fixed.dll
) else (
    echo [✗] 修复版DLL编译失败
)

echo.
echo 下一步:
echo 1. 测试DLL: python test_fixed_dll.py
echo 2. 如果测试成功，可以替换原有的atomboy_signal.dll
echo.

pause
