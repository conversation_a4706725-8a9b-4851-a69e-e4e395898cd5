@echo off
REM 阿童木V100 GPU加速信号生成器编译脚本
REM Astroboy V100 GPU Accelerated Signal Generator Build Script

echo ========================================
echo 阿童木V100 GPU加速信号生成器编译脚本
echo Astroboy V100 GPU Accelerated Build
echo ========================================

REM 设置编译环境
set BUILD_TYPE=Release
set ENABLE_CUDA=ON
set ENABLE_TESTING=ON
set ENABLE_BENCHMARKS=ON
set ENABLE_PYTHON_BINDINGS=ON

REM 检查CUDA环境
echo 检查CUDA环境...
where nvcc >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 未找到CUDA编译器 nvcc
    echo 请确保已安装CUDA Toolkit并添加到PATH
    echo 推荐版本: CUDA 11.8 或 12.x
    pause
    exit /b 1
)

REM 显示CUDA版本
echo CUDA编译器版本:
nvcc --version

REM 检查Visual Studio环境
echo 检查Visual Studio环境...
where cl >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 尝试设置Visual Studio环境...
    
    REM 尝试不同版本的Visual Studio
    if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    ) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat"
    ) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    ) else (
        echo 错误: 未找到Visual Studio环境
        echo 请安装Visual Studio 2019或2022，包含C++开发工具
        pause
        exit /b 1
    )
)

REM 再次检查编译器
where cl >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 错误: Visual Studio环境设置失败
    pause
    exit /b 1
)

echo Visual Studio编译器版本:
cl

REM 检查CMake
echo 检查CMake...
where cmake >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 未找到CMake
    echo 请安装CMake 3.18或更高版本
    pause
    exit /b 1
)

echo CMake版本:
cmake --version

REM 检查GPU设备
echo 检查GPU设备...
nvidia-smi >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo GPU设备信息:
    nvidia-smi --query-gpu=name,memory.total,compute_cap --format=csv,noheader,nounits
) else (
    echo 警告: 未检测到NVIDIA GPU或驱动
    echo V100加速功能可能不可用
)

REM 创建构建目录
echo 创建构建目录...
if not exist "build_v100" mkdir build_v100
cd build_v100

REM 清理旧的构建文件
echo 清理旧的构建文件...
if exist "CMakeCache.txt" del CMakeCache.txt
if exist "CMakeFiles" rmdir /s /q CMakeFiles

REM 配置CMake
echo 配置CMake...
cmake -G "Visual Studio 16 2019" -A x64 ^
    -DCMAKE_BUILD_TYPE=%BUILD_TYPE% ^
    -DASTROBOY_ENABLE_CUDA=%ENABLE_CUDA% ^
    -DASTROBOY_ENABLE_TESTING=%ENABLE_TESTING% ^
    -DASTROBOY_ENABLE_BENCHMARKS=%ENABLE_BENCHMARKS% ^
    -DASTROBOY_ENABLE_PYTHON_BINDINGS=%ENABLE_PYTHON_BINDINGS% ^
    -DCMAKE_CUDA_ARCHITECTURES="70;75;80;86" ^
    -DCMAKE_INSTALL_PREFIX="%CD%\install" ^
    -f ..\CMakeLists_V100.txt ^
    ..

if %ERRORLEVEL% NEQ 0 (
    echo 错误: CMake配置失败
    pause
    exit /b 1
)

REM 编译项目
echo 开始编译...
cmake --build . --config %BUILD_TYPE% --parallel 8

if %ERRORLEVEL% NEQ 0 (
    echo 错误: 编译失败
    pause
    exit /b 1
)

REM 运行测试
if "%ENABLE_TESTING%"=="ON" (
    echo 运行单元测试...
    ctest -C %BUILD_TYPE% --output-on-failure
    
    if %ERRORLEVEL% NEQ 0 (
        echo 警告: 部分测试失败
    )
)

REM 安装
echo 安装文件...
cmake --install . --config %BUILD_TYPE%

if %ERRORLEVEL% NEQ 0 (
    echo 错误: 安装失败
    pause
    exit /b 1
)

REM 复制DLL到Python目录
echo 复制DLL文件...
if exist "%BUILD_TYPE%\astroboy_v100.dll" (
    copy "%BUILD_TYPE%\astroboy_v100.dll" "..\astroboy_v100.dll"
    echo DLL文件已复制到主目录
) else if exist "bin\%BUILD_TYPE%\astroboy_v100.dll" (
    copy "bin\%BUILD_TYPE%\astroboy_v100.dll" "..\astroboy_v100.dll"
    echo DLL文件已复制到主目录
) else (
    echo 警告: 未找到编译生成的DLL文件
)

REM 运行基准测试
if "%ENABLE_BENCHMARKS%"=="ON" (
    echo 运行性能基准测试...
    if exist "%BUILD_TYPE%\astroboy_benchmark.exe" (
        "%BUILD_TYPE%\astroboy_benchmark.exe"
    ) else if exist "bin\%BUILD_TYPE%\astroboy_benchmark.exe" (
        "bin\%BUILD_TYPE%\astroboy_benchmark.exe"
    ) else (
        echo 基准测试程序未找到
    )
)

cd ..

echo ========================================
echo 编译完成!
echo ========================================
echo.
echo 编译结果:
if exist "build_v100\%BUILD_TYPE%\astroboy_v100.dll" (
    echo [✓] 主库编译成功: astroboy_v100.dll
) else (
    echo [✗] 主库编译失败
)

if exist "astroboy_v100.dll" (
    echo [✓] DLL文件已复制到主目录
) else (
    echo [✗] DLL文件复制失败
)

if exist "astroboy_v100_wrapper.py" (
    echo [✓] Python包装器已准备就绪
) else (
    echo [✗] Python包装器未找到
)

echo.
echo 使用说明:
echo 1. 确保astroboy_v100.dll在Python脚本同目录
echo 2. 导入: from astroboy_v100_wrapper import AstroboyV100SignalGenerator
echo 3. 创建实例: generator = AstroboyV100SignalGenerator()
echo 4. 检查V100状态: generator.is_v100_available()
echo.

REM 创建测试脚本
echo 创建测试脚本...
(
echo import sys
echo import os
echo import numpy as np
echo from astroboy_v100_wrapper import AstroboyV100SignalGenerator, GPUPriceData
echo.
echo def test_v100_performance():
echo     """测试V100性能"""
echo     print("=== 阿童木V100性能测试 ==="^)
echo     
echo     # 创建信号生成器
echo     generator = AstroboyV100SignalGenerator()
echo     
echo     print(f"版本: {generator.get_version()}"^)
echo     print(f"GPU可用: {generator.is_gpu_enabled()}"^)
echo     print(f"V100可用: {generator.is_v100_available()}"^)
echo     print(f"GPU信息:\n{generator.get_gpu_info()}"^)
echo     
echo     # 生成测试数据
echo     print("\n生成测试数据..."^)
echo     np.random.seed(42^)
echo     data_size = 1000
echo     
echo     price_data = []
echo     base_price = 100.0
echo     
echo     for i in range(data_size^):
echo         change = np.random.normal(0, 0.02^)
echo         base_price *= (1 + change^)
echo         
echo         high = base_price * (1 + abs(np.random.normal(0, 0.01^)^)^)
echo         low = base_price * (1 - abs(np.random.normal(0, 0.01^)^)^)
echo         volume = np.random.uniform(1000, 10000^)
echo         
echo         price_data.append(GPUPriceData(
echo             base_price, high, low, base_price, volume, i
echo         ^)^)
echo     
echo     print(f"测试数据生成完成: {len(price_data^)}条"^)
echo     
echo     # 性能基准测试
echo     print("\n开始性能基准测试..."^)
echo     benchmark_results = generator.benchmark_performance(price_data, iterations=5^)
echo     
echo     print("\n=== 基准测试结果 ==="^)
echo     for key, value in benchmark_results.items():
echo         print(f"{key}: {value}"^)
echo     
echo     # 计算技术指标
echo     print("\n计算技术指标..."^)
echo     results = generator.calculate_all_indicators(price_data^)
echo     
echo     print(f"计算完成 - 用时: {results.computation_time:.4f}s"^)
echo     print(f"使用GPU: {results.used_gpu}"^)
echo     
echo     # 显示部分结果
echo     print(f"\n最新指标值:"^)
echo     if results.ma_results:
echo         print(f"MA5: {results.ma_results[0][-1]:.4f}"^)
echo         print(f"MA20: {results.ma_results[2][-1]:.4f}"^)
echo     print(f"RSI: {results.rsi_results[-1]:.4f}"^)
echo     print(f"MACD: {results.macd_histogram[-1]:.4f}"^)
echo     
echo     # 特征提取测试
echo     print("\n测试特征提取..."^)
echo     features = generator.extract_features(price_data^)
echo     print(f"提取特征数量: {len(features^)}"^)
echo     print(f"特征样本: {features[:5]}"^)
echo     
echo     print("\n=== 测试完成 ==="^)
echo.
echo if __name__ == "__main__":
echo     test_v100_performance()
) > test_v100_performance.py

echo [✓] 测试脚本已创建: test_v100_performance.py

echo.
echo 快速测试命令:
echo python test_v100_performance.py
echo.

pause
