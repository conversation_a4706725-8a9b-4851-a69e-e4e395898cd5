#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
编译阿童木信号生成器高级版本
包含20+个高级技术指标和复合信号生成
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def setup_vs_environment():
    """设置Visual Studio环境"""
    vs_paths = [
        r"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat",
        r"C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat",
        r"C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat",
        r"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat",
    ]
    
    for vs_path in vs_paths:
        if os.path.exists(vs_path):
            print(f"找到Visual Studio: {vs_path}")
            
            cmd = f'"{vs_path}" && set'
            
            try:
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                if result.returncode == 0:
                    for line in result.stdout.split('\n'):
                        if '=' in line:
                            key, value = line.split('=', 1)
                            os.environ[key] = value
                    
                    print("Visual Studio环境设置成功")
                    return True
                    
            except Exception as e:
                print(f"设置VS环境异常: {str(e)}")
                continue
    
    return False

def compile_advanced_version():
    """编译高级版本"""
    print("=== 编译阿童木信号生成器高级版本 ===")
    
    source_file = "atomboy_signal_advanced.cpp"
    output_dll = "astroboy_signal_advanced.dll"
    
    if not os.path.exists(source_file):
        print(f"错误: 源文件不存在 {source_file}")
        return False
    
    # 编译命令 - 启用更多优化
    compile_cmd = [
        "cl.exe",
        "/LD",  # 生成DLL
        "/MT",  # 静态链接运行时库
        "/O2",  # 优化
        "/Ox",  # 最大优化
        "/Ot",  # 优化速度
        "/GL",  # 全程序优化
        "/EHsc",  # 异常处理
        "/fp:fast",  # 快速浮点运算
        "/arch:AVX2",  # AVX2指令集
        "/DWIN32",
        "/D_WINDOWS",
        "/D_USRDLL",
        "/DATOMBOY_EXPORTS",
        "/D_CRT_SECURE_NO_WARNINGS",
        "/DADVANCED_INDICATORS",
        "/DV100_OPTIMIZED",
        source_file,
        f"/Fe{output_dll}",
        "/link",
        "/MACHINE:X64",
        "/LTCG",  # 链接时代码生成
        "/OPT:REF",  # 优化引用
        "/OPT:ICF"   # 相同COMDAT折叠
    ]
    
    try:
        print(f"执行高级编译命令...")
        result = subprocess.run(compile_cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 高级版本编译成功!")
            if os.path.exists(output_dll):
                size = os.path.getsize(output_dll)
                print(f"✓ 生成高级DLL: {output_dll} ({size} bytes)")
                return True
            else:
                print("✗ 编译成功但未找到DLL文件")
                return False
        else:
            print(f"✗ 编译失败:")
            print(f"stdout: {result.stdout}")
            print(f"stderr: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("✗ 未找到cl.exe编译器")
        return False
    except Exception as e:
        print(f"✗ 编译异常: {str(e)}")
        return False

def test_advanced_dll():
    """测试高级版本DLL"""
    print("\n=== 测试高级版本DLL ===")
    
    dll_path = "astroboy_signal_advanced.dll"
    if not os.path.exists(dll_path):
        print(f"✗ DLL文件不存在: {dll_path}")
        return False
    
    try:
        import ctypes
        import numpy as np
        
        abs_dll_path = os.path.abspath(dll_path)
        lib = ctypes.CDLL(abs_dll_path)
        
        # 设置函数原型
        lib.getVersion.restype = ctypes.c_char_p
        lib.getVersion.argtypes = []
        
        version = lib.getVersion()
        print(f"✓ 版本: {version.decode('utf-8')}")
        
        # 测试基本指标
        lib.calculateMA.restype = ctypes.c_double
        lib.calculateMA.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
        
        lib.calculateEMA.restype = ctypes.c_double
        lib.calculateEMA.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
        
        lib.calculateRSI.restype = ctypes.c_double
        lib.calculateRSI.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
        
        # 测试高级指标
        lib.calculateStochastic.restype = ctypes.c_double
        lib.calculateStochastic.argtypes = [
            ctypes.POINTER(ctypes.c_double), ctypes.POINTER(ctypes.c_double), 
            ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int
        ]
        
        lib.calculateCCI.restype = ctypes.c_double
        lib.calculateCCI.argtypes = [
            ctypes.POINTER(ctypes.c_double), ctypes.POINTER(ctypes.c_double), 
            ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int
        ]
        
        lib.calculateADX.restype = ctypes.c_double
        lib.calculateADX.argtypes = [
            ctypes.POINTER(ctypes.c_double), ctypes.POINTER(ctypes.c_double), 
            ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int
        ]
        
        lib.calculateVWAP.restype = ctypes.c_double
        lib.calculateVWAP.argtypes = [
            ctypes.POINTER(ctypes.c_double), ctypes.POINTER(ctypes.c_double), 
            ctypes.POINTER(ctypes.c_double), ctypes.POINTER(ctypes.c_double), 
            ctypes.c_int, ctypes.c_int
        ]
        
        # 高级信号生成
        lib.generateAdvancedSignal.restype = ctypes.c_int
        lib.generateAdvancedSignal.argtypes = [
            ctypes.POINTER(ctypes.c_double), ctypes.POINTER(ctypes.c_double), 
            ctypes.POINTER(ctypes.c_double), ctypes.POINTER(ctypes.c_double), 
            ctypes.c_int, ctypes.POINTER(ctypes.c_int)
        ]
        
        # 综合市场分析
        lib.analyzeAdvancedMarket.restype = ctypes.c_int
        lib.analyzeAdvancedMarket.argtypes = [
            ctypes.POINTER(ctypes.c_double), ctypes.POINTER(ctypes.c_double), 
            ctypes.POINTER(ctypes.c_double), ctypes.POINTER(ctypes.c_double), 
            ctypes.c_int, ctypes.POINTER(ctypes.c_double), 
            ctypes.POINTER(ctypes.c_int), ctypes.POINTER(ctypes.c_int)
        ]
        
        # 生成测试数据
        np.random.seed(42)
        data_size = 100
        
        highs = []
        lows = []
        closes = []
        volumes = []
        base_price = 100.0
        
        for i in range(data_size):
            change = np.random.normal(0, 0.02)
            base_price *= (1 + change)
            
            high = base_price * (1 + abs(np.random.normal(0, 0.01)))
            low = base_price * (1 - abs(np.random.normal(0, 0.01)))
            close = base_price
            volume = np.random.uniform(1000, 10000)
            
            highs.append(high)
            lows.append(low)
            closes.append(close)
            volumes.append(volume)
        
        # 转换为C数组
        high_array = (ctypes.c_double * data_size)(*highs)
        low_array = (ctypes.c_double * data_size)(*lows)
        close_array = (ctypes.c_double * data_size)(*closes)
        volume_array = (ctypes.c_double * data_size)(*volumes)
        
        print(f"✓ 生成测试数据: {data_size}条OHLCV数据")
        
        # 测试基本指标
        print("\n=== 基本指标测试 ===")
        ma20 = lib.calculateMA(close_array, data_size, 20)
        ema12 = lib.calculateEMA(close_array, data_size, 12)
        rsi = lib.calculateRSI(close_array, data_size, 14)
        
        print(f"MA20: {ma20:.4f}")
        print(f"EMA12: {ema12:.4f}")
        print(f"RSI: {rsi:.4f}")
        
        # 测试高级指标
        print("\n=== 高级指标测试 ===")
        stoch = lib.calculateStochastic(high_array, low_array, close_array, data_size, 14)
        cci = lib.calculateCCI(high_array, low_array, close_array, data_size, 20)
        adx = lib.calculateADX(high_array, low_array, close_array, data_size, 14)
        vwap = lib.calculateVWAP(high_array, low_array, close_array, volume_array, data_size, 20)
        
        print(f"Stochastic: {stoch:.4f}")
        print(f"CCI: {cci:.4f}")
        print(f"ADX: {adx:.4f}")
        print(f"VWAP: {vwap:.4f}")
        
        # 测试高级信号生成
        print("\n=== 高级信号生成测试 ===")
        signal_strength = ctypes.c_int()
        signal = lib.generateAdvancedSignal(
            high_array, low_array, close_array, volume_array, 
            data_size, ctypes.byref(signal_strength)
        )
        
        signal_names = {0: "NONE", 1: "BUY", 2: "SELL", 3: "HOLD"}
        print(f"高级信号: {signal_names.get(signal, 'UNKNOWN')}")
        print(f"信号强度: {signal_strength.value}")
        
        # 测试综合市场分析
        print("\n=== 综合市场分析测试 ===")
        indicators = (ctypes.c_double * 20)()  # 20个指标
        signal_result = ctypes.c_int()
        strength_result = ctypes.c_int()
        
        success = lib.analyzeAdvancedMarket(
            high_array, low_array, close_array, volume_array, data_size,
            indicators, ctypes.byref(signal_result), ctypes.byref(strength_result)
        )
        
        if success:
            print("✓ 综合分析成功")
            
            indicator_names = [
                "MA5", "MA10", "MA20", "MA50", "EMA12", "EMA26", "RSI", "Stochastic",
                "CCI", "Williams%R", "ADX", "ATR", "ParabolicSAR", "IchimokuTenkan",
                "IchimokuKijun", "VWAP", "MACD", "BB_Upper", "BB_Lower", "BB_Position"
            ]
            
            print("技术指标结果:")
            for i, name in enumerate(indicator_names):
                if indicators[i] != -1.0:
                    print(f"  {name}: {indicators[i]:.4f}")
            
            print(f"综合信号: {signal_names.get(signal_result.value, 'UNKNOWN')}")
            print(f"综合强度: {strength_result.value}")
        
        print("✓ 高级版本DLL测试通过")
        return True
        
    except Exception as e:
        print(f"✗ DLL测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("阿童木信号生成器高级版本编译")
    print("=" * 50)
    
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"工作目录: {os.getcwd()}")
    
    if not setup_vs_environment():
        print("Visual Studio环境设置失败")
        return
    
    if compile_advanced_version():
        print("高级版本编译成功，开始测试...")
        
        if test_advanced_dll():
            print("\n=== 阿童木信号生成器高级版本完成 ===")
            print("✓ 高级版DLL: astroboy_signal_advanced.dll")
            print("✓ 包含20+个高级技术指标")
            print("✓ 支持复合信号生成")
            print("✓ V100优化编译")
            print("\n特性:")
            print("  - 基本指标: MA, EMA, RSI")
            print("  - 动量指标: Stochastic, CCI, Williams%R")
            print("  - 趋势指标: ADX, Parabolic SAR, Ichimoku")
            print("  - 成交量指标: VWAP")
            print("  - 波动率指标: ATR, Bollinger Bands")
            print("  - 复合信号: 多指标综合分析")
            print("  - 信号强度: 量化信号置信度")
        else:
            print("高级版本DLL测试失败")
    else:
        print("高级版本编译失败")

if __name__ == "__main__":
    main()
