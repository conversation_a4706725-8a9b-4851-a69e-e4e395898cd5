#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
阿童木信号生成器直接编译脚本
使用Python直接调用编译器，避免批处理文件编码问题
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def find_visual_studio():
    """查找Visual Studio安装路径"""
    possible_paths = [
        r"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat",
        r"C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat",
        r"C:\Program Files\Microsoft Visual Studio\2022\Enterprise\VC\Auxiliary\Build\vcvars64.bat",
        r"C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat",
        r"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat",
        r"C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\VC\Auxiliary\Build\vcvars64.bat",
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return path
    
    return None

def setup_vs_environment():
    """设置Visual Studio环境"""
    vs_path = find_visual_studio()
    if not vs_path:
        print("错误: 未找到Visual Studio安装")
        return False
    
    print(f"找到Visual Studio: {vs_path}")
    
    # 运行vcvars64.bat并获取环境变量
    cmd = f'"{vs_path}" && set'
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"设置VS环境失败: {result.stderr}")
            return False
        
        # 解析环境变量
        for line in result.stdout.split('\n'):
            if '=' in line:
                key, value = line.split('=', 1)
                os.environ[key] = value
        
        print("Visual Studio环境设置成功")
        return True
        
    except Exception as e:
        print(f"设置VS环境异常: {str(e)}")
        return False

def compile_with_cl():
    """使用cl.exe直接编译"""
    print("使用cl.exe直接编译...")
    
    # 源文件和头文件路径
    source_file = "simple_build/src/common/atomboy_signal.cpp"
    header_dir = "simple_build/include"
    output_dll = "astroboy_fixed.dll"
    
    if not os.path.exists(source_file):
        print(f"错误: 源文件不存在 {source_file}")
        return False
    
    if not os.path.exists(header_dir):
        print(f"错误: 头文件目录不存在 {header_dir}")
        return False
    
    # 编译命令
    compile_cmd = [
        "cl.exe",
        "/LD",  # 生成DLL
        "/MT",  # 静态链接运行时库
        "/O2",  # 优化
        "/DWIN32",
        "/D_WINDOWS",
        "/D_USRDLL",
        "/DASTROBOY_EXPORTS",
        "/D_CRT_SECURE_NO_WARNINGS",
        f"/I{header_dir}",
        source_file,
        f"/Fe{output_dll}",
        "/link",
        "/MACHINE:X64"
    ]
    
    try:
        print(f"执行编译命令: {' '.join(compile_cmd)}")
        result = subprocess.run(compile_cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("编译成功!")
            if os.path.exists(output_dll):
                print(f"生成DLL: {output_dll}")
                return True
            else:
                print("警告: 编译成功但未找到DLL文件")
                return False
        else:
            print(f"编译失败:")
            print(f"stdout: {result.stdout}")
            print(f"stderr: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("错误: 未找到cl.exe编译器")
        print("请确保已安装Visual Studio并设置了环境变量")
        return False
    except Exception as e:
        print(f"编译异常: {str(e)}")
        return False

def compile_with_cmake():
    """使用CMake编译"""
    print("尝试使用CMake编译...")
    
    # 检查CMake
    try:
        result = subprocess.run(["cmake", "--version"], capture_output=True, text=True)
        if result.returncode != 0:
            print("CMake不可用")
            return False
        print(f"CMake版本: {result.stdout.split()[2]}")
    except FileNotFoundError:
        print("未找到CMake")
        return False
    
    # 创建构建目录
    build_dir = "build_direct"
    if os.path.exists(build_dir):
        shutil.rmtree(build_dir)
    os.makedirs(build_dir)
    
    try:
        # 配置
        configure_cmd = [
            "cmake",
            "-G", "Visual Studio 16 2019",
            "-A", "x64",
            "-DCMAKE_BUILD_TYPE=Release",
            "-DASTROBOY_BUILD_SHARED=ON",
            "-f", "../CMakeLists_Fixed.txt",
            ".."
        ]
        
        print("配置CMake...")
        result = subprocess.run(configure_cmd, cwd=build_dir, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"CMake配置失败: {result.stderr}")
            return False
        
        # 编译
        build_cmd = [
            "cmake",
            "--build", ".",
            "--config", "Release",
            "--parallel", "4"
        ]
        
        print("开始编译...")
        result = subprocess.run(build_cmd, cwd=build_dir, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"CMake编译失败: {result.stderr}")
            return False
        
        # 查找生成的DLL
        dll_paths = [
            os.path.join(build_dir, "Release", "astroboy_fixed.dll"),
            os.path.join(build_dir, "bin", "Release", "astroboy_fixed.dll"),
        ]
        
        for dll_path in dll_paths:
            if os.path.exists(dll_path):
                # 复制到主目录
                shutil.copy2(dll_path, "astroboy_fixed.dll")
                print(f"编译成功，DLL已复制到主目录")
                return True
        
        print("编译成功但未找到DLL文件")
        return False
        
    except Exception as e:
        print(f"CMake编译异常: {str(e)}")
        return False

def test_existing_dll():
    """测试现有DLL是否可用"""
    print("测试现有DLL...")
    
    dll_path = "simple_build/atomboy_signal.dll"
    if not os.path.exists(dll_path):
        print(f"现有DLL不存在: {dll_path}")
        return False
    
    try:
        import ctypes
        lib = ctypes.CDLL(dll_path)
        
        # 尝试调用基本函数
        lib.getVersion.restype = ctypes.c_char_p
        lib.getVersion.argtypes = []
        
        version = lib.getVersion()
        print(f"现有DLL版本: {version.decode('utf-8')}")
        
        # 复制到主目录作为备用
        shutil.copy2(dll_path, "astroboy_working.dll")
        print("现有DLL已复制为备用版本: astroboy_working.dll")
        
        return True
        
    except Exception as e:
        print(f"现有DLL测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("=== 阿童木信号生成器直接编译 ===")
    
    # 切换到脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"工作目录: {os.getcwd()}")
    
    # 测试现有DLL
    if test_existing_dll():
        print("现有DLL可用，已创建备用版本")
    
    # 尝试编译新版本
    success = False
    
    # 方法1: 设置VS环境后使用cl.exe
    if setup_vs_environment():
        success = compile_with_cl()
    
    # 方法2: 使用CMake
    if not success:
        print("\n尝试使用CMake编译...")
        success = compile_with_cmake()
    
    if success:
        print("\n=== 编译成功 ===")
        print("生成的文件:")
        for dll_name in ["astroboy_fixed.dll", "astroboy_working.dll"]:
            if os.path.exists(dll_name):
                size = os.path.getsize(dll_name)
                print(f"  {dll_name}: {size} bytes")
        
        print("\n下一步:")
        print("1. 运行 python test_fixed_dll.py 测试新DLL")
        print("2. 如果测试成功，可以在VNPY中使用")
    else:
        print("\n=== 编译失败 ===")
        if os.path.exists("astroboy_working.dll"):
            print("但现有DLL备份可用: astroboy_working.dll")
        else:
            print("建议检查Visual Studio和CMake安装")

if __name__ == "__main__":
    main()
