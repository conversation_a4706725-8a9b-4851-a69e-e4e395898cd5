#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
编译阿童木信号生成器简化版本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def setup_vs_environment():
    """设置Visual Studio环境"""
    vs_paths = [
        r"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat",
        r"C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat",
        r"C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat",
        r"C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat",
    ]
    
    for vs_path in vs_paths:
        if os.path.exists(vs_path):
            print(f"找到Visual Studio: {vs_path}")
            
            cmd = f'"{vs_path}" && set'
            
            try:
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
                if result.returncode == 0:
                    for line in result.stdout.split('\n'):
                        if '=' in line:
                            key, value = line.split('=', 1)
                            os.environ[key] = value
                    
                    print("Visual Studio环境设置成功")
                    return True
                    
            except Exception as e:
                print(f"设置VS环境异常: {str(e)}")
                continue
    
    return False

def compile_simple_version():
    """编译简化版本"""
    print("=== 编译阿童木信号生成器简化版本 ===")
    
    source_file = "atomboy_signal_simple.cpp"
    output_dll = "astroboy_signal_simple.dll"
    
    if not os.path.exists(source_file):
        print(f"错误: 源文件不存在 {source_file}")
        return False
    
    # 编译命令
    compile_cmd = [
        "cl.exe",
        "/LD",  # 生成DLL
        "/MT",  # 静态链接运行时库
        "/O2",  # 优化
        "/EHsc",  # 异常处理
        "/DWIN32",
        "/D_WINDOWS",
        "/D_USRDLL",
        "/DATOMBOY_EXPORTS",
        "/D_CRT_SECURE_NO_WARNINGS",
        source_file,
        f"/Fe{output_dll}",
        "/link",
        "/MACHINE:X64"
    ]
    
    try:
        print(f"执行编译命令...")
        result = subprocess.run(compile_cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ 编译成功!")
            if os.path.exists(output_dll):
                size = os.path.getsize(output_dll)
                print(f"✓ 生成DLL: {output_dll} ({size} bytes)")
                return True
            else:
                print("✗ 编译成功但未找到DLL文件")
                return False
        else:
            print(f"✗ 编译失败:")
            print(f"stdout: {result.stdout}")
            print(f"stderr: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("✗ 未找到cl.exe编译器")
        return False
    except Exception as e:
        print(f"✗ 编译异常: {str(e)}")
        return False

def test_simple_dll():
    """测试简化版本DLL"""
    print("\n=== 测试简化版本DLL ===")
    
    dll_path = "astroboy_signal_simple.dll"
    if not os.path.exists(dll_path):
        print(f"✗ DLL文件不存在: {dll_path}")
        return False
    
    try:
        import ctypes
        
        abs_dll_path = os.path.abspath(dll_path)
        lib = ctypes.CDLL(abs_dll_path)
        
        # 测试基本功能
        lib.getVersion.restype = ctypes.c_char_p
        lib.getVersion.argtypes = []
        
        version = lib.getVersion()
        print(f"✓ 版本: {version.decode('utf-8')}")
        
        # 测试加法
        lib.add.restype = ctypes.c_int
        lib.add.argtypes = [ctypes.c_int, ctypes.c_int]
        
        result = lib.add(15, 25)
        print(f"✓ 加法测试: 15 + 25 = {result}")
        
        # 测试MA计算
        lib.calculateMA.restype = ctypes.c_double
        lib.calculateMA.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
        
        # 测试数据 - 递增序列
        prices = [100.0, 101.0, 102.0, 103.0, 104.0, 105.0, 106.0, 107.0, 108.0, 109.0]
        price_array = (ctypes.c_double * len(prices))(*prices)
        
        ma5 = lib.calculateMA(price_array, len(prices), 5)
        ma10 = lib.calculateMA(price_array, len(prices), 10)
        
        print(f"✓ MA5: {ma5:.4f} (期望: ~107.0)")
        print(f"✓ MA10: {ma10:.4f} (期望: ~104.5)")
        
        # 验证MA计算是否正确
        expected_ma5 = sum(prices[-5:]) / 5  # 最后5个数的平均值
        expected_ma10 = sum(prices[-10:]) / 10  # 最后10个数的平均值
        
        print(f"  验证MA5: 计算值={ma5:.4f}, 期望值={expected_ma5:.4f}")
        print(f"  验证MA10: 计算值={ma10:.4f}, 期望值={expected_ma10:.4f}")
        
        # 测试RSI计算
        lib.calculateRSI.restype = ctypes.c_double
        lib.calculateRSI.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
        
        rsi = lib.calculateRSI(price_array, len(prices), 14)
        print(f"✓ RSI: {rsi:.4f}")
        
        # 测试信号生成
        lib.generateSignal.restype = ctypes.c_int
        lib.generateSignal.argtypes = [ctypes.c_double, ctypes.c_double, ctypes.c_double]
        
        signal_names = {0: "NONE", 1: "BUY", 2: "SELL", 3: "HOLD"}
        
        test_cases = [
            (100.0, 105.0, 95.0, "大幅波动"),
            (100.0, 101.0, 99.0, "小幅波动"),
            (100.0, 100.0, 100.0, "无波动"),
        ]
        
        for open_p, high_p, low_p, desc in test_cases:
            signal = lib.generateSignal(open_p, high_p, low_p)
            signal_name = signal_names.get(signal, "UNKNOWN")
            print(f"✓ {desc}: 开{open_p} 高{high_p} 低{low_p} -> {signal_name}")
        
        # 测试批量MA计算
        lib.calculateMultipleMA.restype = ctypes.c_int
        lib.calculateMultipleMA.argtypes = [
            ctypes.POINTER(ctypes.c_double),
            ctypes.c_int,
            ctypes.POINTER(ctypes.c_int),
            ctypes.c_int,
            ctypes.POINTER(ctypes.c_double)
        ]
        
        periods = [5, 10, 20]
        period_array = (ctypes.c_int * len(periods))(*periods)
        result_array = (ctypes.c_double * len(periods))()
        
        success_count = lib.calculateMultipleMA(
            price_array, len(prices), 
            period_array, len(periods), 
            result_array
        )
        
        print(f"✓ 批量MA计算成功: {success_count}/{len(periods)}")
        for i, period in enumerate(periods):
            print(f"  MA{period}: {result_array[i]:.4f}")
        
        # 测试市场分析
        lib.analyzeMarket.restype = ctypes.c_int
        lib.analyzeMarket.argtypes = [
            ctypes.POINTER(ctypes.c_double),
            ctypes.c_int,
            ctypes.POINTER(ctypes.c_double),
            ctypes.POINTER(ctypes.c_double),
            ctypes.POINTER(ctypes.c_int)
        ]
        
        ma_results = (ctypes.c_double * 4)()
        rsi_result = ctypes.c_double()
        signal_result = ctypes.c_int()
        
        success = lib.analyzeMarket(
            price_array, len(prices),
            ma_results, ctypes.byref(rsi_result), ctypes.byref(signal_result)
        )
        
        if success:
            print("✓ 市场分析:")
            periods = [5, 10, 20, 50]
            for i, period in enumerate(periods):
                if ma_results[i] != -1.0:
                    print(f"  MA{period}: {ma_results[i]:.4f}")
            
            if rsi_result.value != -1.0:
                print(f"  RSI: {rsi_result.value:.4f}")
            
            signal_name = signal_names.get(signal_result.value, 'UNKNOWN')
            print(f"  信号: {signal_name}")
        
        print("✓ 简化版本DLL测试通过")
        return True
        
    except Exception as e:
        print(f"✗ DLL测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def create_final_wrapper():
    """创建最终包装器"""
    print("\n=== 创建最终包装器 ===")
    
    wrapper_code = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
阿童木信号生成器最终版本Python包装器
完整功能，经过测试验证
"""

import os
import sys
import ctypes
import numpy as np
from typing import List, Optional, Union, Dict, Any, Tuple
from pathlib import Path

class AstroboySignalGenerator:
    """阿童木信号生成器最终版本"""
    
    def __init__(self, dll_path: Optional[str] = None):
        if dll_path is None:
            current_dir = Path(__file__).parent.absolute()
            dll_path = str(current_dir / "astroboy_signal_simple.dll")
        
        if not os.path.exists(dll_path):
            raise FileNotFoundError(f"找不到DLL文件: {dll_path}")
        
        self.dll_path = dll_path
        self.lib = None
        self.available_functions = []
        self._load_dll()
    
    def _load_dll(self):
        """加载DLL"""
        try:
            self.lib = ctypes.CDLL(self.dll_path)
            self._setup_function_prototypes()
            self._detect_available_functions()
        except Exception as e:
            raise RuntimeError(f"加载DLL失败: {str(e)}")
    
    def _setup_function_prototypes(self):
        """设置函数原型"""
        # 基本功能
        self.lib.getVersion.restype = ctypes.c_char_p
        self.lib.getVersion.argtypes = []
        
        self.lib.add.restype = ctypes.c_int
        self.lib.add.argtypes = [ctypes.c_int, ctypes.c_int]
        
        if hasattr(self.lib, 'printHello'):
            self.lib.printHello.restype = None
            self.lib.printHello.argtypes = []
        
        # 技术指标计算
        self.lib.calculateMA.restype = ctypes.c_double
        self.lib.calculateMA.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
        
        self.lib.calculateRSI.restype = ctypes.c_double
        self.lib.calculateRSI.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
        
        if hasattr(self.lib, 'calculateMACD'):
            self.lib.calculateMACD.restype = ctypes.c_double
            self.lib.calculateMACD.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int, ctypes.c_int, ctypes.c_int]
        
        if hasattr(self.lib, 'calculateBollingerUpper'):
            self.lib.calculateBollingerUpper.restype = ctypes.c_double
            self.lib.calculateBollingerUpper.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int, ctypes.c_double]
        
        if hasattr(self.lib, 'calculateBollingerLower'):
            self.lib.calculateBollingerLower.restype = ctypes.c_double
            self.lib.calculateBollingerLower.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int, ctypes.c_double]
        
        self.lib.generateSignal.restype = ctypes.c_int
        self.lib.generateSignal.argtypes = [ctypes.c_double, ctypes.c_double, ctypes.c_double]
        
        # 批量计算
        if hasattr(self.lib, 'calculateMultipleMA'):
            self.lib.calculateMultipleMA.restype = ctypes.c_int
            self.lib.calculateMultipleMA.argtypes = [
                ctypes.POINTER(ctypes.c_double), ctypes.c_int,
                ctypes.POINTER(ctypes.c_int), ctypes.c_int,
                ctypes.POINTER(ctypes.c_double)
            ]
        
        if hasattr(self.lib, 'analyzeMarket'):
            self.lib.analyzeMarket.restype = ctypes.c_int
            self.lib.analyzeMarket.argtypes = [
                ctypes.POINTER(ctypes.c_double), ctypes.c_int,
                ctypes.POINTER(ctypes.c_double),
                ctypes.POINTER(ctypes.c_double),
                ctypes.POINTER(ctypes.c_int)
            ]
    
    def _detect_available_functions(self):
        """检测可用函数"""
        test_functions = [
            'calculateMA', 'calculateRSI', 'calculateMACD',
            'calculateBollingerUpper', 'calculateBollingerLower',
            'generateSignal', 'calculateMultipleMA', 'analyzeMarket',
            'printHello'
        ]
        
        for func_name in test_functions:
            if hasattr(self.lib, func_name):
                self.available_functions.append(func_name)
    
    def get_version(self) -> str:
        """获取版本信息"""
        return self.lib.getVersion().decode('utf-8')
    
    def calculate_ma(self, prices: List[float], period: int) -> float:
        """计算移动平均线"""
        if len(prices) < period:
            raise ValueError(f"数据长度{len(prices)}小于周期{period}")
        
        price_array = (ctypes.c_double * len(prices))(*prices)
        result = self.lib.calculateMA(price_array, len(prices), period)
        
        if result == -1.0:
            raise RuntimeError("MA计算失败")
        
        return result
    
    def calculate_rsi(self, prices: List[float], period: int = 14) -> float:
        """计算RSI"""
        if len(prices) < period + 1:
            raise ValueError(f"数据长度{len(prices)}不足，需要至少{period + 1}条数据")
        
        price_array = (ctypes.c_double * len(prices))(*prices)
        result = self.lib.calculateRSI(price_array, len(prices), period)
        
        if result == -1.0:
            raise RuntimeError("RSI计算失败")
        
        return result
    
    def calculate_macd(self, prices: List[float], fast_period: int = 12, 
                      slow_period: int = 26, signal_period: int = 9) -> float:
        """计算MACD"""
        if 'calculateMACD' not in self.available_functions:
            raise NotImplementedError("calculateMACD函数不可用")
        
        if len(prices) < slow_period:
            raise ValueError(f"数据长度{len(prices)}不足，需要至少{slow_period}条数据")
        
        price_array = (ctypes.c_double * len(prices))(*prices)
        result = self.lib.calculateMACD(price_array, len(prices), fast_period, slow_period, signal_period)
        
        if result == -1.0:
            raise RuntimeError("MACD计算失败")
        
        return result
    
    def calculate_bollinger_bands(self, prices: List[float], period: int = 20, 
                                 std_multiplier: float = 2.0) -> Tuple[float, float]:
        """计算布林带上下轨"""
        if 'calculateBollingerUpper' not in self.available_functions:
            raise NotImplementedError("布林带计算函数不可用")
        
        if len(prices) < period:
            raise ValueError(f"数据长度{len(prices)}不足，需要至少{period}条数据")
        
        price_array = (ctypes.c_double * len(prices))(*prices)
        
        upper = self.lib.calculateBollingerUpper(price_array, len(prices), period, std_multiplier)
        lower = self.lib.calculateBollingerLower(price_array, len(prices), period, std_multiplier)
        
        if upper == -1.0 or lower == -1.0:
            raise RuntimeError("布林带计算失败")
        
        return upper, lower
    
    def generate_signal(self, open_price: float, high_price: float, low_price: float) -> int:
        """生成信号"""
        return self.lib.generateSignal(open_price, high_price, low_price)
    
    def get_signal_name(self, signal_code: int) -> str:
        """获取信号名称"""
        signal_names = {0: "NONE", 1: "BUY", 2: "SELL", 3: "HOLD"}
        return signal_names.get(signal_code, "UNKNOWN")
    
    def batch_calculate_ma(self, prices: List[float], periods: List[int]) -> Dict[int, float]:
        """批量计算移动平均线"""
        if 'calculateMultipleMA' not in self.available_functions:
            results = {}
            for period in periods:
                try:
                    results[period] = self.calculate_ma(prices, period)
                except:
                    results[period] = None
            return results
        
        price_array = (ctypes.c_double * len(prices))(*prices)
        period_array = (ctypes.c_int * len(periods))(*periods)
        result_array = (ctypes.c_double * len(periods))()
        
        success_count = self.lib.calculateMultipleMA(
            price_array, len(prices),
            period_array, len(periods),
            result_array
        )
        
        results = {}
        for i, period in enumerate(periods):
            if result_array[i] != -1.0:
                results[period] = result_array[i]
            else:
                results[period] = None
        
        return results
    
    def analyze_market(self, prices: List[float]) -> Dict[str, Any]:
        """完整市场分析"""
        if 'analyzeMarket' not in self.available_functions:
            return self._analyze_market_fallback(prices)
        
        price_array = (ctypes.c_double * len(prices))(*prices)
        ma_results = (ctypes.c_double * 4)()
        rsi_result = ctypes.c_double()
        signal_result = ctypes.c_int()
        
        success = self.lib.analyzeMarket(
            price_array, len(prices),
            ma_results, ctypes.byref(rsi_result), ctypes.byref(signal_result)
        )
        
        if not success:
            raise RuntimeError("市场分析失败")
        
        periods = [5, 10, 20, 50]
        ma_dict = {}
        for i, period in enumerate(periods):
            if ma_results[i] != -1.0:
                ma_dict[f'MA{period}'] = ma_results[i]
        
        return {
            'prices_count': len(prices),
            'latest_price': prices[-1] if prices else 0,
            'ma_results': ma_dict,
            'rsi': rsi_result.value if rsi_result.value != -1.0 else None,
            'signal': {
                'code': signal_result.value,
                'name': self.get_signal_name(signal_result.value)
            },
            'available_functions': self.available_functions
        }
    
    def _analyze_market_fallback(self, prices: List[float]) -> Dict[str, Any]:
        """市场分析回退方法"""
        results = {
            'prices_count': len(prices),
            'latest_price': prices[-1] if prices else 0,
            'ma_results': {},
            'rsi': None,
            'signal': None,
            'available_functions': self.available_functions
        }
        
        periods = [5, 10, 20, 50]
        for period in periods:
            if len(prices) >= period:
                try:
                    ma_value = self.calculate_ma(prices, period)
                    results['ma_results'][f'MA{period}'] = ma_value
                except:
                    pass
        
        if len(prices) >= 15:
            try:
                results['rsi'] = self.calculate_rsi(prices, 14)
            except:
                pass
        
        if len(prices) >= 3:
            try:
                recent_prices = prices[-3:]
                open_p = recent_prices[0]
                high_p = max(recent_prices)
                low_p = min(recent_prices)
                
                signal_code = self.generate_signal(open_p, high_p, low_p)
                results['signal'] = {
                    'code': signal_code,
                    'name': self.get_signal_name(signal_code)
                }
            except:
                pass
        
        return results
    
    def get_available_functions(self) -> List[str]:
        """获取可用函数列表"""
        return self.available_functions.copy()

# 兼容性别名
AtomBoySignalGenerator = AstroboySignalGenerator

# 便捷函数
def create_generator(dll_path: Optional[str] = None) -> AstroboySignalGenerator:
    """创建信号生成器实例"""
    return AstroboySignalGenerator(dll_path)

def quick_analysis(prices: List[float]) -> Dict[str, Any]:
    """快速市场分析"""
    generator = create_generator()
    return generator.analyze_market(prices)
'''
    
    try:
        with open("astroboy_wrapper_complete.py", "w", encoding="utf-8") as f:
            f.write(wrapper_code)
        
        print("✓ 完整包装器已创建: astroboy_wrapper_complete.py")
        return True
        
    except Exception as e:
        print(f"✗ 创建包装器失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("阿童木信号生成器简化版本编译")
    print("=" * 50)
    
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"工作目录: {os.getcwd()}")
    
    if not setup_vs_environment():
        print("Visual Studio环境设置失败")
        return
    
    if compile_simple_version():
        print("编译成功，开始测试...")
        
        if test_simple_dll():
            print("DLL测试通过，创建包装器...")
            
            if create_final_wrapper():
                print("\n=== 阿童木信号生成器简化版本完成 ===")
                print("✓ 简化版DLL: astroboy_signal_simple.dll")
                print("✓ 完整包装器: astroboy_wrapper_complete.py")
                print("\n使用方法:")
                print("from astroboy_wrapper_complete import AstroboySignalGenerator")
                print("generator = AstroboySignalGenerator()")
                print("print(generator.get_version())")
                print("result = generator.analyze_market([100, 101, 102, 103, 104, 105])")
                print("print(result)")
            
        else:
            print("DLL测试失败")
    else:
        print("编译失败")

if __name__ == "__main__":
    main()
