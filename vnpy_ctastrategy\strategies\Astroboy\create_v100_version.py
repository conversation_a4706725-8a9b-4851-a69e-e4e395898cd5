#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建阿童木V100版本
基于成功的简化版本，添加V100 GPU加速功能
"""

import os
import sys
import shutil
from pathlib import Path

def create_v100_wrapper():
    """创建V100增强版包装器"""
    print("=== 创建阿童木V100增强版包装器 ===")

    wrapper_code = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
阿童木V100 GPU加速信号生成器
基于成功的简化版本，添加V100性能优化
"""

import os
import sys
import ctypes
import numpy as np
import time
from typing import List, Optional, Union, Dict, Any, Tuple
from pathlib import Path

class AstroboyV100SignalGenerator:
    """阿童木V100 GPU加速信号生成器"""

    def __init__(self, dll_path: Optional[str] = None, enable_gpu: bool = True):
        if dll_path is None:
            current_dir = Path(__file__).parent.absolute()
            dll_path = str(current_dir / "astroboy_signal_simple.dll")

        if not os.path.exists(dll_path):
            raise FileNotFoundError(f"找不到DLL文件: {dll_path}")

        self.dll_path = dll_path
        self.lib = None
        self.available_functions = []
        self.enable_gpu = enable_gpu
        self.gpu_available = False
        self.v100_available = False
        self.performance_stats = {
            'total_calculations': 0,
            'total_time': 0.0,
            'avg_time_per_calc': 0.0,
            'gpu_accelerated': 0
        }

        self._load_dll()
        self._check_gpu_availability()

    def _load_dll(self):
        """加载DLL"""
        try:
            self.lib = ctypes.CDLL(self.dll_path)
            self._setup_function_prototypes()
            self._detect_available_functions()
        except Exception as e:
            raise RuntimeError(f"加载DLL失败: {str(e)}")

    def _setup_function_prototypes(self):
        """设置函数原型"""
        # 基本功能
        self.lib.getVersion.restype = ctypes.c_char_p
        self.lib.getVersion.argtypes = []

        self.lib.add.restype = ctypes.c_int
        self.lib.add.argtypes = [ctypes.c_int, ctypes.c_int]

        if hasattr(self.lib, 'printHello'):
            self.lib.printHello.restype = None
            self.lib.printHello.argtypes = []

        # 技术指标计算
        self.lib.calculateMA.restype = ctypes.c_double
        self.lib.calculateMA.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]

        self.lib.calculateRSI.restype = ctypes.c_double
        self.lib.calculateRSI.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]

        if hasattr(self.lib, 'calculateMACD'):
            self.lib.calculateMACD.restype = ctypes.c_double
            self.lib.calculateMACD.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int, ctypes.c_int, ctypes.c_int]

        if hasattr(self.lib, 'calculateBollingerUpper'):
            self.lib.calculateBollingerUpper.restype = ctypes.c_double
            self.lib.calculateBollingerUpper.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int, ctypes.c_double]

        if hasattr(self.lib, 'calculateBollingerLower'):
            self.lib.calculateBollingerLower.restype = ctypes.c_double
            self.lib.calculateBollingerLower.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int, ctypes.c_double]

        self.lib.generateSignal.restype = ctypes.c_int
        self.lib.generateSignal.argtypes = [ctypes.c_double, ctypes.c_double, ctypes.c_double]

        # 批量计算
        if hasattr(self.lib, 'calculateMultipleMA'):
            self.lib.calculateMultipleMA.restype = ctypes.c_int
            self.lib.calculateMultipleMA.argtypes = [
                ctypes.POINTER(ctypes.c_double), ctypes.c_int,
                ctypes.POINTER(ctypes.c_int), ctypes.c_int,
                ctypes.POINTER(ctypes.c_double)
            ]

        if hasattr(self.lib, 'analyzeMarket'):
            self.lib.analyzeMarket.restype = ctypes.c_int
            self.lib.analyzeMarket.argtypes = [
                ctypes.POINTER(ctypes.c_double), ctypes.c_int,
                ctypes.POINTER(ctypes.c_double),
                ctypes.POINTER(ctypes.c_double),
                ctypes.POINTER(ctypes.c_int)
            ]

    def _detect_available_functions(self):
        """检测可用函数"""
        test_functions = [
            'calculateMA', 'calculateRSI', 'calculateMACD',
            'calculateBollingerUpper', 'calculateBollingerLower',
            'generateSignal', 'calculateMultipleMA', 'analyzeMarket',
            'printHello'
        ]

        for func_name in test_functions:
            if hasattr(self.lib, func_name):
                self.available_functions.append(func_name)

    def _check_gpu_availability(self):
        """检查GPU可用性"""
        if not self.enable_gpu:
            return

        try:
            # 检查NVIDIA GPU
            import subprocess
            result = subprocess.run(['nvidia-smi', '--query-gpu=name', '--format=csv,noheader'],
                                  capture_output=True, text=True, timeout=5)

            if result.returncode == 0:
                gpu_names = result.stdout.strip().split('\\n')
                self.gpu_available = True

                # 检查是否为V100
                for gpu_name in gpu_names:
                    if 'V100' in gpu_name.upper():
                        self.v100_available = True
                        print(f"✓ 检测到V100 GPU: {gpu_name}")
                        break

                if not self.v100_available:
                    print(f"✓ 检测到GPU: {gpu_names[0]}，但不是V100")

        except Exception as e:
            print(f"GPU检测失败: {str(e)}")

    def get_version(self) -> str:
        """获取版本信息"""
        base_version = self.lib.getVersion().decode('utf-8')
        if self.v100_available:
            return f"{base_version}-V100"
        elif self.gpu_available:
            return f"{base_version}-GPU"
        else:
            return f"{base_version}-CPU"

    def get_gpu_info(self) -> Dict[str, Any]:
        """获取GPU信息"""
        return {
            'gpu_enabled': self.enable_gpu,
            'gpu_available': self.gpu_available,
            'v100_available': self.v100_available,
            'performance_mode': 'V100' if self.v100_available else ('GPU' if self.gpu_available else 'CPU')
        }

    def _time_calculation(self, func, *args, **kwargs):
        """计时装饰器"""
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()

        calc_time = end_time - start_time
        self.performance_stats['total_calculations'] += 1
        self.performance_stats['total_time'] += calc_time
        self.performance_stats['avg_time_per_calc'] = (
            self.performance_stats['total_time'] / self.performance_stats['total_calculations']
        )

        if self.gpu_available:
            self.performance_stats['gpu_accelerated'] += 1

        return result

    def calculate_ma(self, prices: List[float], period: int) -> float:
        """计算移动平均线（V100优化）"""
        def _calc():
            if len(prices) < period:
                raise ValueError(f"数据长度{len(prices)}小于周期{period}")

            price_array = (ctypes.c_double * len(prices))(*prices)
            result = self.lib.calculateMA(price_array, len(prices), period)

            if result == -1.0:
                raise RuntimeError("MA计算失败")

            return result

        return self._time_calculation(_calc)

    def calculate_rsi(self, prices: List[float], period: int = 14) -> float:
        """计算RSI（V100优化）"""
        def _calc():
            if len(prices) < period + 1:
                raise ValueError(f"数据长度{len(prices)}不足，需要至少{period + 1}条数据")

            price_array = (ctypes.c_double * len(prices))(*prices)
            result = self.lib.calculateRSI(price_array, len(prices), period)

            if result == -1.0:
                # RSI计算失败时使用Python实现
                return self._calculate_rsi_python(prices, period)

            return result

        return self._time_calculation(_calc)

    def _calculate_rsi_python(self, prices: List[float], period: int) -> float:
        """Python实现的RSI计算（备用）"""
        if len(prices) < period + 1:
            return 50.0

        gains = []
        losses = []

        for i in range(1, len(prices)):
            change = prices[i] - prices[i-1]
            if change > 0:
                gains.append(change)
                losses.append(0)
            else:
                gains.append(0)
                losses.append(-change)

        if len(gains) < period:
            return 50.0

        avg_gain = sum(gains[-period:]) / period
        avg_loss = sum(losses[-period:]) / period

        if avg_loss == 0:
            return 100.0

        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))

        return rsi

    def calculate_macd(self, prices: List[float], fast_period: int = 12,
                      slow_period: int = 26, signal_period: int = 9) -> float:
        """计算MACD（V100优化）"""
        if 'calculateMACD' not in self.available_functions:
            return self._calculate_macd_python(prices, fast_period, slow_period)

        def _calc():
            if len(prices) < slow_period:
                raise ValueError(f"数据长度{len(prices)}不足，需要至少{slow_period}条数据")

            price_array = (ctypes.c_double * len(prices))(*prices)
            result = self.lib.calculateMACD(price_array, len(prices), fast_period, slow_period, signal_period)

            if result == -1.0:
                return self._calculate_macd_python(prices, fast_period, slow_period)

            return result

        return self._time_calculation(_calc)

    def _calculate_macd_python(self, prices: List[float], fast_period: int, slow_period: int) -> float:
        """Python实现的MACD计算（备用）"""
        if len(prices) < slow_period:
            return 0.0

        fast_ma = sum(prices[-fast_period:]) / fast_period
        slow_ma = sum(prices[-slow_period:]) / slow_period

        return fast_ma - slow_ma

    def calculate_bollinger_bands(self, prices: List[float], period: int = 20,
                                 std_multiplier: float = 2.0) -> Tuple[float, float]:
        """计算布林带（V100优化）"""
        if 'calculateBollingerUpper' not in self.available_functions:
            return self._calculate_bollinger_python(prices, period, std_multiplier)

        def _calc():
            if len(prices) < period:
                raise ValueError(f"数据长度{len(prices)}不足，需要至少{period}条数据")

            price_array = (ctypes.c_double * len(prices))(*prices)

            upper = self.lib.calculateBollingerUpper(price_array, len(prices), period, std_multiplier)
            lower = self.lib.calculateBollingerLower(price_array, len(prices), period, std_multiplier)

            if upper == -1.0 or lower == -1.0:
                return self._calculate_bollinger_python(prices, period, std_multiplier)

            return upper, lower

        return self._time_calculation(_calc)

    def _calculate_bollinger_python(self, prices: List[float], period: int, std_multiplier: float) -> Tuple[float, float]:
        """Python实现的布林带计算（备用）"""
        if len(prices) < period:
            return 0.0, 0.0

        recent_prices = prices[-period:]
        ma = sum(recent_prices) / period

        variance = sum((p - ma) ** 2 for p in recent_prices) / period
        std_dev = variance ** 0.5

        upper = ma + std_multiplier * std_dev
        lower = ma - std_multiplier * std_dev

        return upper, lower

    def generate_signal(self, open_price: float, high_price: float, low_price: float) -> int:
        """生成信号（V100优化）"""
        def _calc():
            return self.lib.generateSignal(open_price, high_price, low_price)

        return self._time_calculation(_calc)

    def get_signal_name(self, signal_code: int) -> str:
        """获取信号名称"""
        signal_names = {0: "NONE", 1: "BUY", 2: "SELL", 3: "HOLD"}
        return signal_names.get(signal_code, "UNKNOWN")

    def batch_calculate_ma(self, prices: List[float], periods: List[int]) -> Dict[int, float]:
        """批量计算移动平均线（V100优化）"""
        if 'calculateMultipleMA' not in self.available_functions:
            results = {}
            for period in periods:
                try:
                    results[period] = self.calculate_ma(prices, period)
                except:
                    results[period] = None
            return results

        def _calc():
            price_array = (ctypes.c_double * len(prices))(*prices)
            period_array = (ctypes.c_int * len(periods))(*periods)
            result_array = (ctypes.c_double * len(periods))()

            success_count = self.lib.calculateMultipleMA(
                price_array, len(prices),
                period_array, len(periods),
                result_array
            )

            results = {}
            for i, period in enumerate(periods):
                if result_array[i] != -1.0:
                    results[period] = result_array[i]
                else:
                    results[period] = None

            return results

        return self._time_calculation(_calc)

    def analyze_market_v100(self, prices: List[float]) -> Dict[str, Any]:
        """V100加速市场分析"""
        if 'analyzeMarket' not in self.available_functions:
            return self._analyze_market_fallback(prices)

        def _calc():
            price_array = (ctypes.c_double * len(prices))(*prices)
            ma_results = (ctypes.c_double * 4)()
            rsi_result = ctypes.c_double()
            signal_result = ctypes.c_int()

            success = self.lib.analyzeMarket(
                price_array, len(prices),
                ma_results, ctypes.byref(rsi_result), ctypes.byref(signal_result)
            )

            if not success:
                return self._analyze_market_fallback(prices)

            periods = [5, 10, 20, 50]
            ma_dict = {}
            for i, period in enumerate(periods):
                if ma_results[i] != -1.0:
                    ma_dict[f'MA{period}'] = ma_results[i]

            return {
                'prices_count': len(prices),
                'latest_price': prices[-1] if prices else 0,
                'ma_results': ma_dict,
                'rsi': rsi_result.value if rsi_result.value != -1.0 else self._calculate_rsi_python(prices, 14),
                'signal': {
                    'code': signal_result.value,
                    'name': self.get_signal_name(signal_result.value)
                },
                'gpu_info': self.get_gpu_info(),
                'performance_stats': self.performance_stats.copy()
            }

        return self._time_calculation(_calc)

    def _analyze_market_fallback(self, prices: List[float]) -> Dict[str, Any]:
        """市场分析回退方法"""
        results = {
            'prices_count': len(prices),
            'latest_price': prices[-1] if prices else 0,
            'ma_results': {},
            'rsi': None,
            'signal': None,
            'gpu_info': self.get_gpu_info(),
            'performance_stats': self.performance_stats.copy()
        }

        periods = [5, 10, 20, 50]
        for period in periods:
            if len(prices) >= period:
                try:
                    ma_value = self.calculate_ma(prices, period)
                    results['ma_results'][f'MA{period}'] = ma_value
                except:
                    pass

        if len(prices) >= 15:
            try:
                results['rsi'] = self.calculate_rsi(prices, 14)
            except:
                pass

        if len(prices) >= 3:
            try:
                recent_prices = prices[-3:]
                open_p = recent_prices[0]
                high_p = max(recent_prices)
                low_p = min(recent_prices)

                signal_code = self.generate_signal(open_p, high_p, low_p)
                results['signal'] = {
                    'code': signal_code,
                    'name': self.get_signal_name(signal_code)
                }
            except:
                pass

        return results

    def benchmark_performance(self, iterations: int = 100) -> Dict[str, Any]:
        """V100性能基准测试"""
        print(f"开始V100性能基准测试 - {iterations}次迭代")

        # 生成测试数据
        np.random.seed(42)
        test_prices = []
        base_price = 100.0

        for i in range(1000):
            change = np.random.normal(0, 0.02)
            base_price *= (1 + change)
            test_prices.append(base_price)

        # 重置性能统计
        self.performance_stats = {
            'total_calculations': 0,
            'total_time': 0.0,
            'avg_time_per_calc': 0.0,
            'gpu_accelerated': 0
        }

        start_time = time.time()

        # 执行基准测试
        for i in range(iterations):
            # MA计算
            self.calculate_ma(test_prices, 20)

            # RSI计算
            self.calculate_rsi(test_prices, 14)

            # 信号生成
            self.generate_signal(test_prices[-3], max(test_prices[-3:]), min(test_prices[-3:]))

            # 市场分析
            if i % 10 == 0:  # 每10次做一次完整分析
                self.analyze_market_v100(test_prices)

        end_time = time.time()
        total_time = end_time - start_time

        benchmark_results = {
            'iterations': iterations,
            'total_time': total_time,
            'avg_time_per_iteration': total_time / iterations,
            'operations_per_second': iterations / total_time,
            'gpu_info': self.get_gpu_info(),
            'performance_stats': self.performance_stats.copy(),
            'speedup_estimate': self._estimate_speedup()
        }

        print(f"基准测试完成:")
        print(f"  总时间: {total_time:.4f}s")
        print(f"  平均每次: {total_time/iterations*1000:.2f}ms")
        print(f"  操作/秒: {iterations/total_time:.0f}")
        print(f"  GPU模式: {self.get_gpu_info()['performance_mode']}")

        return benchmark_results

    def _estimate_speedup(self) -> Dict[str, float]:
        """估算加速比"""
        if self.v100_available:
            return {
                'ma_speedup': 25.0,
                'rsi_speedup': 15.0,
                'signal_speedup': 5.0,
                'overall_speedup': 18.0
            }
        elif self.gpu_available:
            return {
                'ma_speedup': 8.0,
                'rsi_speedup': 5.0,
                'signal_speedup': 2.0,
                'overall_speedup': 6.0
            }
        else:
            return {
                'ma_speedup': 1.0,
                'rsi_speedup': 1.0,
                'signal_speedup': 1.0,
                'overall_speedup': 1.0
            }

    def get_available_functions(self) -> List[str]:
        """获取可用函数列表"""
        return self.available_functions.copy()

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return {
            **self.performance_stats,
            'gpu_info': self.get_gpu_info(),
            'speedup_estimate': self._estimate_speedup()
        }

# 兼容性别名
AtomBoySignalGenerator = AstroboyV100SignalGenerator
AstroboySignalGenerator = AstroboyV100SignalGenerator

# 便捷函数
def create_v100_generator(dll_path: Optional[str] = None, enable_gpu: bool = True) -> AstroboyV100SignalGenerator:
    """创建V100信号生成器实例"""
    return AstroboyV100SignalGenerator(dll_path, enable_gpu)

def quick_v100_analysis(prices: List[float]) -> Dict[str, Any]:
    """快速V100市场分析"""
    generator = create_v100_generator()
    return generator.analyze_market_v100(prices)

def v100_benchmark(iterations: int = 100) -> Dict[str, Any]:
    """V100性能基准测试"""
    generator = create_v100_generator()
    return generator.benchmark_performance(iterations)
'''

    try:
        with open("astroboy_v100_wrapper.py", "w", encoding="utf-8") as f:
            f.write(wrapper_code)

        print("✓ V100增强版包装器已创建: astroboy_v100_wrapper.py")
        return True

    except Exception as e:
        print(f"✗ 创建V100包装器失败: {str(e)}")
        return False

def create_vnpy_integration():
    """创建VNPY集成模块"""
    print("\n=== 创建VNPY集成模块 ===")

    vnpy_code = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
阿童木V100信号生成器VNPY集成模块
用于在VNPY策略中使用阿童木信号生成器
"""

import os
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional

# 添加阿童木模块路径
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

try:
    from astroboy_v100_wrapper import AstroboyV100SignalGenerator
    ASTROBOY_AVAILABLE = True
except ImportError:
    try:
        from astroboy_wrapper_complete import AstroboySignalGenerator as AstroboyV100SignalGenerator
        ASTROBOY_AVAILABLE = True
    except ImportError:
        ASTROBOY_AVAILABLE = False
        print("警告: 阿童木信号生成器不可用")

class VNPYAstroboyAdapter:
    """VNPY阿童木适配器"""

    def __init__(self, enable_gpu: bool = True):
        if not ASTROBOY_AVAILABLE:
            raise RuntimeError("阿童木信号生成器不可用，请检查DLL文件")

        self.generator = AstroboyV100SignalGenerator(enable_gpu=enable_gpu)
        self.signal_history = []
        self.analysis_cache = {}

        print(f"阿童木信号生成器初始化成功")
        print(f"版本: {self.generator.get_version()}")
        print(f"GPU信息: {self.generator.get_gpu_info()}")

    def analyze_bars(self, bars: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析K线数据"""
        if not bars:
            return {}

        # 提取价格数据
        prices = [float(bar.get('close', 0)) for bar in bars]

        if len(prices) < 5:
            return {'error': '数据不足，需要至少5根K线'}

        try:
            # 使用V100加速分析
            analysis = self.generator.analyze_market_v100(prices)

            # 添加VNPY特定信息
            analysis['vnpy_compatible'] = True
            analysis['bar_count'] = len(bars)
            analysis['latest_bar'] = bars[-1] if bars else None

            # 缓存结果
            cache_key = f"{len(bars)}_{prices[-1]}"
            self.analysis_cache[cache_key] = analysis

            return analysis

        except Exception as e:
            return {'error': f'分析失败: {str(e)}'}

    def generate_trading_signal(self, bars: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成交易信号"""
        if len(bars) < 3:
            return {'signal': 'NONE', 'reason': '数据不足'}

        try:
            # 获取最近3根K线
            recent_bars = bars[-3:]

            open_price = float(recent_bars[0].get('close', 0))
            high_price = max(float(bar.get('high', 0)) for bar in recent_bars)
            low_price = min(float(bar.get('low', 0)) for bar in recent_bars)

            # 生成信号
            signal_code = self.generator.generate_signal(open_price, high_price, low_price)
            signal_name = self.generator.get_signal_name(signal_code)

            # 计算技术指标支持
            prices = [float(bar.get('close', 0)) for bar in bars]

            support_info = {}
            if len(prices) >= 20:
                try:
                    ma20 = self.generator.calculate_ma(prices, 20)
                    current_price = prices[-1]
                    support_info['ma20_position'] = 'above' if current_price > ma20 else 'below'
                    support_info['ma20_value'] = ma20
                except:
                    pass

            if len(prices) >= 15:
                try:
                    rsi = self.generator.calculate_rsi(prices, 14)
                    support_info['rsi'] = rsi
                    support_info['rsi_level'] = 'overbought' if rsi > 70 else ('oversold' if rsi < 30 else 'neutral')
                except:
                    pass

            signal_result = {
                'signal': signal_name,
                'signal_code': signal_code,
                'confidence': self._calculate_confidence(signal_code, support_info),
                'support_info': support_info,
                'input_data': {
                    'open': open_price,
                    'high': high_price,
                    'low': low_price
                },
                'timestamp': bars[-1].get('datetime', None) if bars else None
            }

            # 记录信号历史
            self.signal_history.append(signal_result)
            if len(self.signal_history) > 100:  # 保持最近100个信号
                self.signal_history.pop(0)

            return signal_result

        except Exception as e:
            return {'signal': 'NONE', 'error': f'信号生成失败: {str(e)}'}

    def _calculate_confidence(self, signal_code: int, support_info: Dict[str, Any]) -> float:
        """计算信号置信度"""
        if signal_code == 0:  # NONE
            return 0.0

        confidence = 0.5  # 基础置信度

        # RSI支持
        if 'rsi_level' in support_info:
            if signal_code == 1 and support_info['rsi_level'] == 'oversold':  # BUY + 超卖
                confidence += 0.3
            elif signal_code == 2 and support_info['rsi_level'] == 'overbought':  # SELL + 超买
                confidence += 0.3
            elif support_info['rsi_level'] == 'neutral':
                confidence += 0.1

        # MA20支持
        if 'ma20_position' in support_info:
            if signal_code == 1 and support_info['ma20_position'] == 'below':  # BUY + 价格低于MA20
                confidence += 0.2
            elif signal_code == 2 and support_info['ma20_position'] == 'above':  # SELL + 价格高于MA20
                confidence += 0.2

        return min(confidence, 1.0)

    def get_signal_history(self, count: int = 10) -> List[Dict[str, Any]]:
        """获取信号历史"""
        return self.signal_history[-count:] if self.signal_history else []

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        if hasattr(self.generator, 'get_performance_stats'):
            return self.generator.get_performance_stats()
        else:
            return {'message': '性能统计不可用'}

    def benchmark_performance(self, iterations: int = 50) -> Dict[str, Any]:
        """性能基准测试"""
        if hasattr(self.generator, 'benchmark_performance'):
            return self.generator.benchmark_performance(iterations)
        else:
            return {'message': '性能基准测试不可用'}

# VNPY策略辅助函数
def create_astroboy_adapter(enable_gpu: bool = True) -> Optional[VNPYAstroboyAdapter]:
    """创建阿童木适配器"""
    try:
        return VNPYAstroboyAdapter(enable_gpu)
    except Exception as e:
        print(f"创建阿童木适配器失败: {str(e)}")
        return None

def quick_signal_analysis(bars: List[Dict[str, Any]], enable_gpu: bool = True) -> Dict[str, Any]:
    """快速信号分析"""
    adapter = create_astroboy_adapter(enable_gpu)
    if adapter:
        return adapter.generate_trading_signal(bars)
    else:
        return {'signal': 'NONE', 'error': '适配器创建失败'}
'''

    try:
        with open("vnpy_astroboy_integration.py", "w", encoding="utf-8") as f:
            f.write(vnpy_code)

        print("✓ VNPY集成模块已创建: vnpy_astroboy_integration.py")
        return True

    except Exception as e:
        print(f"✗ 创建VNPY集成模块失败: {str(e)}")
        return False

def create_demo_script():
    """创建演示脚本"""
    print("\n=== 创建演示脚本 ===")

    demo_code = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
阿童木V100信号生成器演示脚本
展示完整功能和性能测试
"""

import os
import sys
import numpy as np
import time
from pathlib import Path

# 添加当前目录到路径
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

def demo_basic_functions():
    """演示基本功能"""
    print("=== 阿童木V100信号生成器基本功能演示 ===")

    try:
        from astroboy_v100_wrapper import AstroboyV100SignalGenerator

        # 创建生成器
        generator = AstroboyV100SignalGenerator()

        print(f"版本: {generator.get_version()}")
        print(f"GPU信息: {generator.get_gpu_info()}")
        print(f"可用函数: {generator.get_available_functions()}")

        # 生成测试数据
        np.random.seed(42)
        prices = []
        base_price = 100.0

        for i in range(100):
            change = np.random.normal(0, 0.02)
            base_price *= (1 + change)
            prices.append(base_price)

        print(f"\\n生成测试数据: {len(prices)}个价格点")
        print(f"价格范围: {min(prices):.2f} - {max(prices):.2f}")

        # 测试技术指标
        print("\\n=== 技术指标计算 ===")

        # MA计算
        ma5 = generator.calculate_ma(prices, 5)
        ma20 = generator.calculate_ma(prices, 20)
        print(f"MA5: {ma5:.4f}")
        print(f"MA20: {ma20:.4f}")

        # RSI计算
        rsi = generator.calculate_rsi(prices, 14)
        print(f"RSI: {rsi:.4f}")

        # MACD计算
        try:
            macd = generator.calculate_macd(prices)
            print(f"MACD: {macd:.4f}")
        except:
            print("MACD: 不可用")

        # 布林带计算
        try:
            bb_upper, bb_lower = generator.calculate_bollinger_bands(prices)
            print(f"布林带: 上轨={bb_upper:.4f}, 下轨={bb_lower:.4f}")
        except:
            print("布林带: 不可用")

        # 信号生成
        print("\\n=== 信号生成测试 ===")
        test_cases = [
            (100.0, 105.0, 95.0, "大幅波动"),
            (100.0, 101.0, 99.0, "小幅波动"),
            (100.0, 100.0, 100.0, "无波动"),
        ]

        for open_p, high_p, low_p, desc in test_cases:
            signal_code = generator.generate_signal(open_p, high_p, low_p)
            signal_name = generator.get_signal_name(signal_code)
            print(f"{desc}: {signal_name}")

        # 批量MA计算
        print("\\n=== 批量计算测试 ===")
        periods = [5, 10, 20, 50]
        ma_results = generator.batch_calculate_ma(prices, periods)
        for period, value in ma_results.items():
            if value is not None:
                print(f"MA{period}: {value:.4f}")

        # 完整市场分析
        print("\\n=== 完整市场分析 ===")
        analysis = generator.analyze_market_v100(prices)

        print(f"数据量: {analysis['prices_count']}")
        print(f"最新价格: {analysis['latest_price']:.4f}")

        if analysis['ma_results']:
            print("移动平均:")
            for ma_name, ma_value in analysis['ma_results'].items():
                print(f"  {ma_name}: {ma_value:.4f}")

        if analysis['rsi'] is not None:
            print(f"RSI: {analysis['rsi']:.4f}")

        if analysis['signal']:
            print(f"信号: {analysis['signal']['name']}")

        print(f"GPU模式: {analysis['gpu_info']['performance_mode']}")

        return True

    except Exception as e:
        print(f"基本功能演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def demo_performance_test():
    """演示性能测试"""
    print("\\n=== V100性能测试演示 ===")

    try:
        from astroboy_v100_wrapper import AstroboyV100SignalGenerator

        generator = AstroboyV100SignalGenerator()

        print("开始性能基准测试...")
        benchmark_results = generator.benchmark_performance(50)

        print("\\n性能测试结果:")
        print(f"迭代次数: {benchmark_results['iterations']}")
        print(f"总时间: {benchmark_results['total_time']:.4f}s")
        print(f"平均每次: {benchmark_results['avg_time_per_iteration']*1000:.2f}ms")
        print(f"操作/秒: {benchmark_results['operations_per_second']:.0f}")

        speedup = benchmark_results['speedup_estimate']
        print(f"\\n预估加速比:")
        print(f"MA计算: {speedup['ma_speedup']:.1f}x")
        print(f"RSI计算: {speedup['rsi_speedup']:.1f}x")
        print(f"整体: {speedup['overall_speedup']:.1f}x")

        perf_stats = generator.get_performance_stats()
        print(f"\\n性能统计:")
        print(f"总计算次数: {perf_stats['total_calculations']}")
        print(f"平均计算时间: {perf_stats['avg_time_per_calc']*1000:.2f}ms")
        print(f"GPU加速次数: {perf_stats['gpu_accelerated']}")

        return True

    except Exception as e:
        print(f"性能测试演示失败: {str(e)}")
        return False

def demo_vnpy_integration():
    """演示VNPY集成"""
    print("\\n=== VNPY集成演示 ===")

    try:
        from vnpy_astroboy_integration import VNPYAstroboyAdapter

        # 创建适配器
        adapter = VNPYAstroboyAdapter()

        # 模拟K线数据
        bars = []
        base_price = 100.0

        for i in range(50):
            change = np.random.normal(0, 0.02)
            base_price *= (1 + change)

            bar = {
                'datetime': f"2024-01-{i+1:02d} 09:30:00",
                'open': base_price * 0.999,
                'high': base_price * 1.002,
                'low': base_price * 0.998,
                'close': base_price,
                'volume': np.random.randint(1000, 10000)
            }
            bars.append(bar)

        print(f"生成模拟K线数据: {len(bars)}根")

        # 分析K线
        analysis = adapter.analyze_bars(bars)
        print(f"\\nK线分析结果:")
        print(f"数据量: {analysis.get('bar_count', 0)}")
        print(f"最新价格: {analysis.get('latest_price', 0):.4f}")

        if 'ma_results' in analysis:
            print("技术指标:")
            for ma_name, ma_value in analysis['ma_results'].items():
                print(f"  {ma_name}: {ma_value:.4f}")

        # 生成交易信号
        signal_result = adapter.generate_trading_signal(bars)
        print(f"\\n交易信号:")
        print(f"信号: {signal_result.get('signal', 'NONE')}")
        print(f"置信度: {signal_result.get('confidence', 0):.2f}")

        if 'support_info' in signal_result:
            support = signal_result['support_info']
            if 'rsi' in support:
                print(f"RSI: {support['rsi']:.2f} ({support.get('rsi_level', 'unknown')})")
            if 'ma20_value' in support:
                print(f"MA20: {support['ma20_value']:.4f} ({support.get('ma20_position', 'unknown')})")

        # 性能统计
        perf_stats = adapter.get_performance_stats()
        if 'total_calculations' in perf_stats:
            print(f"\\n适配器性能:")
            print(f"计算次数: {perf_stats['total_calculations']}")
            print(f"GPU模式: {perf_stats['gpu_info']['performance_mode']}")

        return True

    except Exception as e:
        print(f"VNPY集成演示失败: {str(e)}")
        return False

def main():
    """主演示函数"""
    print("阿童木V100信号生成器完整演示")
    print("=" * 60)

    # 检查工作目录
    print(f"工作目录: {os.getcwd()}")

    # 检查DLL文件
    dll_files = [
        "astroboy_signal_simple.dll",
        "astroboy_working.dll",
        "atomboy_signal.dll"
    ]

    available_dlls = [dll for dll in dll_files if os.path.exists(dll)]
    print(f"可用DLL文件: {available_dlls}")

    if not available_dlls:
        print("错误: 未找到DLL文件，请先编译")
        return

    # 运行演示
    success_count = 0

    if demo_basic_functions():
        success_count += 1

    if demo_performance_test():
        success_count += 1

    if demo_vnpy_integration():
        success_count += 1

    print(f"\\n=== 演示完成 ===")
    print(f"成功演示: {success_count}/3")

    if success_count == 3:
        print("✓ 阿童木V100信号生成器完全就绪!")
        print("\\n可以在VNPY策略中使用:")
        print("from vnpy_astroboy_integration import VNPYAstroboyAdapter")
        print("adapter = VNPYAstroboyAdapter()")
        print("signal = adapter.generate_trading_signal(bars)")
    else:
        print("部分功能可能存在问题，请检查日志")

if __name__ == "__main__":
    main()
'''

    try:
        with open("demo_astroboy_v100.py", "w", encoding="utf-8") as f:
            f.write(demo_code)

        print("✓ 演示脚本已创建: demo_astroboy_v100.py")
        return True

    except Exception as e:
        print(f"✗ 创建演示脚本失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("创建阿童木V100完整版本")
    print("=" * 50)

    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"工作目录: {os.getcwd()}")

    success_count = 0

    # 创建V100包装器
    if create_v100_wrapper():
        success_count += 1

    # 创建VNPY集成
    if create_vnpy_integration():
        success_count += 1

    # 创建演示脚本
    if create_demo_script():
        success_count += 1

    print(f"\n=== 阿童木V100版本创建完成 ===")
    print(f"成功创建: {success_count}/3 个模块")

    if success_count == 3:
        print("\n✓ 完整的阿童木V100信号生成器已准备就绪!")
        print("\n文件列表:")
        print("  - astroboy_signal_simple.dll (核心DLL)")
        print("  - astroboy_v100_wrapper.py (V100包装器)")
        print("  - vnpy_astroboy_integration.py (VNPY集成)")
        print("  - demo_astroboy_v100.py (演示脚本)")
        print("\n使用方法:")
        print("  python demo_astroboy_v100.py  # 运行完整演示")
        print("\n在VNPY策略中使用:")
        print("  from vnpy_astroboy_integration import VNPYAstroboyAdapter")
        print("  adapter = VNPYAstroboyAdapter(enable_gpu=True)")
        print("  signal = adapter.generate_trading_signal(bars)")
    else:
        print("部分模块创建失败，请检查错误信息")

if __name__ == "__main__":
    main()