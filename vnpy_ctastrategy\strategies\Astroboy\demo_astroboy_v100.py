#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
阿童木V100信号生成器演示脚本
展示完整功能和性能测试
"""

import os
import sys
import numpy as np
import time
from pathlib import Path

# 添加当前目录到路径
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

def demo_basic_functions():
    """演示基本功能"""
    print("=== 阿童木V100信号生成器基本功能演示 ===")

    try:
        from astroboy_v100_wrapper import AstroboyV100SignalGenerator

        # 创建生成器
        generator = AstroboyV100SignalGenerator()

        print(f"版本: {generator.get_version()}")
        print(f"GPU信息: {generator.get_gpu_info()}")
        print(f"可用函数: {generator.get_available_functions()}")

        # 生成测试数据
        np.random.seed(42)
        prices = []
        base_price = 100.0

        for i in range(100):
            change = np.random.normal(0, 0.02)
            base_price *= (1 + change)
            prices.append(base_price)

        print(f"\n生成测试数据: {len(prices)}个价格点")
        print(f"价格范围: {min(prices):.2f} - {max(prices):.2f}")

        # 测试技术指标
        print("\n=== 技术指标计算 ===")

        # MA计算
        ma5 = generator.calculate_ma(prices, 5)
        ma20 = generator.calculate_ma(prices, 20)
        print(f"MA5: {ma5:.4f}")
        print(f"MA20: {ma20:.4f}")

        # RSI计算
        rsi = generator.calculate_rsi(prices, 14)
        print(f"RSI: {rsi:.4f}")

        # MACD计算
        try:
            macd = generator.calculate_macd(prices)
            print(f"MACD: {macd:.4f}")
        except:
            print("MACD: 不可用")

        # 布林带计算
        try:
            bb_upper, bb_lower = generator.calculate_bollinger_bands(prices)
            print(f"布林带: 上轨={bb_upper:.4f}, 下轨={bb_lower:.4f}")
        except:
            print("布林带: 不可用")

        # 信号生成
        print("\n=== 信号生成测试 ===")
        test_cases = [
            (100.0, 105.0, 95.0, "大幅波动"),
            (100.0, 101.0, 99.0, "小幅波动"),
            (100.0, 100.0, 100.0, "无波动"),
        ]

        for open_p, high_p, low_p, desc in test_cases:
            signal_code = generator.generate_signal(open_p, high_p, low_p)
            signal_name = generator.get_signal_name(signal_code)
            print(f"{desc}: {signal_name}")

        # 批量MA计算
        print("\n=== 批量计算测试 ===")
        periods = [5, 10, 20, 50]
        ma_results = generator.batch_calculate_ma(prices, periods)
        for period, value in ma_results.items():
            if value is not None:
                print(f"MA{period}: {value:.4f}")

        # 完整市场分析
        print("\n=== 完整市场分析 ===")
        analysis = generator.analyze_market_v100(prices)

        print(f"数据量: {analysis['prices_count']}")
        print(f"最新价格: {analysis['latest_price']:.4f}")

        if analysis['ma_results']:
            print("移动平均:")
            for ma_name, ma_value in analysis['ma_results'].items():
                print(f"  {ma_name}: {ma_value:.4f}")

        if analysis['rsi'] is not None:
            print(f"RSI: {analysis['rsi']:.4f}")

        if analysis['signal']:
            print(f"信号: {analysis['signal']['name']}")

        print(f"GPU模式: {analysis['gpu_info']['performance_mode']}")

        return True

    except Exception as e:
        print(f"基本功能演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def demo_performance_test():
    """演示性能测试"""
    print("\n=== V100性能测试演示 ===")

    try:
        from astroboy_v100_wrapper import AstroboyV100SignalGenerator

        generator = AstroboyV100SignalGenerator()

        print("开始性能基准测试...")
        benchmark_results = generator.benchmark_performance(50)

        print("\n性能测试结果:")
        print(f"迭代次数: {benchmark_results['iterations']}")
        print(f"总时间: {benchmark_results['total_time']:.4f}s")
        print(f"平均每次: {benchmark_results['avg_time_per_iteration']*1000:.2f}ms")
        print(f"操作/秒: {benchmark_results['operations_per_second']:.0f}")

        speedup = benchmark_results['speedup_estimate']
        print(f"\n预估加速比:")
        print(f"MA计算: {speedup['ma_speedup']:.1f}x")
        print(f"RSI计算: {speedup['rsi_speedup']:.1f}x")
        print(f"整体: {speedup['overall_speedup']:.1f}x")

        perf_stats = generator.get_performance_stats()
        print(f"\n性能统计:")
        print(f"总计算次数: {perf_stats['total_calculations']}")
        print(f"平均计算时间: {perf_stats['avg_time_per_calc']*1000:.2f}ms")
        print(f"GPU加速次数: {perf_stats['gpu_accelerated']}")

        return True

    except Exception as e:
        print(f"性能测试演示失败: {str(e)}")
        return False

def demo_vnpy_integration():
    """演示VNPY集成"""
    print("\n=== VNPY集成演示 ===")

    try:
        from vnpy_astroboy_integration import VNPYAstroboyAdapter

        # 创建适配器
        adapter = VNPYAstroboyAdapter()

        # 模拟K线数据
        bars = []
        base_price = 100.0

        for i in range(50):
            change = np.random.normal(0, 0.02)
            base_price *= (1 + change)

            bar = {
                'datetime': f"2024-01-{i+1:02d} 09:30:00",
                'open': base_price * 0.999,
                'high': base_price * 1.002,
                'low': base_price * 0.998,
                'close': base_price,
                'volume': np.random.randint(1000, 10000)
            }
            bars.append(bar)

        print(f"生成模拟K线数据: {len(bars)}根")

        # 分析K线
        analysis = adapter.analyze_bars(bars)
        print(f"\nK线分析结果:")
        print(f"数据量: {analysis.get('bar_count', 0)}")
        print(f"最新价格: {analysis.get('latest_price', 0):.4f}")

        if 'ma_results' in analysis:
            print("技术指标:")
            for ma_name, ma_value in analysis['ma_results'].items():
                print(f"  {ma_name}: {ma_value:.4f}")

        # 生成交易信号
        signal_result = adapter.generate_trading_signal(bars)
        print(f"\n交易信号:")
        print(f"信号: {signal_result.get('signal', 'NONE')}")
        print(f"置信度: {signal_result.get('confidence', 0):.2f}")

        if 'support_info' in signal_result:
            support = signal_result['support_info']
            if 'rsi' in support:
                print(f"RSI: {support['rsi']:.2f} ({support.get('rsi_level', 'unknown')})")
            if 'ma20_value' in support:
                print(f"MA20: {support['ma20_value']:.4f} ({support.get('ma20_position', 'unknown')})")

        # 性能统计
        perf_stats = adapter.get_performance_stats()
        if 'total_calculations' in perf_stats:
            print(f"\n适配器性能:")
            print(f"计算次数: {perf_stats['total_calculations']}")
            print(f"GPU模式: {perf_stats['gpu_info']['performance_mode']}")

        return True

    except Exception as e:
        print(f"VNPY集成演示失败: {str(e)}")
        return False

def main():
    """主演示函数"""
    print("阿童木V100信号生成器完整演示")
    print("=" * 60)

    # 检查工作目录
    print(f"工作目录: {os.getcwd()}")

    # 检查DLL文件
    dll_files = [
        "astroboy_signal_simple.dll",
        "astroboy_working.dll",
        "atomboy_signal.dll"
    ]

    available_dlls = [dll for dll in dll_files if os.path.exists(dll)]
    print(f"可用DLL文件: {available_dlls}")

    if not available_dlls:
        print("错误: 未找到DLL文件，请先编译")
        return

    # 运行演示
    success_count = 0

    if demo_basic_functions():
        success_count += 1

    if demo_performance_test():
        success_count += 1

    if demo_vnpy_integration():
        success_count += 1

    print(f"\n=== 演示完成 ===")
    print(f"成功演示: {success_count}/3")

    if success_count == 3:
        print("✓ 阿童木V100信号生成器完全就绪!")
        print("\n可以在VNPY策略中使用:")
        print("from vnpy_astroboy_integration import VNPYAstroboyAdapter")
        print("adapter = VNPYAstroboyAdapter()")
        print("signal = adapter.generate_trading_signal(bars)")
    else:
        print("部分功能可能存在问题，请检查日志")

if __name__ == "__main__":
    main()
