#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
阿童木完整ML/RL信号生成器演示
展示机器学习推理、强化学习、深度学习模型的完整功能
"""

import os
import sys
import numpy as np
import time
import json
from pathlib import Path

# 添加当前目录到路径
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

def demo_ml_features_and_prediction():
    """演示ML特征提取和预测"""
    print("=== 机器学习特征提取和预测演示 ===")
    
    try:
        from astroboy_ml_rl_complete import (
            AstroboyMLEngine, MLModelType, create_ml_engine
        )
        
        # 创建ML引擎
        engine = create_ml_engine()
        
        print(f"ML引擎初始化成功")
        print(f"可用模型: {list(engine.models.keys())}")
        
        # 生成模拟市场数据
        print(f"\n生成模拟市场数据...")
        np.random.seed(42)
        market_data = []
        base_price = 100.0
        
        for i in range(100):
            # 添加趋势和噪声
            trend = 0.001 * i
            noise = np.random.normal(0, 0.02)
            price_change = trend + noise
            base_price *= (1 + price_change)
            
            high = base_price * (1 + abs(np.random.normal(0, 0.01)))
            low = base_price * (1 - abs(np.random.normal(0, 0.01)))
            volume = np.random.uniform(1000, 10000)
            
            market_data.append({
                'high': high,
                'low': low,
                'close': base_price,
                'volume': volume
            })
        
        print(f"生成数据: {len(market_data)}条")
        print(f"价格范围: {min(d['close'] for d in market_data):.2f} - {max(d['close'] for d in market_data):.2f}")
        
        # 特征提取
        print(f"\n=== ML特征提取 ===")
        features = engine.extract_features(market_data)
        
        print(f"特征维度: {features.features.shape}")
        print(f"特征名称: {features.feature_names[:10]}...")  # 显示前10个
        print(f"特征值示例: {features.features[:5]}")
        
        # 测试不同模型的预测
        print(f"\n=== 多模型预测对比 ===")
        model_types = ['random_forest', 'mlp', 'lightgbm']
        
        if engine.models.get('pytorch_nn'):
            model_types.append('pytorch_nn')
        
        predictions = {}
        for model_type in model_types:
            try:
                start_time = time.time()
                prediction = engine.predict_with_ml(features, model_type)
                end_time = time.time()
                
                predictions[model_type] = prediction
                
                print(f"\n{model_type.upper()}模型:")
                print(f"  预测类别: {prediction.predicted_class} ({'下跌' if prediction.predicted_class == 0 else '持平' if prediction.predicted_class == 1 else '上涨'})")
                print(f"  置信度: {prediction.confidence:.4f}")
                print(f"  概率分布: 下跌={prediction.probabilities[0]:.3f}, 持平={prediction.probabilities[1]:.3f}, 上涨={prediction.probabilities[2]:.3f}")
                print(f"  预期收益: {prediction.expected_return:.4f}")
                print(f"  风险评分: {prediction.risk_score:.4f}")
                print(f"  预测时间: {(end_time-start_time)*1000:.2f}ms")
                
            except Exception as e:
                print(f"{model_type}模型预测失败: {str(e)}")
        
        # 模型一致性分析
        print(f"\n=== 模型一致性分析 ===")
        predicted_classes = [pred.predicted_class for pred in predictions.values()]
        avg_confidence = np.mean([pred.confidence for pred in predictions.values()])
        
        print(f"预测类别: {predicted_classes}")
        print(f"平均置信度: {avg_confidence:.4f}")
        
        # 计算一致性
        if len(set(predicted_classes)) == 1:
            print(f"✓ 所有模型预测一致: {predicted_classes[0]}")
        else:
            print(f"⚠️ 模型预测不一致，需要进一步分析")
        
        return True
        
    except Exception as e:
        print(f"ML演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def demo_reinforcement_learning():
    """演示强化学习功能"""
    print(f"\n=== 强化学习演示 ===")
    
    try:
        from astroboy_ml_rl_complete import (
            AstroboyMLEngine, RLActionType, create_ml_engine
        )
        
        engine = create_ml_engine()
        
        # 生成模拟交易环境数据
        print(f"生成模拟交易环境...")
        np.random.seed(123)
        market_data = []
        base_price = 100.0
        
        # 模拟不同市场状态
        market_phases = [
            ('上升趋势', 50, 0.002, 0.015),
            ('震荡市场', 30, 0.0, 0.025),
            ('下降趋势', 40, -0.001, 0.02)
        ]
        
        for phase_name, length, trend, volatility in market_phases:
            print(f"  生成{phase_name}数据: {length}条")
            for i in range(length):
                noise = np.random.normal(0, volatility)
                price_change = trend + noise
                base_price *= (1 + price_change)
                
                high = base_price * (1 + abs(np.random.normal(0, 0.01)))
                low = base_price * (1 - abs(np.random.normal(0, 0.01)))
                volume = np.random.uniform(1000, 10000)
                
                market_data.append({
                    'high': high,
                    'low': low,
                    'close': base_price,
                    'volume': volume
                })
        
        print(f"总数据量: {len(market_data)}条")
        
        # 模拟交易过程
        print(f"\n=== 强化学习交易模拟 ===")
        
        portfolio_value = 100000.0  # 初始资金
        current_position = 0.0      # 当前仓位
        trade_history = []
        
        # 每10条数据进行一次决策
        decision_points = range(50, len(market_data), 10)
        
        for i, decision_point in enumerate(decision_points[:10]):  # 只演示前10个决策点
            # 获取历史数据
            historical_data = market_data[decision_point-50:decision_point]
            current_price = historical_data[-1]['close']
            
            # 提取RL状态
            state = engine.extract_rl_state(
                historical_data, 
                current_position, 
                portfolio_value
            )
            
            # 选择动作
            action = engine.select_rl_action(state)
            
            # 执行交易
            old_position = current_position
            
            if action.action_type == RLActionType.BUY and current_position < 0.8:
                # 买入
                position_change = min(action.action_size, 0.8 - current_position)
                current_position += position_change
                cost = position_change * portfolio_value * current_price / 100000
                
                trade_info = {
                    'step': i+1,
                    'action': 'BUY',
                    'price': current_price,
                    'position_change': position_change,
                    'new_position': current_position,
                    'confidence': action.confidence
                }
                
            elif action.action_type == RLActionType.SELL and current_position > -0.8:
                # 卖出
                position_change = min(action.action_size, current_position + 0.8)
                current_position -= position_change
                
                trade_info = {
                    'step': i+1,
                    'action': 'SELL',
                    'price': current_price,
                    'position_change': -position_change,
                    'new_position': current_position,
                    'confidence': action.confidence
                }
                
            else:
                # 持有
                trade_info = {
                    'step': i+1,
                    'action': 'HOLD',
                    'price': current_price,
                    'position_change': 0.0,
                    'new_position': current_position,
                    'confidence': action.confidence
                }
            
            trade_history.append(trade_info)
            
            # 更新组合价值（简化计算）
            if old_position != current_position:
                portfolio_value *= (1 + (current_position - old_position) * 0.001)  # 假设小幅收益
            
            # 显示交易信息
            print(f"步骤 {i+1:2d}: {trade_info['action']:4s} | "
                  f"价格: {current_price:7.2f} | "
                  f"仓位: {current_position:6.3f} | "
                  f"置信度: {action.confidence:6.3f} | "
                  f"Q值: [{action.q_values[0]:.2f}, {action.q_values[1]:.2f}, {action.q_values[2]:.2f}]")
        
        # 交易统计
        print(f"\n=== 交易统计 ===")
        actions = [trade['action'] for trade in trade_history]
        action_counts = {action: actions.count(action) for action in ['BUY', 'SELL', 'HOLD']}
        
        print(f"交易次数统计:")
        for action, count in action_counts.items():
            percentage = count / len(trade_history) * 100
            print(f"  {action}: {count}次 ({percentage:.1f}%)")
        
        print(f"最终仓位: {current_position:.3f}")
        print(f"组合价值: {portfolio_value:.2f}")
        
        # 计算平均置信度
        avg_confidence = np.mean([trade['confidence'] for trade in trade_history])
        print(f"平均决策置信度: {avg_confidence:.4f}")
        
        return True
        
    except Exception as e:
        print(f"强化学习演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def demo_model_performance_comparison():
    """演示模型性能对比"""
    print(f"\n=== 模型性能对比演示 ===")
    
    try:
        from astroboy_ml_rl_complete import create_ml_engine
        
        engine = create_ml_engine()
        
        # 生成大量测试数据
        print(f"生成性能测试数据...")
        np.random.seed(456)
        test_datasets = []
        
        for dataset_id in range(5):
            market_data = []
            base_price = 100.0 + dataset_id * 10
            
            for i in range(200):
                trend = np.random.choice([-0.001, 0.0, 0.001])
                noise = np.random.normal(0, 0.02)
                price_change = trend + noise
                base_price *= (1 + price_change)
                
                high = base_price * (1 + abs(np.random.normal(0, 0.01)))
                low = base_price * (1 - abs(np.random.normal(0, 0.01)))
                volume = np.random.uniform(1000, 10000)
                
                market_data.append({
                    'high': high,
                    'low': low,
                    'close': base_price,
                    'volume': volume
                })
            
            test_datasets.append(market_data)
        
        print(f"生成 {len(test_datasets)} 个测试数据集")
        
        # 性能测试
        model_types = ['random_forest', 'mlp', 'lightgbm']
        performance_results = {}
        
        for model_type in model_types:
            print(f"\n测试 {model_type.upper()} 模型性能...")
            
            predictions = []
            total_time = 0.0
            
            for dataset_id, market_data in enumerate(test_datasets):
                # 特征提取
                features = engine.extract_features(market_data)
                
                # 预测
                start_time = time.time()
                prediction = engine.predict_with_ml(features, model_type)
                end_time = time.time()
                
                predictions.append(prediction)
                total_time += (end_time - start_time)
                
                print(f"  数据集 {dataset_id+1}: 预测={prediction.predicted_class}, 置信度={prediction.confidence:.3f}")
            
            # 计算性能指标
            avg_time = total_time / len(test_datasets)
            avg_confidence = np.mean([pred.confidence for pred in predictions])
            prediction_distribution = {}
            for pred in predictions:
                pred_class = pred.predicted_class
                prediction_distribution[pred_class] = prediction_distribution.get(pred_class, 0) + 1
            
            performance_results[model_type] = {
                'avg_prediction_time': avg_time,
                'avg_confidence': avg_confidence,
                'prediction_distribution': prediction_distribution,
                'total_predictions': len(predictions)
            }
            
            print(f"  平均预测时间: {avg_time*1000:.2f}ms")
            print(f"  平均置信度: {avg_confidence:.4f}")
            print(f"  预测分布: {prediction_distribution}")
        
        # 性能对比总结
        print(f"\n=== 性能对比总结 ===")
        print(f"{'模型':<12} {'平均时间(ms)':<12} {'平均置信度':<12} {'预测稳定性':<12}")
        print(f"{'-'*50}")
        
        for model_type, results in performance_results.items():
            stability = len(results['prediction_distribution']) / 3.0  # 预测类别多样性
            print(f"{model_type:<12} {results['avg_prediction_time']*1000:<12.2f} "
                  f"{results['avg_confidence']:<12.4f} {stability:<12.2f}")
        
        # 推荐最佳模型
        best_model = min(performance_results.keys(), 
                        key=lambda x: performance_results[x]['avg_prediction_time'])
        most_confident = max(performance_results.keys(), 
                           key=lambda x: performance_results[x]['avg_confidence'])
        
        print(f"\n推荐:")
        print(f"  最快模型: {best_model}")
        print(f"  最高置信度: {most_confident}")
        
        return True
        
    except Exception as e:
        print(f"性能对比演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主演示函数"""
    print("阿童木完整ML/RL信号生成器演示")
    print("=" * 70)
    
    # 检查工作目录和文件
    print(f"工作目录: {os.getcwd()}")
    
    dll_files = [
        "astroboy_signal_advanced.dll",
        "astroboy_signal_simple.dll"
    ]
    
    available_dlls = [dll for dll in dll_files if os.path.exists(dll)]
    print(f"可用DLL文件: {available_dlls}")
    
    if not available_dlls:
        print("错误: 未找到DLL文件，请先编译")
        return
    
    # 运行演示
    success_count = 0
    total_demos = 3
    
    print(f"\n开始演示 {total_demos} 个ML/RL功能模块...")
    
    # 1. ML特征提取和预测演示
    if demo_ml_features_and_prediction():
        success_count += 1
        print("✓ ML特征提取和预测演示成功")
    else:
        print("✗ ML特征提取和预测演示失败")
    
    # 2. 强化学习演示
    if demo_reinforcement_learning():
        success_count += 1
        print("✓ 强化学习演示成功")
    else:
        print("✗ 强化学习演示失败")
    
    # 3. 模型性能对比演示
    if demo_model_performance_comparison():
        success_count += 1
        print("✓ 模型性能对比演示成功")
    else:
        print("✗ 模型性能对比演示失败")
    
    # 总结
    print(f"\n" + "=" * 70)
    print(f"ML/RL演示完成: {success_count}/{total_demos} 个模块成功")
    
    if success_count == total_demos:
        print("🎉 阿童木完整ML/RL信号生成器完全就绪!")
        print("\n🧠 ML/RL核心特性:")
        print("  🤖 多模型机器学习预测 (RandomForest, MLP, LightGBM, PyTorch)")
        print("  🎯 强化学习动作选择 (DQN)")
        print("  📊 特征工程和提取")
        print("  ⚡ 实时预测和决策")
        print("  📈 模型性能监控")
        print("  🔄 自适应学习机制")
        
        print("\n🔧 在VNPY中使用:")
        print("  from astroboy_ml_rl_complete import AstroboyMLEngine")
        print("  engine = AstroboyMLEngine()")
        print("  features = engine.extract_features(market_data)")
        print("  prediction = engine.predict_with_ml(features, 'random_forest')")
        print("  state = engine.extract_rl_state(market_data, position, portfolio)")
        print("  action = engine.select_rl_action(state)")
        
        print("\n📊 性能优势:")
        print("  - 特征提取: 自动化技术指标特征工程")
        print("  - ML预测: 多模型集成预测，提高准确性")
        print("  - RL决策: 基于环境状态的智能动作选择")
        print("  - 实时性: 毫秒级预测和决策响应")
        
    else:
        print("⚠️ 部分ML/RL功能存在问题，请检查日志")
        print("建议:")
        print("  1. 确保所有ML库已安装 (sklearn, lightgbm, torch)")
        print("  2. 检查DLL文件完整性")
        print("  3. 验证数据格式")

if __name__ == "__main__":
    main()
