/**
 * @file enhanced_signal_algorithms.cpp
 * @brief 增强版信号生成算法
 * <AUTHOR>
 */

#include "atomboy_signal.h"
#include <vector>
#include <algorithm>
#include <cmath>
#include <numeric>

namespace astroboy {
namespace enhanced {

/**
 * @brief 增强版MACD信号生成器
 */
class MACDSignalGenerator {
private:
    struct MACDData {
        double dif;      // DIF线
        double dea;      // DEA线  
        double macd;     // MACD柱
    };
    
    std::vector<MACDData> macdHistory_;
    
public:
    /**
     * @brief 计算MACD指标
     */
    MACDData calculateMACD(const std::vector<PriceData>& prices, 
                          int fastPeriod = 12, int slowPeriod = 26, int signalPeriod = 9) {
        if (prices.size() < slowPeriod) {
            return {0.0, 0.0, 0.0};
        }
        
        // 计算EMA
        auto calculateEMA = [](const std::vector<double>& values, int period) -> double {
            if (values.size() < period) return 0.0;
            
            double multiplier = 2.0 / (period + 1);
            double ema = values[values.size() - period];
            
            for (size_t i = values.size() - period + 1; i < values.size(); i++) {
                ema = (values[i] * multiplier) + (ema * (1 - multiplier));
            }
            return ema;
        };
        
        // 提取收盘价
        std::vector<double> closes;
        for (const auto& price : prices) {
            closes.push_back(price.close);
        }
        
        // 计算快慢EMA
        double fastEMA = calculateEMA(closes, fastPeriod);
        double slowEMA = calculateEMA(closes, slowPeriod);
        
        // 计算DIF
        double dif = fastEMA - slowEMA;
        
        // 计算DEA (DIF的EMA)
        std::vector<double> difHistory;
        for (const auto& macd : macdHistory_) {
            difHistory.push_back(macd.dif);
        }
        difHistory.push_back(dif);
        
        double dea = calculateEMA(difHistory, signalPeriod);
        
        // 计算MACD柱
        double macd = (dif - dea) * 2;
        
        MACDData result = {dif, dea, macd};
        macdHistory_.push_back(result);
        
        // 保持历史数据不超过1000条
        if (macdHistory_.size() > 1000) {
            macdHistory_.erase(macdHistory_.begin());
        }
        
        return result;
    }
    
    /**
     * @brief 生成MACD信号
     */
    Signal generateMACDSignal(const std::vector<PriceData>& prices) {
        if (prices.size() < 2) {
            return Signal(NONE, STRENGTH_NONE, 0.0, 0.0, 0);
        }
        
        MACDData current = calculateMACD(prices);
        
        if (macdHistory_.size() < 2) {
            return Signal(HOLD, WEAK, 0.3, prices.back().close, prices.back().timestamp);
        }
        
        MACDData previous = macdHistory_[macdHistory_.size() - 2];
        
        SignalType type = NONE;
        SignalStrength strength = STRENGTH_NONE;
        double confidence = 0.0;
        
        // MACD金叉：MACD柱从负转正
        if (previous.macd <= 0 && current.macd > 0) {
            type = BUY;
            // 根据DIF和DEA的位置判断强度
            if (current.dif > 0 && current.dea > 0) {
                strength = STRONG;
                confidence = 0.85;
            } else {
                strength = MEDIUM;
                confidence = 0.65;
            }
        }
        // MACD死叉：MACD柱从正转负
        else if (previous.macd >= 0 && current.macd < 0) {
            type = SELL;
            if (current.dif < 0 && current.dea < 0) {
                strength = STRONG;
                confidence = 0.85;
            } else {
                strength = MEDIUM;
                confidence = 0.65;
            }
        }
        // 趋势延续
        else if (current.macd > 0 && current.macd > previous.macd) {
            type = BUY;
            strength = WEAK;
            confidence = 0.4;
        }
        else if (current.macd < 0 && current.macd < previous.macd) {
            type = SELL;
            strength = WEAK;
            confidence = 0.4;
        }
        else {
            type = HOLD;
            strength = WEAK;
            confidence = 0.2;
        }
        
        return Signal(type, strength, confidence, prices.back().close, prices.back().timestamp);
    }
};

/**
 * @brief 增强版布林带信号生成器
 */
class BollingerBandsSignalGenerator {
public:
    struct BollingerBands {
        double upper;    // 上轨
        double middle;   // 中轨(MA)
        double lower;    // 下轨
        double width;    // 带宽
        double position; // 价格在带中的位置(0-1)
    };
    
    /**
     * @brief 计算布林带
     */
    BollingerBands calculateBollingerBands(const std::vector<PriceData>& prices, 
                                          int period = 20, double stdMultiplier = 2.0) {
        if (prices.size() < period) {
            return {0.0, 0.0, 0.0, 0.0, 0.5};
        }
        
        // 计算中轨(简单移动平均)
        double sum = 0.0;
        for (size_t i = prices.size() - period; i < prices.size(); i++) {
            sum += prices[i].close;
        }
        double middle = sum / period;
        
        // 计算标准差
        double variance = 0.0;
        for (size_t i = prices.size() - period; i < prices.size(); i++) {
            double diff = prices[i].close - middle;
            variance += diff * diff;
        }
        double stdDev = std::sqrt(variance / period);
        
        // 计算上下轨
        double upper = middle + (stdMultiplier * stdDev);
        double lower = middle - (stdMultiplier * stdDev);
        
        // 计算带宽
        double width = (upper - lower) / middle;
        
        // 计算价格位置
        double currentPrice = prices.back().close;
        double position = (currentPrice - lower) / (upper - lower);
        position = std::max(0.0, std::min(1.0, position));
        
        return {upper, middle, lower, width, position};
    }
    
    /**
     * @brief 生成布林带信号
     */
    Signal generateBollingerSignal(const std::vector<PriceData>& prices) {
        if (prices.size() < 21) {
            return Signal(NONE, STRENGTH_NONE, 0.0, 0.0, 0);
        }
        
        BollingerBands current = calculateBollingerBands(prices);
        double currentPrice = prices.back().close;
        
        SignalType type = NONE;
        SignalStrength strength = STRENGTH_NONE;
        double confidence = 0.0;
        
        // 价格触及下轨，超卖信号
        if (currentPrice <= current.lower) {
            type = BUY;
            // 根据突破程度判断强度
            double penetration = (current.lower - currentPrice) / current.lower;
            if (penetration > 0.02) {
                strength = STRONG;
                confidence = 0.8;
            } else if (penetration > 0.01) {
                strength = MEDIUM;
                confidence = 0.6;
            } else {
                strength = WEAK;
                confidence = 0.4;
            }
        }
        // 价格触及上轨，超买信号
        else if (currentPrice >= current.upper) {
            type = SELL;
            double penetration = (currentPrice - current.upper) / current.upper;
            if (penetration > 0.02) {
                strength = STRONG;
                confidence = 0.8;
            } else if (penetration > 0.01) {
                strength = MEDIUM;
                confidence = 0.6;
            } else {
                strength = WEAK;
                confidence = 0.4;
            }
        }
        // 价格在中轨附近，趋势信号
        else if (std::abs(currentPrice - current.middle) / current.middle < 0.005) {
            // 根据最近价格走势判断
            if (prices.size() >= 3) {
                double trend = (prices.back().close - prices[prices.size()-3].close) / prices[prices.size()-3].close;
                if (trend > 0.01) {
                    type = BUY;
                    strength = WEAK;
                    confidence = 0.3;
                } else if (trend < -0.01) {
                    type = SELL;
                    strength = WEAK;
                    confidence = 0.3;
                } else {
                    type = HOLD;
                    strength = WEAK;
                    confidence = 0.2;
                }
            }
        }
        // 其他情况保持观望
        else {
            type = HOLD;
            strength = WEAK;
            confidence = 0.2;
        }
        
        return Signal(type, strength, confidence, currentPrice, prices.back().timestamp);
    }
};

/**
 * @brief 增强版KDJ信号生成器
 */
class KDJSignalGenerator {
private:
    struct KDJData {
        double k;
        double d;
        double j;
    };
    
    std::vector<KDJData> kdjHistory_;
    
public:
    /**
     * @brief 计算KDJ指标
     */
    KDJData calculateKDJ(const std::vector<PriceData>& prices, 
                        int period = 9, int kPeriod = 3, int dPeriod = 3) {
        if (prices.size() < period) {
            return {50.0, 50.0, 50.0};
        }
        
        // 计算RSV
        double highest = prices[prices.size() - period].high;
        double lowest = prices[prices.size() - period].low;
        
        for (size_t i = prices.size() - period + 1; i < prices.size(); i++) {
            highest = std::max(highest, prices[i].high);
            lowest = std::min(lowest, prices[i].low);
        }
        
        double currentClose = prices.back().close;
        double rsv = 0.0;
        if (highest != lowest) {
            rsv = (currentClose - lowest) / (highest - lowest) * 100.0;
        } else {
            rsv = 50.0;
        }
        
        // 计算K值
        double k = 50.0;
        if (!kdjHistory_.empty()) {
            k = (rsv + (kPeriod - 1) * kdjHistory_.back().k) / kPeriod;
        } else {
            k = rsv;
        }
        
        // 计算D值
        double d = 50.0;
        if (!kdjHistory_.empty()) {
            d = (k + (dPeriod - 1) * kdjHistory_.back().d) / dPeriod;
        } else {
            d = k;
        }
        
        // 计算J值
        double j = 3 * k - 2 * d;
        
        KDJData result = {k, d, j};
        kdjHistory_.push_back(result);
        
        // 保持历史数据不超过1000条
        if (kdjHistory_.size() > 1000) {
            kdjHistory_.erase(kdjHistory_.begin());
        }
        
        return result;
    }
    
    /**
     * @brief 生成KDJ信号
     */
    Signal generateKDJSignal(const std::vector<PriceData>& prices) {
        if (prices.size() < 10) {
            return Signal(NONE, STRENGTH_NONE, 0.0, 0.0, 0);
        }
        
        KDJData current = calculateKDJ(prices);
        
        SignalType type = NONE;
        SignalStrength strength = STRENGTH_NONE;
        double confidence = 0.0;
        
        // KDJ超卖区域(K<20, D<20)
        if (current.k < 20 && current.d < 20) {
            type = BUY;
            if (current.k < 10 && current.d < 10) {
                strength = STRONG;
                confidence = 0.8;
            } else {
                strength = MEDIUM;
                confidence = 0.6;
            }
        }
        // KDJ超买区域(K>80, D>80)
        else if (current.k > 80 && current.d > 80) {
            type = SELL;
            if (current.k > 90 && current.d > 90) {
                strength = STRONG;
                confidence = 0.8;
            } else {
                strength = MEDIUM;
                confidence = 0.6;
            }
        }
        // KDJ金叉(K上穿D)
        else if (kdjHistory_.size() >= 2) {
            KDJData previous = kdjHistory_[kdjHistory_.size() - 2];
            if (previous.k <= previous.d && current.k > current.d) {
                type = BUY;
                strength = MEDIUM;
                confidence = 0.5;
            }
            // KDJ死叉(K下穿D)
            else if (previous.k >= previous.d && current.k < current.d) {
                type = SELL;
                strength = MEDIUM;
                confidence = 0.5;
            }
            else {
                type = HOLD;
                strength = WEAK;
                confidence = 0.3;
            }
        }
        else {
            type = HOLD;
            strength = WEAK;
            confidence = 0.3;
        }
        
        return Signal(type, strength, confidence, prices.back().close, prices.back().timestamp);
    }
};

} // namespace enhanced
} // namespace astroboy
