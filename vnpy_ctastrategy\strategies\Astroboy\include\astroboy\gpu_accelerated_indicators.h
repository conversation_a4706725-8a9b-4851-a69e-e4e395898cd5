/**
 * @file gpu_accelerated_indicators.h
 * @brief V100 GPU加速技术指标计算
 * <AUTHOR> - V100优化版
 */

#ifndef ASTROBOY_GPU_ACCELERATED_INDICATORS_H
#define ASTROBOY_GPU_ACCELERATED_INDICATORS_H

#include <vector>
#include <memory>
#include <cuda_runtime.h>
#include <cublas_v2.h>
#include <curand.h>
#include <thrust/device_vector.h>
#include <thrust/host_vector.h>
#include <thrust/transform.h>
#include <thrust/reduce.h>
#include <thrust/functional.h>

namespace astroboy {
namespace gpu {

/**
 * @brief V100 GPU加速管理器
 */
class V100AcceleratorManager {
private:
    static V100AcceleratorManager* instance_;
    cudaStream_t stream_;
    cublasHandle_t cublas_handle_;
    curandGenerator_t curand_gen_;
    
    // GPU内存池
    std::vector<void*> memory_pool_;
    std::vector<size_t> memory_sizes_;
    
    // 性能统计
    struct PerformanceStats {
        double total_gpu_time = 0.0;
        double total_cpu_time = 0.0;
        int gpu_operations = 0;
        int cpu_operations = 0;
        double speedup_ratio = 1.0;
    } perf_stats_;
    
public:
    static V100AcceleratorManager* getInstance();
    
    /**
     * @brief 初始化V100加速器
     */
    bool initialize();
    
    /**
     * @brief 清理资源
     */
    void cleanup();
    
    /**
     * @brief 获取CUDA流
     */
    cudaStream_t getStream() const { return stream_; }
    
    /**
     * @brief 获取cuBLAS句柄
     */
    cublasHandle_t getCublasHandle() const { return cublas_handle_; }
    
    /**
     * @brief 分配GPU内存
     */
    void* allocateGPUMemory(size_t size);
    
    /**
     * @brief 释放GPU内存
     */
    void freeGPUMemory(void* ptr);
    
    /**
     * @brief 获取性能统计
     */
    PerformanceStats getPerformanceStats() const { return perf_stats_; }
    
    /**
     * @brief 更新性能统计
     */
    void updatePerformanceStats(double gpu_time, double cpu_time);
    
    /**
     * @brief 检查V100可用性
     */
    static bool isV100Available();
    
    /**
     * @brief 获取GPU信息
     */
    static std::string getGPUInfo();
};

/**
 * @brief GPU加速的价格数据结构
 */
struct GPUPriceData {
    float open;
    float high;
    float low;
    float close;
    float volume;
    long long timestamp;
    
    __host__ __device__
    GPUPriceData() : open(0), high(0), low(0), close(0), volume(0), timestamp(0) {}
    
    __host__ __device__
    GPUPriceData(float o, float h, float l, float c, float v, long long t)
        : open(o), high(h), low(l), close(c), volume(v), timestamp(t) {}
};

/**
 * @brief V100加速的技术指标计算器
 */
class V100TechnicalIndicators {
private:
    V100AcceleratorManager* accelerator_;
    
    // GPU内存缓存
    thrust::device_vector<float> d_prices_;
    thrust::device_vector<float> d_volumes_;
    thrust::device_vector<float> d_results_;
    thrust::device_vector<float> d_temp_;
    
public:
    V100TechnicalIndicators();
    ~V100TechnicalIndicators();
    
    /**
     * @brief 批量计算移动平均线
     * @param prices 价格数据
     * @param periods 周期数组
     * @param results 结果数组
     */
    bool calculateMovingAveragesBatch(const std::vector<float>& prices,
                                     const std::vector<int>& periods,
                                     std::vector<float>& results);
    
    /**
     * @brief V100加速RSI计算
     * @param prices 价格数据
     * @param period RSI周期
     * @param results RSI结果数组
     */
    bool calculateRSIBatch(const std::vector<float>& prices,
                          int period,
                          std::vector<float>& results);
    
    /**
     * @brief V100加速MACD计算
     * @param prices 价格数据
     * @param fast_period 快线周期
     * @param slow_period 慢线周期
     * @param signal_period 信号线周期
     * @param dif_results DIF结果
     * @param dea_results DEA结果
     * @param macd_results MACD结果
     */
    bool calculateMACDBatch(const std::vector<float>& prices,
                           int fast_period, int slow_period, int signal_period,
                           std::vector<float>& dif_results,
                           std::vector<float>& dea_results,
                           std::vector<float>& macd_results);
    
    /**
     * @brief V100加速布林带计算
     * @param prices 价格数据
     * @param period 周期
     * @param std_multiplier 标准差倍数
     * @param upper_results 上轨结果
     * @param middle_results 中轨结果
     * @param lower_results 下轨结果
     */
    bool calculateBollingerBandsBatch(const std::vector<float>& prices,
                                     int period, float std_multiplier,
                                     std::vector<float>& upper_results,
                                     std::vector<float>& middle_results,
                                     std::vector<float>& lower_results);
    
    /**
     * @brief V100加速KDJ计算
     * @param high_prices 最高价数据
     * @param low_prices 最低价数据
     * @param close_prices 收盘价数据
     * @param period K线周期
     * @param k_period K值平滑周期
     * @param d_period D值平滑周期
     * @param k_results K值结果
     * @param d_results D值结果
     * @param j_results J值结果
     */
    bool calculateKDJBatch(const std::vector<float>& high_prices,
                          const std::vector<float>& low_prices,
                          const std::vector<float>& close_prices,
                          int period, int k_period, int d_period,
                          std::vector<float>& k_results,
                          std::vector<float>& d_results,
                          std::vector<float>& j_results);
    
    /**
     * @brief V100加速波动率计算
     * @param prices 价格数据
     * @param period 计算周期
     * @param results 波动率结果
     */
    bool calculateVolatilityBatch(const std::vector<float>& prices,
                                 int period,
                                 std::vector<float>& results);
    
    /**
     * @brief V100加速相关性计算
     * @param prices1 价格序列1
     * @param prices2 价格序列2
     * @param window_size 滑动窗口大小
     * @param correlations 相关性结果
     */
    bool calculateCorrelationBatch(const std::vector<float>& prices1,
                                  const std::vector<float>& prices2,
                                  int window_size,
                                  std::vector<float>& correlations);
    
    /**
     * @brief 批量技术指标计算（一次性计算多个指标）
     * @param price_data 价格数据
     * @param indicator_config 指标配置
     * @param results 所有指标结果
     */
    struct IndicatorConfig {
        std::vector<int> ma_periods = {5, 10, 20, 50};
        int rsi_period = 14;
        struct {
            int fast = 12;
            int slow = 26;
            int signal = 9;
        } macd;
        struct {
            int period = 20;
            float std_mult = 2.0f;
        } bollinger;
        struct {
            int period = 9;
            int k_period = 3;
            int d_period = 3;
        } kdj;
        int volatility_period = 20;
    };
    
    struct AllIndicatorResults {
        std::vector<std::vector<float>> ma_results;  // 多周期MA
        std::vector<float> rsi_results;
        std::vector<float> macd_dif, macd_dea, macd_histogram;
        std::vector<float> bb_upper, bb_middle, bb_lower;
        std::vector<float> kdj_k, kdj_d, kdj_j;
        std::vector<float> volatility_results;
    };
    
    bool calculateAllIndicatorsBatch(const std::vector<GPUPriceData>& price_data,
                                    const IndicatorConfig& config,
                                    AllIndicatorResults& results);
    
    /**
     * @brief 获取计算性能统计
     */
    struct ComputeStats {
        double last_gpu_time;
        double last_cpu_time;
        double speedup_ratio;
        int total_operations;
    };
    
    ComputeStats getComputeStats() const;
    
private:
    /**
     * @brief 确保GPU内存足够
     */
    void ensureGPUMemory(size_t required_size);
    
    /**
     * @brief 同步GPU计算
     */
    void synchronizeGPU();
    
    /**
     * @brief 错误检查
     */
    bool checkCudaError(const char* operation);
};

/**
 * @brief V100加速的特征工程
 */
class V100FeatureEngineering {
private:
    V100AcceleratorManager* accelerator_;
    V100TechnicalIndicators* indicators_;
    
    // GPU特征缓存
    thrust::device_vector<float> d_features_;
    thrust::device_vector<float> d_normalized_features_;
    
public:
    V100FeatureEngineering();
    ~V100FeatureEngineering();
    
    /**
     * @brief 特征向量结构
     */
    struct FeatureVector {
        std::vector<float> technical_features;
        std::vector<float> price_features;
        std::vector<float> volume_features;
        std::vector<float> volatility_features;
        std::vector<float> pattern_features;
        
        // 获取所有特征
        std::vector<float> getAllFeatures() const;
        
        // 特征标准化
        void normalize();
        
        // 特征选择
        void selectTopFeatures(int top_k);
    };
    
    /**
     * @brief V100加速特征提取
     * @param price_data 价格数据
     * @param lookback_period 回看周期
     * @param features 输出特征向量
     */
    bool extractFeaturesBatch(const std::vector<GPUPriceData>& price_data,
                             int lookback_period,
                             std::vector<FeatureVector>& features);
    
    /**
     * @brief 批量特征标准化
     * @param features 特征矩阵
     * @param normalized_features 标准化后的特征
     */
    bool normalizeFeaturesBatch(const std::vector<std::vector<float>>& features,
                               std::vector<std::vector<float>>& normalized_features);
    
    /**
     * @brief 特征重要性计算
     * @param features 特征矩阵
     * @param labels 标签
     * @param importance_scores 重要性分数
     */
    bool calculateFeatureImportance(const std::vector<std::vector<float>>& features,
                                   const std::vector<int>& labels,
                                   std::vector<float>& importance_scores);
    
    /**
     * @brief PCA降维
     * @param features 原始特征
     * @param n_components 主成分数量
     * @param reduced_features 降维后特征
     */
    bool performPCA(const std::vector<std::vector<float>>& features,
                   int n_components,
                   std::vector<std::vector<float>>& reduced_features);
};

/**
 * @brief V100加速的ML推理引擎
 */
class V100MLInferenceEngine {
private:
    V100AcceleratorManager* accelerator_;
    
    // GPU模型参数
    thrust::device_vector<float> d_weights_;
    thrust::device_vector<float> d_biases_;
    thrust::device_vector<float> d_activations_;
    
public:
    V100MLInferenceEngine();
    ~V100MLInferenceEngine();
    
    /**
     * @brief 神经网络前向传播
     * @param input_features 输入特征
     * @param network_config 网络配置
     * @param predictions 预测结果
     */
    struct NetworkConfig {
        std::vector<int> layer_sizes;
        std::string activation = "relu";
        float dropout_rate = 0.0f;
    };
    
    bool forwardPass(const std::vector<float>& input_features,
                    const NetworkConfig& config,
                    std::vector<float>& predictions);
    
    /**
     * @brief 批量推理
     * @param batch_features 批量特征
     * @param config 网络配置
     * @param batch_predictions 批量预测结果
     */
    bool batchInference(const std::vector<std::vector<float>>& batch_features,
                       const NetworkConfig& config,
                       std::vector<std::vector<float>>& batch_predictions);
    
    /**
     * @brief 加载预训练模型
     * @param model_path 模型路径
     */
    bool loadPretrainedModel(const std::string& model_path);
    
    /**
     * @brief 在线学习更新
     * @param features 新特征
     * @param labels 新标签
     * @param learning_rate 学习率
     */
    bool onlineLearningUpdate(const std::vector<float>& features,
                             const std::vector<float>& labels,
                             float learning_rate);
};

} // namespace gpu
} // namespace astroboy

#endif // ASTROBOY_GPU_ACCELERATED_INDICATORS_H
