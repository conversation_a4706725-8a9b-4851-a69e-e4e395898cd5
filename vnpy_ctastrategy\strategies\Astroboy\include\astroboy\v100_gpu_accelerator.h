/**
 * @file v100_gpu_accelerator.h
 * @brief V100 GPU加速核心引擎
 * <AUTHOR> - V100优化版
 */

#ifndef ASTROBOY_V100_GPU_ACCELERATOR_H
#define ASTROBOY_V100_GPU_ACCELERATOR_H

#include <vector>
#include <memory>
#include <string>
#include <map>
#include <mutex>
#include <chrono>

// 条件编译：如果有CUDA支持
#ifdef ASTROBOY_ENABLE_CUDA
#include <cuda_runtime.h>
#include <cublas_v2.h>
#include <curand.h>
#include <thrust/device_vector.h>
#include <thrust/host_vector.h>
#include <thrust/transform.h>
#include <thrust/reduce.h>
#include <thrust/functional.h>
#endif

namespace astroboy {
namespace gpu {

/**
 * @brief GPU加速的价格数据结构
 */
struct GPUPriceData {
    float open;
    float high;
    float low;
    float close;
    float volume;
    long long timestamp;
    
    GPUPriceData() : open(0), high(0), low(0), close(0), volume(0), timestamp(0) {}
    GPUPriceData(float o, float h, float l, float c, float v, long long t)
        : open(o), high(h), low(l), close(c), volume(v), timestamp(t) {}
};

/**
 * @brief V100性能统计
 */
struct V100PerformanceStats {
    double total_gpu_time = 0.0;
    double total_cpu_time = 0.0;
    int gpu_operations = 0;
    int cpu_operations = 0;
    double speedup_ratio = 1.0;
    double gpu_memory_used = 0.0;
    double gpu_utilization = 0.0;
    
    void updateStats(double gpu_time, double cpu_time) {
        total_gpu_time += gpu_time;
        total_cpu_time += cpu_time;
        gpu_operations++;
        cpu_operations++;
        if (gpu_time > 0) {
            speedup_ratio = total_cpu_time / total_gpu_time;
        }
    }
    
    std::string toString() const {
        char buffer[512];
        snprintf(buffer, sizeof(buffer),
            "V100性能统计:\n"
            "GPU操作: %d次, 总时间: %.4fs\n"
            "CPU操作: %d次, 总时间: %.4fs\n"
            "加速比: %.2fx\n"
            "GPU内存使用: %.2fMB\n"
            "GPU利用率: %.1f%%",
            gpu_operations, total_gpu_time,
            cpu_operations, total_cpu_time,
            speedup_ratio,
            gpu_memory_used / (1024*1024),
            gpu_utilization * 100);
        return std::string(buffer);
    }
};

/**
 * @brief V100 GPU加速管理器
 */
class V100AcceleratorManager {
private:
    static V100AcceleratorManager* instance_;
    static std::mutex instance_mutex_;
    
    bool initialized_ = false;
    bool cuda_available_ = false;
    
#ifdef ASTROBOY_ENABLE_CUDA
    cudaStream_t stream_;
    cublasHandle_t cublas_handle_;
    curandGenerator_t curand_gen_;
    
    // GPU设备信息
    int device_id_ = 0;
    cudaDeviceProp device_prop_;
    
    // 内存管理
    std::vector<void*> memory_pool_;
    std::vector<size_t> memory_sizes_;
    std::mutex memory_mutex_;
#endif
    
    // 性能统计
    V100PerformanceStats perf_stats_;
    std::mutex stats_mutex_;
    
    V100AcceleratorManager() = default;
    
public:
    static V100AcceleratorManager* getInstance();
    ~V100AcceleratorManager();
    
    /**
     * @brief 初始化V100加速器
     */
    bool initialize();
    
    /**
     * @brief 清理资源
     */
    void cleanup();
    
    /**
     * @brief 检查CUDA是否可用
     */
    bool isCudaAvailable() const { return cuda_available_; }
    
    /**
     * @brief 检查是否为V100
     */
    bool isV100() const;
    
    /**
     * @brief 获取GPU信息
     */
    std::string getGPUInfo() const;
    
#ifdef ASTROBOY_ENABLE_CUDA
    /**
     * @brief 获取CUDA流
     */
    cudaStream_t getStream() const { return stream_; }
    
    /**
     * @brief 获取cuBLAS句柄
     */
    cublasHandle_t getCublasHandle() const { return cublas_handle_; }
    
    /**
     * @brief 分配GPU内存
     */
    void* allocateGPUMemory(size_t size);
    
    /**
     * @brief 释放GPU内存
     */
    void freeGPUMemory(void* ptr);
    
    /**
     * @brief 同步GPU
     */
    void synchronize();
#endif
    
    /**
     * @brief 获取性能统计
     */
    V100PerformanceStats getPerformanceStats();
    
    /**
     * @brief 更新性能统计
     */
    void updatePerformanceStats(double gpu_time, double cpu_time);
    
    /**
     * @brief 重置性能统计
     */
    void resetPerformanceStats();
};

/**
 * @brief V100加速的技术指标计算器
 */
class V100TechnicalIndicators {
private:
    V100AcceleratorManager* accelerator_;
    bool use_gpu_;
    
#ifdef ASTROBOY_ENABLE_CUDA
    // GPU内存缓存
    mutable std::vector<float*> gpu_buffers_;
    mutable std::vector<size_t> buffer_sizes_;
    mutable std::mutex buffer_mutex_;
#endif
    
public:
    V100TechnicalIndicators();
    ~V100TechnicalIndicators();
    
    /**
     * @brief 指标配置结构
     */
    struct IndicatorConfig {
        std::vector<int> ma_periods = {5, 10, 20, 50, 200};
        int rsi_period = 14;
        struct {
            int fast = 12;
            int slow = 26;
            int signal = 9;
        } macd;
        struct {
            int period = 20;
            float std_mult = 2.0f;
        } bollinger;
        struct {
            int period = 9;
            int k_period = 3;
            int d_period = 3;
        } kdj;
        std::vector<int> volatility_periods = {10, 20, 50};
    };
    
    /**
     * @brief 所有指标结果
     */
    struct AllIndicatorResults {
        std::vector<std::vector<float>> ma_results;  // 多周期MA
        std::vector<float> rsi_results;
        std::vector<float> macd_dif, macd_dea, macd_histogram;
        std::vector<float> bb_upper, bb_middle, bb_lower;
        std::vector<float> kdj_k, kdj_d, kdj_j;
        std::vector<std::vector<float>> volatility_results;
        
        // 性能统计
        double computation_time = 0.0;
        bool used_gpu = false;
    };
    
    /**
     * @brief 批量计算所有技术指标（V100加速）
     * @param price_data 价格数据
     * @param config 指标配置
     * @param results 计算结果
     * @return 是否成功
     */
    bool calculateAllIndicatorsBatch(const std::vector<GPUPriceData>& price_data,
                                    const IndicatorConfig& config,
                                    AllIndicatorResults& results);
    
    /**
     * @brief 单独计算移动平均线（V100加速）
     */
    bool calculateMovingAveragesBatch(const std::vector<float>& prices,
                                     const std::vector<int>& periods,
                                     std::vector<std::vector<float>>& results);
    
    /**
     * @brief 单独计算RSI（V100加速）
     */
    bool calculateRSIBatch(const std::vector<float>& prices,
                          int period,
                          std::vector<float>& results);
    
    /**
     * @brief 单独计算MACD（V100加速）
     */
    bool calculateMACDBatch(const std::vector<float>& prices,
                           int fast_period, int slow_period, int signal_period,
                           std::vector<float>& dif_results,
                           std::vector<float>& dea_results,
                           std::vector<float>& macd_results);
    
    /**
     * @brief 单独计算布林带（V100加速）
     */
    bool calculateBollingerBandsBatch(const std::vector<float>& prices,
                                     int period, float std_multiplier,
                                     std::vector<float>& upper_results,
                                     std::vector<float>& middle_results,
                                     std::vector<float>& lower_results);
    
    /**
     * @brief 单独计算KDJ（V100加速）
     */
    bool calculateKDJBatch(const std::vector<float>& high_prices,
                          const std::vector<float>& low_prices,
                          const std::vector<float>& close_prices,
                          int period, int k_period, int d_period,
                          std::vector<float>& k_results,
                          std::vector<float>& d_results,
                          std::vector<float>& j_results);
    
    /**
     * @brief 启用/禁用GPU加速
     */
    void setGPUEnabled(bool enabled) { use_gpu_ = enabled && accelerator_->isCudaAvailable(); }
    
    /**
     * @brief 检查是否使用GPU
     */
    bool isGPUEnabled() const { return use_gpu_; }
    
private:
    /**
     * @brief CPU版本的指标计算（备用）
     */
    bool calculateAllIndicatorsCPU(const std::vector<GPUPriceData>& price_data,
                                  const IndicatorConfig& config,
                                  AllIndicatorResults& results);
    
#ifdef ASTROBOY_ENABLE_CUDA
    /**
     * @brief GPU版本的指标计算
     */
    bool calculateAllIndicatorsGPU(const std::vector<GPUPriceData>& price_data,
                                  const IndicatorConfig& config,
                                  AllIndicatorResults& results);
    
    /**
     * @brief 获取GPU缓冲区
     */
    float* getGPUBuffer(size_t size);
    
    /**
     * @brief 清理GPU缓冲区
     */
    void cleanupGPUBuffers();
#endif
};

/**
 * @brief V100加速的特征工程引擎
 */
class V100FeatureEngine {
private:
    V100AcceleratorManager* accelerator_;
    V100TechnicalIndicators* indicators_;
    bool use_gpu_;
    
public:
    V100FeatureEngine();
    ~V100FeatureEngine();
    
    /**
     * @brief 特征向量结构
     */
    struct FeatureVector {
        std::vector<float> technical_features;    // 技术指标特征
        std::vector<float> price_features;       // 价格特征
        std::vector<float> volume_features;      // 成交量特征
        std::vector<float> volatility_features;  // 波动率特征
        std::vector<float> pattern_features;     // 形态特征
        std::vector<float> market_features;      // 市场特征
        
        // 获取所有特征
        std::vector<float> getAllFeatures() const;
        
        // 特征数量
        size_t getFeatureCount() const;
        
        // 清空特征
        void clear();
    };
    
    /**
     * @brief 特征提取配置
     */
    struct FeatureConfig {
        int lookback_period = 50;               // 回看周期
        bool include_technical = true;          // 包含技术指标
        bool include_price = true;              // 包含价格特征
        bool include_volume = true;             // 包含成交量特征
        bool include_volatility = true;         // 包含波动率特征
        bool include_pattern = true;            // 包含形态特征
        bool include_market = true;             // 包含市场特征
        bool normalize_features = true;         // 特征标准化
        int max_features = 100;                 // 最大特征数
    };
    
    /**
     * @brief V100加速特征提取
     * @param price_data 价格数据
     * @param config 特征配置
     * @param features 输出特征向量
     * @return 是否成功
     */
    bool extractFeatures(const std::vector<GPUPriceData>& price_data,
                        const FeatureConfig& config,
                        FeatureVector& features);
    
    /**
     * @brief 批量特征提取
     * @param price_data_batch 批量价格数据
     * @param config 特征配置
     * @param features_batch 批量特征向量
     * @return 是否成功
     */
    bool extractFeaturesBatch(const std::vector<std::vector<GPUPriceData>>& price_data_batch,
                             const FeatureConfig& config,
                             std::vector<FeatureVector>& features_batch);
    
    /**
     * @brief 特征重要性分析
     * @param features_batch 特征批次
     * @param labels 标签
     * @param importance_scores 重要性分数
     * @return 是否成功
     */
    bool analyzeFeatureImportance(const std::vector<FeatureVector>& features_batch,
                                 const std::vector<int>& labels,
                                 std::vector<float>& importance_scores);
    
    /**
     * @brief 启用/禁用GPU加速
     */
    void setGPUEnabled(bool enabled) { use_gpu_ = enabled && accelerator_->isCudaAvailable(); }
    
    /**
     * @brief 检查是否使用GPU
     */
    bool isGPUEnabled() const { return use_gpu_; }
    
private:
    /**
     * @brief 提取技术指标特征
     */
    bool extractTechnicalFeatures(const std::vector<GPUPriceData>& price_data,
                                 const FeatureConfig& config,
                                 std::vector<float>& features);
    
    /**
     * @brief 提取价格特征
     */
    bool extractPriceFeatures(const std::vector<GPUPriceData>& price_data,
                             const FeatureConfig& config,
                             std::vector<float>& features);
    
    /**
     * @brief 提取成交量特征
     */
    bool extractVolumeFeatures(const std::vector<GPUPriceData>& price_data,
                              const FeatureConfig& config,
                              std::vector<float>& features);
    
    /**
     * @brief 提取波动率特征
     */
    bool extractVolatilityFeatures(const std::vector<GPUPriceData>& price_data,
                                  const FeatureConfig& config,
                                  std::vector<float>& features);
    
    /**
     * @brief 提取形态特征
     */
    bool extractPatternFeatures(const std::vector<GPUPriceData>& price_data,
                               const FeatureConfig& config,
                               std::vector<float>& features);
    
    /**
     * @brief 提取市场特征
     */
    bool extractMarketFeatures(const std::vector<GPUPriceData>& price_data,
                              const FeatureConfig& config,
                              std::vector<float>& features);
    
    /**
     * @brief 特征标准化
     */
    bool normalizeFeatures(std::vector<float>& features);
};

} // namespace gpu
} // namespace astroboy

#endif // ASTROBOY_V100_GPU_ACCELERATOR_H
