/**
 * @file ml_signal_enhancement.cpp
 * @brief 机器学习信号增强系统
 * <AUTHOR>
 */

#include "atomboy_signal.h"
#include <vector>
#include <map>
#include <algorithm>
#include <cmath>
#include <memory>

namespace astroboy {
namespace ml {

/**
 * @brief 特征工程类
 */
class FeatureEngineer {
public:
    /**
     * @brief 特征向量结构
     */
    struct FeatureVector {
        std::vector<double> technical_features;    // 技术指标特征
        std::vector<double> price_features;       // 价格特征
        std::vector<double> volume_features;      // 成交量特征
        std::vector<double> volatility_features;  // 波动率特征
        std::vector<double> pattern_features;     // 形态特征
        
        // 合并所有特征
        std::vector<double> getAllFeatures() const {
            std::vector<double> all_features;
            all_features.insert(all_features.end(), technical_features.begin(), technical_features.end());
            all_features.insert(all_features.end(), price_features.begin(), price_features.end());
            all_features.insert(all_features.end(), volume_features.begin(), volume_features.end());
            all_features.insert(all_features.end(), volatility_features.begin(), volatility_features.end());
            all_features.insert(all_features.end(), pattern_features.begin(), pattern_features.end());
            return all_features;
        }
    };
    
    /**
     * @brief 提取技术指标特征
     */
    std::vector<double> extractTechnicalFeatures(const std::vector<PriceData>& prices) {
        std::vector<double> features;
        
        if (prices.size() < 50) {
            return features;
        }
        
        // MA特征
        double ma5 = calculateMA(prices, 5);
        double ma10 = calculateMA(prices, 10);
        double ma20 = calculateMA(prices, 20);
        double ma50 = calculateMA(prices, 50);
        
        features.push_back(ma5 / prices.back().close - 1.0);    // MA5相对偏差
        features.push_back(ma10 / prices.back().close - 1.0);   // MA10相对偏差
        features.push_back(ma20 / prices.back().close - 1.0);   // MA20相对偏差
        features.push_back(ma50 / prices.back().close - 1.0);   // MA50相对偏差
        features.push_back((ma5 - ma10) / ma10);                // MA5-MA10偏差
        features.push_back((ma10 - ma20) / ma20);               // MA10-MA20偏差
        features.push_back((ma20 - ma50) / ma50);               // MA20-MA50偏差
        
        // RSI特征
        double rsi14 = calculateRSI(prices, 14);
        double rsi7 = calculateRSI(prices, 7);
        features.push_back(rsi14 / 100.0);                      // RSI14标准化
        features.push_back(rsi7 / 100.0);                       // RSI7标准化
        features.push_back((rsi14 - 50.0) / 50.0);             // RSI14中性偏差
        
        // 布林带特征
        auto bollinger = calculateBollingerBands(prices, 20, 2.0);
        features.push_back((prices.back().close - bollinger.lower) / (bollinger.upper - bollinger.lower)); // BB位置
        features.push_back((bollinger.upper - bollinger.lower) / bollinger.middle); // BB宽度
        
        return features;
    }
    
    /**
     * @brief 提取价格特征
     */
    std::vector<double> extractPriceFeatures(const std::vector<PriceData>& prices) {
        std::vector<double> features;
        
        if (prices.size() < 20) {
            return features;
        }
        
        const auto& current = prices.back();
        
        // 价格变化率特征
        for (int period : {1, 3, 5, 10, 20}) {
            if (prices.size() > period) {
                double change = (current.close - prices[prices.size()-1-period].close) / 
                               prices[prices.size()-1-period].close;
                features.push_back(change);
            }
        }
        
        // 高低价特征
        features.push_back((current.high - current.low) / current.close);  // 日内波幅
        features.push_back((current.close - current.low) / (current.high - current.low)); // 收盘位置
        features.push_back((current.open - current.close) / current.close); // 开收盘偏差
        
        // 价格形态特征
        if (prices.size() >= 3) {
            // 连续上涨/下跌天数
            int upDays = 0, downDays = 0;
            for (int i = prices.size() - 1; i > 0 && i > prices.size() - 10; i--) {
                if (prices[i].close > prices[i-1].close) {
                    if (downDays == 0) upDays++;
                    else break;
                } else if (prices[i].close < prices[i-1].close) {
                    if (upDays == 0) downDays++;
                    else break;
                } else {
                    break;
                }
            }
            features.push_back(upDays / 10.0);
            features.push_back(downDays / 10.0);
        }
        
        return features;
    }
    
    /**
     * @brief 提取成交量特征
     */
    std::vector<double> extractVolumeFeatures(const std::vector<PriceData>& prices) {
        std::vector<double> features;
        
        if (prices.size() < 20) {
            return features;
        }
        
        // 成交量移动平均
        double vol_ma5 = 0.0, vol_ma20 = 0.0;
        for (int i = 0; i < 5; i++) {
            vol_ma5 += prices[prices.size()-1-i].volume;
        }
        vol_ma5 /= 5.0;
        
        for (int i = 0; i < 20; i++) {
            vol_ma20 += prices[prices.size()-1-i].volume;
        }
        vol_ma20 /= 20.0;
        
        features.push_back(prices.back().volume / vol_ma5 - 1.0);  // 当日成交量相对5日均量
        features.push_back(prices.back().volume / vol_ma20 - 1.0); // 当日成交量相对20日均量
        features.push_back(vol_ma5 / vol_ma20 - 1.0);              // 5日均量相对20日均量
        
        // 量价关系
        double price_change = (prices.back().close - prices[prices.size()-2].close) / 
                             prices[prices.size()-2].close;
        double volume_change = (prices.back().volume - prices[prices.size()-2].volume) / 
                              prices[prices.size()-2].volume;
        
        features.push_back(price_change * volume_change);  // 量价协同性
        
        return features;
    }
    
    /**
     * @brief 提取波动率特征
     */
    std::vector<double> extractVolatilityFeatures(const std::vector<PriceData>& prices) {
        std::vector<double> features;
        
        if (prices.size() < 30) {
            return features;
        }
        
        // 计算不同周期的波动率
        for (int period : {5, 10, 20}) {
            double volatility = calculateVolatility(prices, period);
            features.push_back(volatility);
        }
        
        // 波动率比率
        double vol5 = calculateVolatility(prices, 5);
        double vol20 = calculateVolatility(prices, 20);
        features.push_back(vol5 / vol20);
        
        // ATR (Average True Range)
        double atr = calculateATR(prices, 14);
        features.push_back(atr / prices.back().close);
        
        return features;
    }
    
    /**
     * @brief 提取形态特征
     */
    std::vector<double> extractPatternFeatures(const std::vector<PriceData>& prices) {
        std::vector<double> features;
        
        if (prices.size() < 10) {
            return features;
        }
        
        // 检测经典形态
        features.push_back(detectHammerPattern(prices) ? 1.0 : 0.0);      // 锤子线
        features.push_back(detectDojiPattern(prices) ? 1.0 : 0.0);        // 十字星
        features.push_back(detectEngulfingPattern(prices) ? 1.0 : 0.0);   // 吞没形态
        features.push_back(detectGapPattern(prices) ? 1.0 : 0.0);         // 跳空形态
        
        // 趋势强度
        double trendStrength = calculateTrendStrength(prices, 10);
        features.push_back(trendStrength);
        
        return features;
    }
    
    /**
     * @brief 生成完整特征向量
     */
    FeatureVector generateFeatureVector(const std::vector<PriceData>& prices) {
        FeatureVector fv;
        
        fv.technical_features = extractTechnicalFeatures(prices);
        fv.price_features = extractPriceFeatures(prices);
        fv.volume_features = extractVolumeFeatures(prices);
        fv.volatility_features = extractVolatilityFeatures(prices);
        fv.pattern_features = extractPatternFeatures(prices);
        
        return fv;
    }
    
private:
    // 辅助函数实现
    double calculateMA(const std::vector<PriceData>& prices, int period) {
        if (prices.size() < period) return 0.0;
        double sum = 0.0;
        for (int i = 0; i < period; i++) {
            sum += prices[prices.size()-1-i].close;
        }
        return sum / period;
    }
    
    double calculateRSI(const std::vector<PriceData>& prices, int period) {
        if (prices.size() < period + 1) return 50.0;
        
        double gain = 0.0, loss = 0.0;
        for (int i = 0; i < period; i++) {
            double change = prices[prices.size()-1-i].close - prices[prices.size()-2-i].close;
            if (change > 0) gain += change;
            else loss += -change;
        }
        
        if (loss == 0) return 100.0;
        double rs = (gain / period) / (loss / period);
        return 100.0 - (100.0 / (1.0 + rs));
    }
    
    struct BollingerBands {
        double upper, middle, lower;
    };
    
    BollingerBands calculateBollingerBands(const std::vector<PriceData>& prices, int period, double std_mult) {
        if (prices.size() < period) return {0, 0, 0};
        
        double sum = 0.0;
        for (int i = 0; i < period; i++) {
            sum += prices[prices.size()-1-i].close;
        }
        double middle = sum / period;
        
        double variance = 0.0;
        for (int i = 0; i < period; i++) {
            double diff = prices[prices.size()-1-i].close - middle;
            variance += diff * diff;
        }
        double std_dev = std::sqrt(variance / period);
        
        return {middle + std_mult * std_dev, middle, middle - std_mult * std_dev};
    }
    
    double calculateVolatility(const std::vector<PriceData>& prices, int period) {
        if (prices.size() < period) return 0.0;
        
        std::vector<double> returns;
        for (int i = 1; i < period; i++) {
            double ret = std::log(prices[prices.size()-i].close / prices[prices.size()-i-1].close);
            returns.push_back(ret);
        }
        
        double mean = 0.0;
        for (double ret : returns) mean += ret;
        mean /= returns.size();
        
        double variance = 0.0;
        for (double ret : returns) {
            variance += (ret - mean) * (ret - mean);
        }
        
        return std::sqrt(variance / returns.size()) * std::sqrt(252);
    }
    
    double calculateATR(const std::vector<PriceData>& prices, int period) {
        if (prices.size() < period + 1) return 0.0;
        
        double sum = 0.0;
        for (int i = 0; i < period; i++) {
            const auto& current = prices[prices.size()-1-i];
            const auto& previous = prices[prices.size()-2-i];
            
            double tr = std::max({
                current.high - current.low,
                std::abs(current.high - previous.close),
                std::abs(current.low - previous.close)
            });
            sum += tr;
        }
        
        return sum / period;
    }
    
    bool detectHammerPattern(const std::vector<PriceData>& prices) {
        if (prices.size() < 2) return false;
        const auto& current = prices.back();
        
        double body = std::abs(current.close - current.open);
        double lowerShadow = std::min(current.open, current.close) - current.low;
        double upperShadow = current.high - std::max(current.open, current.close);
        
        return (lowerShadow > 2 * body) && (upperShadow < 0.5 * body);
    }
    
    bool detectDojiPattern(const std::vector<PriceData>& prices) {
        if (prices.empty()) return false;
        const auto& current = prices.back();
        
        double body = std::abs(current.close - current.open);
        double range = current.high - current.low;
        
        return (body / range) < 0.1;
    }
    
    bool detectEngulfingPattern(const std::vector<PriceData>& prices) {
        if (prices.size() < 2) return false;
        
        const auto& current = prices.back();
        const auto& previous = prices[prices.size()-2];
        
        bool currentBullish = current.close > current.open;
        bool previousBearish = previous.close < previous.open;
        
        return currentBullish && previousBearish && 
               current.open < previous.close && current.close > previous.open;
    }
    
    bool detectGapPattern(const std::vector<PriceData>& prices) {
        if (prices.size() < 2) return false;
        
        const auto& current = prices.back();
        const auto& previous = prices[prices.size()-2];
        
        double gapUp = current.low - previous.high;
        double gapDown = previous.low - current.high;
        
        return (gapUp > 0) || (gapDown > 0);
    }
    
    double calculateTrendStrength(const std::vector<PriceData>& prices, int period) {
        if (prices.size() < period) return 0.0;
        
        double startPrice = prices[prices.size()-period].close;
        double endPrice = prices.back().close;
        
        return (endPrice - startPrice) / startPrice;
    }
};

/**
 * @brief ML信号增强器
 */
class MLSignalEnhancer {
private:
    FeatureEngineer featureEngineer_;
    std::vector<std::vector<double>> trainingFeatures_;
    std::vector<int> trainingLabels_;
    
public:
    /**
     * @brief 添加训练样本
     */
    void addTrainingSample(const std::vector<PriceData>& prices, int label) {
        auto features = featureEngineer_.generateFeatureVector(prices);
        auto allFeatures = features.getAllFeatures();
        
        if (!allFeatures.empty()) {
            trainingFeatures_.push_back(allFeatures);
            trainingLabels_.push_back(label);
        }
    }
    
    /**
     * @brief 预测信号
     */
    Signal predictSignal(const std::vector<PriceData>& prices) {
        if (prices.empty()) {
            return Signal(NONE, STRENGTH_NONE, 0.0, 0.0, 0);
        }
        
        auto features = featureEngineer_.generateFeatureVector(prices);
        auto allFeatures = features.getAllFeatures();
        
        if (allFeatures.empty()) {
            return Signal(HOLD, WEAK, 0.3, prices.back().close, prices.back().timestamp);
        }
        
        // 简单的k-NN预测 (实际应用中可以集成更复杂的ML模型)
        int prediction = knnPredict(allFeatures, 5);
        double confidence = calculatePredictionConfidence(allFeatures);
        
        SignalType type = HOLD;
        SignalStrength strength = WEAK;
        
        if (prediction == 1) {
            type = BUY;
        } else if (prediction == -1) {
            type = SELL;
        }
        
        if (confidence > 0.7) {
            strength = STRONG;
        } else if (confidence > 0.5) {
            strength = MEDIUM;
        }
        
        return Signal(type, strength, confidence, prices.back().close, prices.back().timestamp);
    }
    
private:
    int knnPredict(const std::vector<double>& features, int k) {
        if (trainingFeatures_.empty()) return 0;
        
        std::vector<std::pair<double, int>> distances;
        
        for (size_t i = 0; i < trainingFeatures_.size(); i++) {
            double distance = calculateEuclideanDistance(features, trainingFeatures_[i]);
            distances.push_back({distance, trainingLabels_[i]});
        }
        
        std::sort(distances.begin(), distances.end());
        
        int buyVotes = 0, sellVotes = 0, holdVotes = 0;
        for (int i = 0; i < std::min(k, (int)distances.size()); i++) {
            if (distances[i].second == 1) buyVotes++;
            else if (distances[i].second == -1) sellVotes++;
            else holdVotes++;
        }
        
        if (buyVotes > sellVotes && buyVotes > holdVotes) return 1;
        if (sellVotes > buyVotes && sellVotes > holdVotes) return -1;
        return 0;
    }
    
    double calculateEuclideanDistance(const std::vector<double>& a, const std::vector<double>& b) {
        if (a.size() != b.size()) return std::numeric_limits<double>::max();
        
        double sum = 0.0;
        for (size_t i = 0; i < a.size(); i++) {
            double diff = a[i] - b[i];
            sum += diff * diff;
        }
        return std::sqrt(sum);
    }
    
    double calculatePredictionConfidence(const std::vector<double>& features) {
        // 基于特征的置信度计算
        double confidence = 0.5;
        
        // 根据特征的一致性调整置信度
        if (!features.empty()) {
            double variance = 0.0;
            double mean = 0.0;
            for (double f : features) {
                mean += std::abs(f);
            }
            mean /= features.size();
            
            for (double f : features) {
                variance += (std::abs(f) - mean) * (std::abs(f) - mean);
            }
            variance /= features.size();
            
            // 特征一致性越高，置信度越高
            confidence = std::min(0.9, 0.3 + 0.6 / (1.0 + variance));
        }
        
        return confidence;
    }
};

} // namespace ml
} // namespace astroboy
