/**
 * @file multi_signal_fusion.cpp
 * @brief 多信号融合系统
 * <AUTHOR>
 */

#include "atomboy_signal.h"
#include "enhanced_signal_algorithms.cpp"
#include <vector>
#include <map>
#include <algorithm>
#include <cmath>

namespace astroboy {
namespace fusion {

/**
 * @brief 信号权重配置
 */
struct SignalWeight {
    double ma_weight = 0.25;        // MA信号权重
    double rsi_weight = 0.20;       // RSI信号权重
    double macd_weight = 0.25;      // MACD信号权重
    double bollinger_weight = 0.15; // 布林带信号权重
    double kdj_weight = 0.15;       // KDJ信号权重
};

/**
 * @brief 市场状态枚举
 */
enum MarketState {
    TRENDING_UP,      // 上升趋势
    TRENDING_DOWN,    // 下降趋势
    SIDEWAYS,         // 横盘震荡
    HIGH_VOLATILITY,  // 高波动
    LOW_VOLATILITY    // 低波动
};

/**
 * @brief 多信号融合引擎
 */
class MultiSignalFusionEngine {
private:
    SignalWeight weights_;
    std::vector<Signal> signalHistory_;
    
    // 信号生成器
    enhanced::MACDSignalGenerator macdGenerator_;
    enhanced::BollingerBandsSignalGenerator bollingerGenerator_;
    enhanced::KDJSignalGenerator kdjGenerator_;
    
    /**
     * @brief 检测市场状态
     */
    MarketState detectMarketState(const std::vector<PriceData>& prices) {
        if (prices.size() < 50) {
            return SIDEWAYS;
        }
        
        // 计算价格趋势
        double longTermTrend = calculateTrend(prices, 50);
        double shortTermTrend = calculateTrend(prices, 20);
        
        // 计算波动率
        double volatility = calculateVolatility(prices, 20);
        double avgVolatility = calculateVolatility(prices, 50);
        
        // 判断趋势
        if (longTermTrend > 0.02 && shortTermTrend > 0.01) {
            return TRENDING_UP;
        } else if (longTermTrend < -0.02 && shortTermTrend < -0.01) {
            return TRENDING_DOWN;
        }
        
        // 判断波动率
        if (volatility > avgVolatility * 1.5) {
            return HIGH_VOLATILITY;
        } else if (volatility < avgVolatility * 0.5) {
            return LOW_VOLATILITY;
        }
        
        return SIDEWAYS;
    }
    
    /**
     * @brief 计算趋势斜率
     */
    double calculateTrend(const std::vector<PriceData>& prices, int period) {
        if (prices.size() < period) return 0.0;
        
        double startPrice = prices[prices.size() - period].close;
        double endPrice = prices.back().close;
        
        return (endPrice - startPrice) / startPrice;
    }
    
    /**
     * @brief 计算波动率
     */
    double calculateVolatility(const std::vector<PriceData>& prices, int period) {
        if (prices.size() < period) return 0.0;
        
        std::vector<double> returns;
        for (size_t i = prices.size() - period + 1; i < prices.size(); i++) {
            double ret = (prices[i].close - prices[i-1].close) / prices[i-1].close;
            returns.push_back(ret);
        }
        
        double mean = 0.0;
        for (double ret : returns) {
            mean += ret;
        }
        mean /= returns.size();
        
        double variance = 0.0;
        for (double ret : returns) {
            variance += (ret - mean) * (ret - mean);
        }
        
        return std::sqrt(variance / returns.size());
    }
    
    /**
     * @brief 根据市场状态调整权重
     */
    SignalWeight adjustWeightsForMarketState(MarketState state) {
        SignalWeight adjustedWeights = weights_;
        
        switch (state) {
            case TRENDING_UP:
            case TRENDING_DOWN:
                // 趋势市场，增加趋势指标权重
                adjustedWeights.ma_weight *= 1.3;
                adjustedWeights.macd_weight *= 1.2;
                adjustedWeights.rsi_weight *= 0.8;
                adjustedWeights.bollinger_weight *= 0.9;
                break;
                
            case SIDEWAYS:
                // 震荡市场，增加震荡指标权重
                adjustedWeights.rsi_weight *= 1.3;
                adjustedWeights.bollinger_weight *= 1.2;
                adjustedWeights.kdj_weight *= 1.2;
                adjustedWeights.ma_weight *= 0.8;
                break;
                
            case HIGH_VOLATILITY:
                // 高波动，降低所有信号权重
                adjustedWeights.ma_weight *= 0.7;
                adjustedWeights.rsi_weight *= 0.8;
                adjustedWeights.macd_weight *= 0.8;
                adjustedWeights.bollinger_weight *= 1.1;
                adjustedWeights.kdj_weight *= 0.9;
                break;
                
            case LOW_VOLATILITY:
                // 低波动，增加趋势信号权重
                adjustedWeights.ma_weight *= 1.2;
                adjustedWeights.macd_weight *= 1.1;
                adjustedWeights.rsi_weight *= 0.9;
                break;
        }
        
        // 归一化权重
        double totalWeight = adjustedWeights.ma_weight + adjustedWeights.rsi_weight + 
                           adjustedWeights.macd_weight + adjustedWeights.bollinger_weight + 
                           adjustedWeights.kdj_weight;
        
        adjustedWeights.ma_weight /= totalWeight;
        adjustedWeights.rsi_weight /= totalWeight;
        adjustedWeights.macd_weight /= totalWeight;
        adjustedWeights.bollinger_weight /= totalWeight;
        adjustedWeights.kdj_weight /= totalWeight;
        
        return adjustedWeights;
    }
    
    /**
     * @brief 计算信号一致性
     */
    double calculateSignalConsistency(const std::vector<Signal>& signals) {
        if (signals.empty()) return 0.0;
        
        std::map<SignalType, int> typeCount;
        for (const auto& signal : signals) {
            typeCount[signal.type]++;
        }
        
        int maxCount = 0;
        for (const auto& pair : typeCount) {
            maxCount = std::max(maxCount, pair.second);
        }
        
        return static_cast<double>(maxCount) / signals.size();
    }
    
    /**
     * @brief 计算加权置信度
     */
    double calculateWeightedConfidence(const std::vector<Signal>& signals, 
                                     const SignalWeight& weights) {
        if (signals.size() != 5) return 0.0;
        
        std::vector<double> weightArray = {
            weights.ma_weight, weights.rsi_weight, weights.macd_weight,
            weights.bollinger_weight, weights.kdj_weight
        };
        
        double weightedConfidence = 0.0;
        for (size_t i = 0; i < signals.size(); i++) {
            weightedConfidence += signals[i].confidence * weightArray[i];
        }
        
        return weightedConfidence;
    }
    
public:
    /**
     * @brief 构造函数
     */
    MultiSignalFusionEngine(const SignalWeight& weights = SignalWeight()) 
        : weights_(weights) {}
    
    /**
     * @brief 生成融合信号
     */
    Signal generateFusedSignal(const std::vector<PriceData>& prices) {
        if (prices.size() < 50) {
            return Signal(NONE, STRENGTH_NONE, 0.0, 0.0, 0);
        }
        
        // 检测市场状态
        MarketState marketState = detectMarketState(prices);
        
        // 根据市场状态调整权重
        SignalWeight adjustedWeights = adjustWeightsForMarketState(marketState);
        
        // 生成各种信号
        std::vector<Signal> signals;
        
        // MA信号 (使用原有的generateMASignal)
        signals.push_back(generateMASignal(prices));
        
        // RSI信号 (使用原有的generateRSISignal)
        signals.push_back(generateRSISignal(prices));
        
        // MACD信号
        signals.push_back(macdGenerator_.generateMACDSignal(prices));
        
        // 布林带信号
        signals.push_back(bollingerGenerator_.generateBollingerSignal(prices));
        
        // KDJ信号
        signals.push_back(kdjGenerator_.generateKDJSignal(prices));
        
        // 计算信号一致性
        double consistency = calculateSignalConsistency(signals);
        
        // 计算加权置信度
        double weightedConfidence = calculateWeightedConfidence(signals, adjustedWeights);
        
        // 确定最终信号类型
        std::map<SignalType, double> typeWeights;
        for (size_t i = 0; i < signals.size(); i++) {
            std::vector<double> weightArray = {
                adjustedWeights.ma_weight, adjustedWeights.rsi_weight, 
                adjustedWeights.macd_weight, adjustedWeights.bollinger_weight, 
                adjustedWeights.kdj_weight
            };
            
            typeWeights[signals[i].type] += weightArray[i] * signals[i].confidence;
        }
        
        // 找到权重最大的信号类型
        SignalType finalType = HOLD;
        double maxWeight = 0.0;
        for (const auto& pair : typeWeights) {
            if (pair.second > maxWeight) {
                maxWeight = pair.second;
                finalType = pair.first;
            }
        }
        
        // 确定信号强度
        SignalStrength finalStrength = WEAK;
        if (weightedConfidence > 0.7 && consistency > 0.6) {
            finalStrength = STRONG;
        } else if (weightedConfidence > 0.5 && consistency > 0.4) {
            finalStrength = MEDIUM;
        }
        
        // 调整最终置信度
        double finalConfidence = weightedConfidence * consistency;
        
        // 根据市场状态进一步调整置信度
        switch (marketState) {
            case HIGH_VOLATILITY:
                finalConfidence *= 0.8;  // 高波动时降低置信度
                break;
            case LOW_VOLATILITY:
                finalConfidence *= 1.1;  // 低波动时提高置信度
                break;
            default:
                break;
        }
        
        finalConfidence = std::min(0.95, std::max(0.0, finalConfidence));
        
        Signal fusedSignal(finalType, finalStrength, finalConfidence, 
                          prices.back().close, prices.back().timestamp);
        
        signalHistory_.push_back(fusedSignal);
        
        // 保持历史记录不超过1000条
        if (signalHistory_.size() > 1000) {
            signalHistory_.erase(signalHistory_.begin());
        }
        
        return fusedSignal;
    }
    
    /**
     * @brief 获取信号统计信息
     */
    std::map<std::string, double> getSignalStatistics() {
        std::map<std::string, double> stats;
        
        if (signalHistory_.empty()) {
            return stats;
        }
        
        int buyCount = 0, sellCount = 0, holdCount = 0;
        double avgConfidence = 0.0;
        
        for (const auto& signal : signalHistory_) {
            switch (signal.type) {
                case BUY: buyCount++; break;
                case SELL: sellCount++; break;
                case HOLD: holdCount++; break;
                default: break;
            }
            avgConfidence += signal.confidence;
        }
        
        avgConfidence /= signalHistory_.size();
        
        stats["total_signals"] = signalHistory_.size();
        stats["buy_signals"] = buyCount;
        stats["sell_signals"] = sellCount;
        stats["hold_signals"] = holdCount;
        stats["avg_confidence"] = avgConfidence;
        stats["buy_ratio"] = static_cast<double>(buyCount) / signalHistory_.size();
        stats["sell_ratio"] = static_cast<double>(sellCount) / signalHistory_.size();
        
        return stats;
    }
    
    /**
     * @brief 设置信号权重
     */
    void setSignalWeights(const SignalWeight& weights) {
        weights_ = weights;
    }
    
    /**
     * @brief 获取当前权重
     */
    SignalWeight getSignalWeights() const {
        return weights_;
    }
};

} // namespace fusion
} // namespace astroboy
