#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简化的DLL测试脚本，避免typing模块问题
"""

import os
import sys
import ctypes

def test_basic_dll():
    """基础DLL测试"""
    print("=== 阿童木信号生成器基础测试 ===")
    
    # 查找DLL文件
    dll_path = "atomboy_signal.dll"
    if not os.path.exists(dll_path):
        print(f"错误: 找不到DLL文件 {dll_path}")
        return False
    
    try:
        # 加载DLL
        print(f"加载DLL: {dll_path}")
        lib = ctypes.CDLL(dll_path)
        
        # 设置函数原型
        lib.getVersion.restype = ctypes.c_char_p
        lib.getVersion.argtypes = []
        
        lib.add.restype = ctypes.c_int
        lib.add.argtypes = [ctypes.c_int, ctypes.c_int]
        
        lib.printHello.restype = None
        lib.printHello.argtypes = []
        
        # 测试基本功能
        print("1. 测试版本信息...")
        version = lib.getVersion()
        print(f"   版本: {version.decode('utf-8')}")
        
        print("2. 测试加法函数...")
        result = lib.add(5, 3)
        print(f"   5 + 3 = {result}")
        
        print("3. 测试Hello函数...")
        lib.printHello()
        
        # 测试信号生成功能
        if hasattr(lib, 'generateSignal'):
            print("4. 测试信号生成...")
            lib.generateSignal.restype = ctypes.c_int
            lib.generateSignal.argtypes = [ctypes.c_double, ctypes.c_double, ctypes.c_double]
            
            signal = lib.generateSignal(100.0, 101.0, 99.0)
            print(f"   信号结果: {signal}")
        
        print("=== 基础测试通过 ===")
        return True
        
    except Exception as e:
        print(f"错误: {str(e)}")
        return False

def test_advanced_functions():
    """测试高级功能"""
    print("\n=== 高级功能测试 ===")
    
    dll_path = "atomboy_signal.dll"
    if not os.path.exists(dll_path):
        return False
    
    try:
        lib = ctypes.CDLL(dll_path)
        
        # 测试技术指标计算
        if hasattr(lib, 'calculateMA'):
            print("1. 测试移动平均计算...")
            lib.calculateMA.restype = ctypes.c_double
            lib.calculateMA.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
            
            # 准备测试数据
            prices = [100.0, 101.0, 102.0, 101.5, 103.0, 102.5, 104.0, 103.5, 105.0, 104.5]
            price_array = (ctypes.c_double * len(prices))(*prices)
            
            ma5 = lib.calculateMA(price_array, len(prices), 5)
            print(f"   MA5: {ma5:.4f}")
        
        if hasattr(lib, 'calculateRSI'):
            print("2. 测试RSI计算...")
            lib.calculateRSI.restype = ctypes.c_double
            lib.calculateRSI.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
            
            rsi = lib.calculateRSI(price_array, len(prices), 14)
            print(f"   RSI: {rsi:.4f}")
        
        print("=== 高级功能测试完成 ===")
        return True
        
    except Exception as e:
        print(f"高级功能测试错误: {str(e)}")
        return False

if __name__ == "__main__":
    print("阿童木信号生成器DLL测试")
    print("=" * 40)
    
    # 切换到DLL所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"工作目录: {os.getcwd()}")
    
    # 运行测试
    basic_ok = test_basic_dll()
    if basic_ok:
        test_advanced_functions()
    else:
        print("基础测试失败，跳过高级测试")
    
    print("\n测试完成")
