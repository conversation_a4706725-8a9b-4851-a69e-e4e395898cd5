/**
 * @file cuda_kernels.cu
 * @brief V100 CUDA计算内核实现
 * <AUTHOR> - V100优化版
 */

#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#include <cub/cub.cuh>
#include <thrust/device_vector.h>
#include <thrust/transform.h>
#include <thrust/reduce.h>
#include <cmath>

namespace astroboy {
namespace gpu {
namespace kernels {

/**
 * @brief 移动平均计算内核
 */
__global__ void calculateMovingAverageKernel(
    const float* prices, 
    float* ma_results, 
    int data_size, 
    int period) {
    
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    
    if (idx >= period - 1 && idx < data_size) {
        float sum = 0.0f;
        
        // 使用共享内存优化
        extern __shared__ float shared_prices[];
        
        // 加载数据到共享内存
        int tid = threadIdx.x;
        int start_idx = max(0, idx - period + 1);
        
        for (int i = 0; i < period; i++) {
            if (start_idx + i < data_size) {
                shared_prices[tid * period + i] = prices[start_idx + i];
            }
        }
        
        __syncthreads();
        
        // 计算移动平均
        for (int i = 0; i < period; i++) {
            sum += shared_prices[tid * period + i];
        }
        
        ma_results[idx] = sum / period;
    }
}

/**
 * @brief 批量移动平均计算内核
 */
__global__ void calculateMultiPeriodMAKernel(
    const float* prices,
    float* ma_results,
    const int* periods,
    int num_periods,
    int data_size) {
    
    int period_idx = blockIdx.y;
    int data_idx = blockIdx.x * blockDim.x + threadIdx.x;
    
    if (period_idx >= num_periods || data_idx >= data_size) {
        return;
    }
    
    int period = periods[period_idx];
    
    if (data_idx >= period - 1) {
        float sum = 0.0f;
        
        for (int i = 0; i < period; i++) {
            sum += prices[data_idx - i];
        }
        
        // 结果存储：[period_idx * data_size + data_idx]
        ma_results[period_idx * data_size + data_idx] = sum / period;
    }
}

/**
 * @brief RSI计算内核
 */
__global__ void calculateRSIKernel(
    const float* prices,
    float* rsi_results,
    int data_size,
    int period) {
    
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    
    if (idx >= period && idx < data_size) {
        float gain_sum = 0.0f;
        float loss_sum = 0.0f;
        
        // 计算增益和损失
        for (int i = 1; i <= period; i++) {
            float change = prices[idx - i + 1] - prices[idx - i];
            if (change > 0) {
                gain_sum += change;
            } else {
                loss_sum += -change;
            }
        }
        
        float avg_gain = gain_sum / period;
        float avg_loss = loss_sum / period;
        
        // 计算RSI
        if (avg_loss > 0) {
            float rs = avg_gain / avg_loss;
            rsi_results[idx] = 100.0f - (100.0f / (1.0f + rs));
        } else {
            rsi_results[idx] = 100.0f;
        }
    }
}

/**
 * @brief EMA计算内核
 */
__global__ void calculateEMAKernel(
    const float* prices,
    float* ema_results,
    int data_size,
    float alpha) {
    
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    
    if (idx == 0) {
        ema_results[0] = prices[0];
    } else if (idx < data_size) {
        // 等待前一个值计算完成
        __syncthreads();
        ema_results[idx] = alpha * prices[idx] + (1.0f - alpha) * ema_results[idx - 1];
    }
}

/**
 * @brief MACD计算内核
 */
__global__ void calculateMACDKernel(
    const float* prices,
    float* dif_results,
    float* dea_results,
    float* macd_results,
    int data_size,
    int fast_period,
    int slow_period,
    int signal_period) {
    
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    
    if (idx >= slow_period && idx < data_size) {
        float alpha_fast = 2.0f / (fast_period + 1);
        float alpha_slow = 2.0f / (slow_period + 1);
        float alpha_signal = 2.0f / (signal_period + 1);
        
        // 计算快慢EMA
        float ema_fast = prices[0];
        float ema_slow = prices[0];
        
        for (int i = 1; i <= idx; i++) {
            ema_fast = alpha_fast * prices[i] + (1.0f - alpha_fast) * ema_fast;
            ema_slow = alpha_slow * prices[i] + (1.0f - alpha_slow) * ema_slow;
        }
        
        // 计算DIF
        dif_results[idx] = ema_fast - ema_slow;
        
        // 计算DEA
        if (idx == slow_period) {
            dea_results[idx] = dif_results[idx];
        } else {
            dea_results[idx] = alpha_signal * dif_results[idx] + 
                              (1.0f - alpha_signal) * dea_results[idx - 1];
        }
        
        // 计算MACD柱
        macd_results[idx] = (dif_results[idx] - dea_results[idx]) * 2.0f;
    }
}

/**
 * @brief 布林带计算内核
 */
__global__ void calculateBollingerBandsKernel(
    const float* prices,
    float* upper_results,
    float* middle_results,
    float* lower_results,
    int data_size,
    int period,
    float std_multiplier) {
    
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    
    if (idx >= period - 1 && idx < data_size) {
        // 计算移动平均
        float sum = 0.0f;
        for (int i = 0; i < period; i++) {
            sum += prices[idx - i];
        }
        float ma = sum / period;
        middle_results[idx] = ma;
        
        // 计算标准差
        float variance = 0.0f;
        for (int i = 0; i < period; i++) {
            float diff = prices[idx - i] - ma;
            variance += diff * diff;
        }
        float std_dev = sqrtf(variance / period);
        
        // 计算上下轨
        upper_results[idx] = ma + std_multiplier * std_dev;
        lower_results[idx] = ma - std_multiplier * std_dev;
    }
}

/**
 * @brief KDJ计算内核
 */
__global__ void calculateKDJKernel(
    const float* high_prices,
    const float* low_prices,
    const float* close_prices,
    float* k_results,
    float* d_results,
    float* j_results,
    int data_size,
    int period,
    int k_period,
    int d_period) {
    
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    
    if (idx >= period - 1 && idx < data_size) {
        // 计算最高价和最低价
        float highest = high_prices[idx - period + 1];
        float lowest = low_prices[idx - period + 1];
        
        for (int i = 0; i < period; i++) {
            highest = fmaxf(highest, high_prices[idx - i]);
            lowest = fminf(lowest, low_prices[idx - i]);
        }
        
        // 计算RSV
        float rsv = 50.0f;
        if (highest != lowest) {
            rsv = (close_prices[idx] - lowest) / (highest - lowest) * 100.0f;
        }
        
        // 计算K值
        if (idx == period - 1) {
            k_results[idx] = rsv;
        } else {
            k_results[idx] = (rsv + (k_period - 1) * k_results[idx - 1]) / k_period;
        }
        
        // 计算D值
        if (idx == period - 1) {
            d_results[idx] = k_results[idx];
        } else {
            d_results[idx] = (k_results[idx] + (d_period - 1) * d_results[idx - 1]) / d_period;
        }
        
        // 计算J值
        j_results[idx] = 3.0f * k_results[idx] - 2.0f * d_results[idx];
    }
}

/**
 * @brief 波动率计算内核
 */
__global__ void calculateVolatilityKernel(
    const float* prices,
    float* volatility_results,
    int data_size,
    int period) {
    
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    
    if (idx >= period && idx < data_size) {
        // 计算对数收益率
        float returns[256];  // 假设period不超过256
        float mean = 0.0f;
        
        for (int i = 1; i <= period; i++) {
            returns[i-1] = logf(prices[idx - i + 1] / prices[idx - i]);
            mean += returns[i-1];
        }
        mean /= period;
        
        // 计算方差
        float variance = 0.0f;
        for (int i = 0; i < period; i++) {
            float diff = returns[i] - mean;
            variance += diff * diff;
        }
        variance /= period;
        
        // 年化波动率
        volatility_results[idx] = sqrtf(variance) * sqrtf(252.0f);
    }
}

/**
 * @brief 特征标准化内核
 */
__global__ void normalizeFeaturesBatchKernel(
    float* features,
    const float* means,
    const float* std_devs,
    int num_samples,
    int num_features) {
    
    int sample_idx = blockIdx.x * blockDim.x + threadIdx.x;
    int feature_idx = blockIdx.y * blockDim.y + threadIdx.y;
    
    if (sample_idx < num_samples && feature_idx < num_features) {
        int idx = sample_idx * num_features + feature_idx;
        
        if (std_devs[feature_idx] > 0) {
            features[idx] = (features[idx] - means[feature_idx]) / std_devs[feature_idx];
        }
    }
}

/**
 * @brief 相关性计算内核
 */
__global__ void calculateCorrelationKernel(
    const float* series1,
    const float* series2,
    float* correlation_results,
    int data_size,
    int window_size) {
    
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    
    if (idx >= window_size - 1 && idx < data_size) {
        float sum1 = 0.0f, sum2 = 0.0f;
        float sum1_sq = 0.0f, sum2_sq = 0.0f;
        float sum_product = 0.0f;
        
        // 计算统计量
        for (int i = 0; i < window_size; i++) {
            float val1 = series1[idx - i];
            float val2 = series2[idx - i];
            
            sum1 += val1;
            sum2 += val2;
            sum1_sq += val1 * val1;
            sum2_sq += val2 * val2;
            sum_product += val1 * val2;
        }
        
        // 计算相关系数
        float n = window_size;
        float numerator = n * sum_product - sum1 * sum2;
        float denominator = sqrtf((n * sum1_sq - sum1 * sum1) * (n * sum2_sq - sum2 * sum2));
        
        if (denominator > 0) {
            correlation_results[idx] = numerator / denominator;
        } else {
            correlation_results[idx] = 0.0f;
        }
    }
}

/**
 * @brief 矩阵乘法内核（用于ML特征计算）
 */
__global__ void matrixMultiplyKernel(
    const float* A,
    const float* B,
    float* C,
    int M, int N, int K) {
    
    int row = blockIdx.y * blockDim.y + threadIdx.y;
    int col = blockIdx.x * blockDim.x + threadIdx.x;
    
    if (row < M && col < N) {
        float sum = 0.0f;
        
        for (int k = 0; k < K; k++) {
            sum += A[row * K + k] * B[k * N + col];
        }
        
        C[row * N + col] = sum;
    }
}

/**
 * @brief 激活函数内核（ReLU）
 */
__global__ void reluActivationKernel(float* data, int size) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    
    if (idx < size) {
        data[idx] = fmaxf(0.0f, data[idx]);
    }
}

/**
 * @brief Sigmoid激活函数内核
 */
__global__ void sigmoidActivationKernel(float* data, int size) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    
    if (idx < size) {
        data[idx] = 1.0f / (1.0f + expf(-data[idx]));
    }
}

/**
 * @brief Tanh激活函数内核
 */
__global__ void tanhActivationKernel(float* data, int size) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    
    if (idx < size) {
        data[idx] = tanhf(data[idx]);
    }
}

} // namespace kernels

// C++接口函数

extern "C" {

/**
 * @brief 启动移动平均计算内核
 */
cudaError_t launchMovingAverageKernel(
    const float* d_prices,
    float* d_ma_results,
    int data_size,
    int period,
    cudaStream_t stream) {
    
    int block_size = 256;
    int grid_size = (data_size + block_size - 1) / block_size;
    
    size_t shared_mem_size = block_size * period * sizeof(float);
    
    kernels::calculateMovingAverageKernel<<<grid_size, block_size, shared_mem_size, stream>>>(
        d_prices, d_ma_results, data_size, period);
    
    return cudaGetLastError();
}

/**
 * @brief 启动多周期移动平均计算内核
 */
cudaError_t launchMultiPeriodMAKernel(
    const float* d_prices,
    float* d_ma_results,
    const int* d_periods,
    int num_periods,
    int data_size,
    cudaStream_t stream) {
    
    dim3 block_size(16, 16);
    dim3 grid_size(
        (data_size + block_size.x - 1) / block_size.x,
        (num_periods + block_size.y - 1) / block_size.y
    );
    
    kernels::calculateMultiPeriodMAKernel<<<grid_size, block_size, 0, stream>>>(
        d_prices, d_ma_results, d_periods, num_periods, data_size);
    
    return cudaGetLastError();
}

/**
 * @brief 启动RSI计算内核
 */
cudaError_t launchRSIKernel(
    const float* d_prices,
    float* d_rsi_results,
    int data_size,
    int period,
    cudaStream_t stream) {
    
    int block_size = 256;
    int grid_size = (data_size + block_size - 1) / block_size;
    
    kernels::calculateRSIKernel<<<grid_size, block_size, 0, stream>>>(
        d_prices, d_rsi_results, data_size, period);
    
    return cudaGetLastError();
}

/**
 * @brief 启动MACD计算内核
 */
cudaError_t launchMACDKernel(
    const float* d_prices,
    float* d_dif_results,
    float* d_dea_results,
    float* d_macd_results,
    int data_size,
    int fast_period,
    int slow_period,
    int signal_period,
    cudaStream_t stream) {
    
    int block_size = 256;
    int grid_size = (data_size + block_size - 1) / block_size;
    
    kernels::calculateMACDKernel<<<grid_size, block_size, 0, stream>>>(
        d_prices, d_dif_results, d_dea_results, d_macd_results,
        data_size, fast_period, slow_period, signal_period);
    
    return cudaGetLastError();
}

} // extern "C"

} // namespace gpu
} // namespace astroboy
