/**
 * @file v100_gpu_accelerator.cpp
 * @brief V100 GPU加速实现
 * <AUTHOR> - V100优化版
 */

#include "astroboy/v100_gpu_accelerator.h"
#include <iostream>
#include <algorithm>
#include <cmath>
#include <chrono>
#include <cstring>

namespace astroboy {
namespace gpu {

// 静态成员初始化
V100AcceleratorManager* V100AcceleratorManager::instance_ = nullptr;
std::mutex V100AcceleratorManager::instance_mutex_;

V100AcceleratorManager* V100AcceleratorManager::getInstance() {
    std::lock_guard<std::mutex> lock(instance_mutex_);
    if (instance_ == nullptr) {
        instance_ = new V100AcceleratorManager();
    }
    return instance_;
}

V100AcceleratorManager::~V100AcceleratorManager() {
    cleanup();
}

bool V100AcceleratorManager::initialize() {
    if (initialized_) {
        return true;
    }
    
    std::cout << "初始化V100 GPU加速器..." << std::endl;
    
#ifdef ASTROBOY_ENABLE_CUDA
    // 检查CUDA设备
    int device_count = 0;
    cudaError_t error = cudaGetDeviceCount(&device_count);
    if (error != cudaSuccess || device_count == 0) {
        std::cout << "未检测到CUDA设备，使用CPU模式" << std::endl;
        cuda_available_ = false;
        initialized_ = true;
        return true;
    }
    
    // 选择最佳GPU设备
    int best_device = 0;
    size_t max_memory = 0;
    
    for (int i = 0; i < device_count; i++) {
        cudaDeviceProp prop;
        cudaGetDeviceProperties(&prop, i);
        
        std::cout << "GPU " << i << ": " << prop.name 
                  << " (内存: " << prop.totalGlobalMem / (1024*1024*1024) << "GB)" << std::endl;
        
        if (prop.totalGlobalMem > max_memory) {
            max_memory = prop.totalGlobalMem;
            best_device = i;
        }
    }
    
    // 设置设备
    error = cudaSetDevice(best_device);
    if (error != cudaSuccess) {
        std::cout << "设置CUDA设备失败: " << cudaGetErrorString(error) << std::endl;
        cuda_available_ = false;
        initialized_ = true;
        return true;
    }
    
    device_id_ = best_device;
    cudaGetDeviceProperties(&device_prop_, device_id_);
    
    // 创建CUDA流
    error = cudaStreamCreate(&stream_);
    if (error != cudaSuccess) {
        std::cout << "创建CUDA流失败: " << cudaGetErrorString(error) << std::endl;
        cuda_available_ = false;
        initialized_ = true;
        return true;
    }
    
    // 创建cuBLAS句柄
    cublasStatus_t cublas_status = cublasCreate(&cublas_handle_);
    if (cublas_status != CUBLAS_STATUS_SUCCESS) {
        std::cout << "创建cuBLAS句柄失败" << std::endl;
        cudaStreamDestroy(stream_);
        cuda_available_ = false;
        initialized_ = true;
        return true;
    }
    
    cublasSetStream(cublas_handle_, stream_);
    
    // 创建cuRAND生成器
    curandStatus_t curand_status = curandCreateGenerator(&curand_gen_, CURAND_RNG_PSEUDO_DEFAULT);
    if (curand_status != CURAND_STATUS_SUCCESS) {
        std::cout << "创建cuRAND生成器失败" << std::endl;
        cublasDestroy(cublas_handle_);
        cudaStreamDestroy(stream_);
        cuda_available_ = false;
        initialized_ = true;
        return true;
    }
    
    curandSetStream(curand_gen_, stream_);
    
    cuda_available_ = true;
    std::cout << "V100 GPU加速器初始化成功!" << std::endl;
    std::cout << "使用设备: " << device_prop_.name << std::endl;
    std::cout << "计算能力: " << device_prop_.major << "." << device_prop_.minor << std::endl;
    std::cout << "全局内存: " << device_prop_.totalGlobalMem / (1024*1024*1024) << "GB" << std::endl;
    std::cout << "多处理器: " << device_prop_.multiProcessorCount << "个" << std::endl;
    
#else
    std::cout << "CUDA支持未编译，使用CPU模式" << std::endl;
    cuda_available_ = false;
#endif
    
    initialized_ = true;
    return true;
}

void V100AcceleratorManager::cleanup() {
    if (!initialized_) {
        return;
    }
    
#ifdef ASTROBOY_ENABLE_CUDA
    if (cuda_available_) {
        // 清理内存池
        {
            std::lock_guard<std::mutex> lock(memory_mutex_);
            for (void* ptr : memory_pool_) {
                cudaFree(ptr);
            }
            memory_pool_.clear();
            memory_sizes_.clear();
        }
        
        // 销毁cuRAND生成器
        if (curand_gen_) {
            curandDestroyGenerator(curand_gen_);
        }
        
        // 销毁cuBLAS句柄
        if (cublas_handle_) {
            cublasDestroy(cublas_handle_);
        }
        
        // 销毁CUDA流
        if (stream_) {
            cudaStreamDestroy(stream_);
        }
        
        // 重置设备
        cudaDeviceReset();
    }
#endif
    
    initialized_ = false;
    cuda_available_ = false;
}

bool V100AcceleratorManager::isV100() const {
#ifdef ASTROBOY_ENABLE_CUDA
    if (!cuda_available_) {
        return false;
    }
    
    // 检查是否为V100系列
    std::string device_name(device_prop_.name);
    return device_name.find("V100") != std::string::npos ||
           device_name.find("Tesla V100") != std::string::npos;
#else
    return false;
#endif
}

std::string V100AcceleratorManager::getGPUInfo() const {
#ifdef ASTROBOY_ENABLE_CUDA
    if (!cuda_available_) {
        return "CUDA不可用，使用CPU模式";
    }
    
    char buffer[512];
    snprintf(buffer, sizeof(buffer),
        "GPU设备: %s\n"
        "计算能力: %d.%d\n"
        "全局内存: %.2fGB\n"
        "多处理器: %d个\n"
        "最大线程/块: %d\n"
        "最大块维度: %dx%dx%d\n"
        "最大网格维度: %dx%dx%d",
        device_prop_.name,
        device_prop_.major, device_prop_.minor,
        device_prop_.totalGlobalMem / (1024.0*1024.0*1024.0),
        device_prop_.multiProcessorCount,
        device_prop_.maxThreadsPerBlock,
        device_prop_.maxThreadsDim[0], device_prop_.maxThreadsDim[1], device_prop_.maxThreadsDim[2],
        device_prop_.maxGridSize[0], device_prop_.maxGridSize[1], device_prop_.maxGridSize[2]);
    
    return std::string(buffer);
#else
    return "CUDA支持未编译";
#endif
}

#ifdef ASTROBOY_ENABLE_CUDA
void* V100AcceleratorManager::allocateGPUMemory(size_t size) {
    if (!cuda_available_) {
        return nullptr;
    }
    
    void* ptr = nullptr;
    cudaError_t error = cudaMalloc(&ptr, size);
    if (error != cudaSuccess) {
        std::cout << "GPU内存分配失败: " << cudaGetErrorString(error) << std::endl;
        return nullptr;
    }
    
    {
        std::lock_guard<std::mutex> lock(memory_mutex_);
        memory_pool_.push_back(ptr);
        memory_sizes_.push_back(size);
    }
    
    return ptr;
}

void V100AcceleratorManager::freeGPUMemory(void* ptr) {
    if (!cuda_available_ || ptr == nullptr) {
        return;
    }
    
    {
        std::lock_guard<std::mutex> lock(memory_mutex_);
        auto it = std::find(memory_pool_.begin(), memory_pool_.end(), ptr);
        if (it != memory_pool_.end()) {
            size_t index = it - memory_pool_.begin();
            memory_pool_.erase(it);
            memory_sizes_.erase(memory_sizes_.begin() + index);
        }
    }
    
    cudaFree(ptr);
}

void V100AcceleratorManager::synchronize() {
    if (cuda_available_) {
        cudaStreamSynchronize(stream_);
    }
}
#endif

V100PerformanceStats V100AcceleratorManager::getPerformanceStats() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return perf_stats_;
}

void V100AcceleratorManager::updatePerformanceStats(double gpu_time, double cpu_time) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    perf_stats_.updateStats(gpu_time, cpu_time);
}

void V100AcceleratorManager::resetPerformanceStats() {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    perf_stats_ = V100PerformanceStats();
}

// V100TechnicalIndicators 实现
V100TechnicalIndicators::V100TechnicalIndicators() 
    : accelerator_(V100AcceleratorManager::getInstance())
    , use_gpu_(false) {
    
    if (accelerator_->initialize()) {
        use_gpu_ = accelerator_->isCudaAvailable();
        if (use_gpu_) {
            std::cout << "V100技术指标计算器初始化成功 (GPU模式)" << std::endl;
        } else {
            std::cout << "V100技术指标计算器初始化成功 (CPU模式)" << std::endl;
        }
    }
}

V100TechnicalIndicators::~V100TechnicalIndicators() {
#ifdef ASTROBOY_ENABLE_CUDA
    cleanupGPUBuffers();
#endif
}

bool V100TechnicalIndicators::calculateAllIndicatorsBatch(
    const std::vector<GPUPriceData>& price_data,
    const IndicatorConfig& config,
    AllIndicatorResults& results) {
    
    if (price_data.empty()) {
        return false;
    }
    
    auto start_time = std::chrono::high_resolution_clock::now();
    
    bool success = false;
    
#ifdef ASTROBOY_ENABLE_CUDA
    if (use_gpu_) {
        success = calculateAllIndicatorsGPU(price_data, config, results);
        results.used_gpu = true;
    } else {
        success = calculateAllIndicatorsCPU(price_data, config, results);
        results.used_gpu = false;
    }
#else
    success = calculateAllIndicatorsCPU(price_data, config, results);
    results.used_gpu = false;
#endif
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
    results.computation_time = duration.count() / 1000.0; // 转换为毫秒
    
    // 更新性能统计
    if (results.used_gpu) {
        accelerator_->updatePerformanceStats(results.computation_time, 0.0);
    } else {
        accelerator_->updatePerformanceStats(0.0, results.computation_time);
    }
    
    return success;
}

bool V100TechnicalIndicators::calculateAllIndicatorsCPU(
    const std::vector<GPUPriceData>& price_data,
    const IndicatorConfig& config,
    AllIndicatorResults& results) {
    
    size_t data_size = price_data.size();
    if (data_size < 50) {
        return false;
    }
    
    // 提取价格数据
    std::vector<float> closes, highs, lows, volumes;
    closes.reserve(data_size);
    highs.reserve(data_size);
    lows.reserve(data_size);
    volumes.reserve(data_size);
    
    for (const auto& data : price_data) {
        closes.push_back(data.close);
        highs.push_back(data.high);
        lows.push_back(data.low);
        volumes.push_back(data.volume);
    }
    
    // 计算移动平均线
    results.ma_results.resize(config.ma_periods.size());
    for (size_t i = 0; i < config.ma_periods.size(); i++) {
        int period = config.ma_periods[i];
        results.ma_results[i].resize(data_size, 0.0f);
        
        for (size_t j = period - 1; j < data_size; j++) {
            float sum = 0.0f;
            for (int k = 0; k < period; k++) {
                sum += closes[j - k];
            }
            results.ma_results[i][j] = sum / period;
        }
    }
    
    // 计算RSI
    results.rsi_results.resize(data_size, 50.0f);
    if (data_size > config.rsi_period) {
        for (size_t i = config.rsi_period; i < data_size; i++) {
            float gain = 0.0f, loss = 0.0f;
            
            for (int j = 1; j <= config.rsi_period; j++) {
                float change = closes[i - j + 1] - closes[i - j];
                if (change > 0) {
                    gain += change;
                } else {
                    loss += -change;
                }
            }
            
            float avg_gain = gain / config.rsi_period;
            float avg_loss = loss / config.rsi_period;
            
            if (avg_loss > 0) {
                float rs = avg_gain / avg_loss;
                results.rsi_results[i] = 100.0f - (100.0f / (1.0f + rs));
            } else {
                results.rsi_results[i] = 100.0f;
            }
        }
    }
    
    // 计算MACD
    results.macd_dif.resize(data_size, 0.0f);
    results.macd_dea.resize(data_size, 0.0f);
    results.macd_histogram.resize(data_size, 0.0f);
    
    // 简化的MACD计算
    if (data_size > config.macd.slow) {
        // 计算EMA
        std::vector<float> ema_fast(data_size), ema_slow(data_size);
        
        // 初始化
        ema_fast[0] = ema_slow[0] = closes[0];
        
        float alpha_fast = 2.0f / (config.macd.fast + 1);
        float alpha_slow = 2.0f / (config.macd.slow + 1);
        
        for (size_t i = 1; i < data_size; i++) {
            ema_fast[i] = alpha_fast * closes[i] + (1 - alpha_fast) * ema_fast[i-1];
            ema_slow[i] = alpha_slow * closes[i] + (1 - alpha_slow) * ema_slow[i-1];
            results.macd_dif[i] = ema_fast[i] - ema_slow[i];
        }
        
        // 计算DEA
        results.macd_dea[0] = results.macd_dif[0];
        float alpha_signal = 2.0f / (config.macd.signal + 1);
        
        for (size_t i = 1; i < data_size; i++) {
            results.macd_dea[i] = alpha_signal * results.macd_dif[i] + 
                                 (1 - alpha_signal) * results.macd_dea[i-1];
            results.macd_histogram[i] = (results.macd_dif[i] - results.macd_dea[i]) * 2;
        }
    }
    
    // 计算布林带
    results.bb_upper.resize(data_size, 0.0f);
    results.bb_middle.resize(data_size, 0.0f);
    results.bb_lower.resize(data_size, 0.0f);
    
    for (size_t i = config.bollinger.period - 1; i < data_size; i++) {
        // 计算移动平均
        float sum = 0.0f;
        for (int j = 0; j < config.bollinger.period; j++) {
            sum += closes[i - j];
        }
        float ma = sum / config.bollinger.period;
        results.bb_middle[i] = ma;
        
        // 计算标准差
        float variance = 0.0f;
        for (int j = 0; j < config.bollinger.period; j++) {
            float diff = closes[i - j] - ma;
            variance += diff * diff;
        }
        float std_dev = std::sqrt(variance / config.bollinger.period);
        
        results.bb_upper[i] = ma + config.bollinger.std_mult * std_dev;
        results.bb_lower[i] = ma - config.bollinger.std_mult * std_dev;
    }
    
    // 计算KDJ
    results.kdj_k.resize(data_size, 50.0f);
    results.kdj_d.resize(data_size, 50.0f);
    results.kdj_j.resize(data_size, 50.0f);
    
    for (size_t i = config.kdj.period - 1; i < data_size; i++) {
        // 计算RSV
        float highest = highs[i - config.kdj.period + 1];
        float lowest = lows[i - config.kdj.period + 1];
        
        for (int j = 0; j < config.kdj.period; j++) {
            highest = std::max(highest, highs[i - j]);
            lowest = std::min(lowest, lows[i - j]);
        }
        
        float rsv = 50.0f;
        if (highest != lowest) {
            rsv = (closes[i] - lowest) / (highest - lowest) * 100.0f;
        }
        
        // 计算K值
        if (i == config.kdj.period - 1) {
            results.kdj_k[i] = rsv;
        } else {
            results.kdj_k[i] = (rsv + (config.kdj.k_period - 1) * results.kdj_k[i-1]) / config.kdj.k_period;
        }
        
        // 计算D值
        if (i == config.kdj.period - 1) {
            results.kdj_d[i] = results.kdj_k[i];
        } else {
            results.kdj_d[i] = (results.kdj_k[i] + (config.kdj.d_period - 1) * results.kdj_d[i-1]) / config.kdj.d_period;
        }
        
        // 计算J值
        results.kdj_j[i] = 3 * results.kdj_k[i] - 2 * results.kdj_d[i];
    }
    
    // 计算波动率
    results.volatility_results.resize(config.volatility_periods.size());
    for (size_t i = 0; i < config.volatility_periods.size(); i++) {
        int period = config.volatility_periods[i];
        results.volatility_results[i].resize(data_size, 0.0f);
        
        for (size_t j = period; j < data_size; j++) {
            std::vector<float> returns;
            for (int k = 1; k <= period; k++) {
                float ret = std::log(closes[j - k + 1] / closes[j - k]);
                returns.push_back(ret);
            }
            
            float mean = 0.0f;
            for (float ret : returns) {
                mean += ret;
            }
            mean /= returns.size();
            
            float variance = 0.0f;
            for (float ret : returns) {
                variance += (ret - mean) * (ret - mean);
            }
            
            results.volatility_results[i][j] = std::sqrt(variance / returns.size()) * std::sqrt(252.0f);
        }
    }
    
    return true;
}

#ifdef ASTROBOY_ENABLE_CUDA
bool V100TechnicalIndicators::calculateAllIndicatorsGPU(
    const std::vector<GPUPriceData>& price_data,
    const IndicatorConfig& config,
    AllIndicatorResults& results) {
    
    // GPU版本的实现将在下一个文件中完成
    // 这里先调用CPU版本作为备用
    return calculateAllIndicatorsCPU(price_data, config, results);
}

float* V100TechnicalIndicators::getGPUBuffer(size_t size) {
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    
    // 查找合适大小的缓冲区
    for (size_t i = 0; i < gpu_buffers_.size(); i++) {
        if (buffer_sizes_[i] >= size) {
            float* buffer = gpu_buffers_[i];
            gpu_buffers_.erase(gpu_buffers_.begin() + i);
            buffer_sizes_.erase(buffer_sizes_.begin() + i);
            return buffer;
        }
    }
    
    // 分配新缓冲区
    float* buffer = static_cast<float*>(accelerator_->allocateGPUMemory(size * sizeof(float)));
    return buffer;
}

void V100TechnicalIndicators::cleanupGPUBuffers() {
    std::lock_guard<std::mutex> lock(buffer_mutex_);
    
    for (float* buffer : gpu_buffers_) {
        accelerator_->freeGPUMemory(buffer);
    }
    
    gpu_buffers_.clear();
    buffer_sizes_.clear();
}
#endif

// V100FeatureEngine 实现
V100FeatureEngine::V100FeatureEngine()
    : accelerator_(V100AcceleratorManager::getInstance())
    , indicators_(nullptr)
    , use_gpu_(false) {

    if (accelerator_->initialize()) {
        use_gpu_ = accelerator_->isCudaAvailable();
        indicators_ = new V100TechnicalIndicators();
        indicators_->setGPUEnabled(use_gpu_);

        if (use_gpu_) {
            std::cout << "V100特征工程引擎初始化成功 (GPU模式)" << std::endl;
        } else {
            std::cout << "V100特征工程引擎初始化成功 (CPU模式)" << std::endl;
        }
    }
}

V100FeatureEngine::~V100FeatureEngine() {
    delete indicators_;
}

std::vector<float> V100FeatureEngine::FeatureVector::getAllFeatures() const {
    std::vector<float> all_features;

    all_features.insert(all_features.end(), technical_features.begin(), technical_features.end());
    all_features.insert(all_features.end(), price_features.begin(), price_features.end());
    all_features.insert(all_features.end(), volume_features.begin(), volume_features.end());
    all_features.insert(all_features.end(), volatility_features.begin(), volatility_features.end());
    all_features.insert(all_features.end(), pattern_features.begin(), pattern_features.end());
    all_features.insert(all_features.end(), market_features.begin(), market_features.end());

    return all_features;
}

size_t V100FeatureEngine::FeatureVector::getFeatureCount() const {
    return technical_features.size() + price_features.size() + volume_features.size() +
           volatility_features.size() + pattern_features.size() + market_features.size();
}

void V100FeatureEngine::FeatureVector::clear() {
    technical_features.clear();
    price_features.clear();
    volume_features.clear();
    volatility_features.clear();
    pattern_features.clear();
    market_features.clear();
}

bool V100FeatureEngine::extractFeatures(const std::vector<GPUPriceData>& price_data,
                                        const FeatureConfig& config,
                                        FeatureVector& features) {

    if (price_data.size() < config.lookback_period) {
        return false;
    }

    features.clear();

    // 提取技术指标特征
    if (config.include_technical) {
        if (!extractTechnicalFeatures(price_data, config, features.technical_features)) {
            return false;
        }
    }

    // 提取价格特征
    if (config.include_price) {
        if (!extractPriceFeatures(price_data, config, features.price_features)) {
            return false;
        }
    }

    // 提取成交量特征
    if (config.include_volume) {
        if (!extractVolumeFeatures(price_data, config, features.volume_features)) {
            return false;
        }
    }

    // 提取波动率特征
    if (config.include_volatility) {
        if (!extractVolatilityFeatures(price_data, config, features.volatility_features)) {
            return false;
        }
    }

    // 提取形态特征
    if (config.include_pattern) {
        if (!extractPatternFeatures(price_data, config, features.pattern_features)) {
            return false;
        }
    }

    // 提取市场特征
    if (config.include_market) {
        if (!extractMarketFeatures(price_data, config, features.market_features)) {
            return false;
        }
    }

    // 特征标准化
    if (config.normalize_features) {
        auto all_features = features.getAllFeatures();
        if (!normalizeFeatures(all_features)) {
            return false;
        }

        // 重新分配标准化后的特征
        size_t offset = 0;
        if (config.include_technical) {
            for (size_t i = 0; i < features.technical_features.size(); i++) {
                features.technical_features[i] = all_features[offset + i];
            }
            offset += features.technical_features.size();
        }

        if (config.include_price) {
            for (size_t i = 0; i < features.price_features.size(); i++) {
                features.price_features[i] = all_features[offset + i];
            }
            offset += features.price_features.size();
        }

        // 继续其他特征类型...
    }

    return true;
}

bool V100FeatureEngine::extractTechnicalFeatures(const std::vector<GPUPriceData>& price_data,
                                                 const FeatureConfig& config,
                                                 std::vector<float>& features) {

    if (!indicators_) {
        return false;
    }

    // 配置技术指标
    V100TechnicalIndicators::IndicatorConfig indicator_config;
    indicator_config.ma_periods = {5, 10, 20, 50};
    indicator_config.rsi_period = 14;

    // 计算技术指标
    V100TechnicalIndicators::AllIndicatorResults results;
    if (!indicators_->calculateAllIndicatorsBatch(price_data, indicator_config, results)) {
        return false;
    }

    // 提取最新的指标值作为特征
    size_t last_idx = price_data.size() - 1;

    // MA特征
    for (const auto& ma_result : results.ma_results) {
        if (!ma_result.empty()) {
            features.push_back(ma_result[last_idx]);
            // MA相对偏差
            features.push_back((price_data[last_idx].close - ma_result[last_idx]) / ma_result[last_idx]);
        }
    }

    // RSI特征
    if (!results.rsi_results.empty()) {
        features.push_back(results.rsi_results[last_idx] / 100.0f);  // 标准化到0-1
        features.push_back((results.rsi_results[last_idx] - 50.0f) / 50.0f);  // 中性偏差
    }

    // MACD特征
    if (!results.macd_dif.empty()) {
        features.push_back(results.macd_dif[last_idx]);
        features.push_back(results.macd_dea[last_idx]);
        features.push_back(results.macd_histogram[last_idx]);
    }

    // 布林带特征
    if (!results.bb_middle.empty()) {
        float bb_position = (price_data[last_idx].close - results.bb_lower[last_idx]) /
                           (results.bb_upper[last_idx] - results.bb_lower[last_idx]);
        features.push_back(bb_position);

        float bb_width = (results.bb_upper[last_idx] - results.bb_lower[last_idx]) /
                        results.bb_middle[last_idx];
        features.push_back(bb_width);
    }

    // KDJ特征
    if (!results.kdj_k.empty()) {
        features.push_back(results.kdj_k[last_idx] / 100.0f);
        features.push_back(results.kdj_d[last_idx] / 100.0f);
        features.push_back(results.kdj_j[last_idx] / 100.0f);
    }

    return true;
}

bool V100FeatureEngine::extractPriceFeatures(const std::vector<GPUPriceData>& price_data,
                                             const FeatureConfig& config,
                                             std::vector<float>& features) {

    size_t data_size = price_data.size();
    if (data_size < 20) {
        return false;
    }

    const auto& current = price_data.back();

    // 价格变化率特征
    std::vector<int> periods = {1, 3, 5, 10, 20};
    for (int period : periods) {
        if (data_size > period) {
            float change = (current.close - price_data[data_size - 1 - period].close) /
                          price_data[data_size - 1 - period].close;
            features.push_back(change);
        }
    }

    // 高低价特征
    features.push_back((current.high - current.low) / current.close);  // 日内波幅
    features.push_back((current.close - current.low) / (current.high - current.low)); // 收盘位置
    features.push_back((current.open - current.close) / current.close); // 开收盘偏差

    // 价格形态特征
    if (data_size >= 3) {
        // 连续上涨/下跌天数
        int up_days = 0, down_days = 0;
        for (size_t i = data_size - 1; i > 0 && i > data_size - 10; i--) {
            if (price_data[i].close > price_data[i-1].close) {
                if (down_days == 0) up_days++;
                else break;
            } else if (price_data[i].close < price_data[i-1].close) {
                if (up_days == 0) down_days++;
                else break;
            } else {
                break;
            }
        }
        features.push_back(up_days / 10.0f);
        features.push_back(down_days / 10.0f);
    }

    // 价格动量特征
    if (data_size >= 10) {
        float momentum_5 = (current.close - price_data[data_size - 6].close) / price_data[data_size - 6].close;
        float momentum_10 = (current.close - price_data[data_size - 11].close) / price_data[data_size - 11].close;
        features.push_back(momentum_5);
        features.push_back(momentum_10);
        features.push_back(momentum_5 - momentum_10);  // 动量差
    }

    return true;
}

bool V100FeatureEngine::extractVolumeFeatures(const std::vector<GPUPriceData>& price_data,
                                              const FeatureConfig& config,
                                              std::vector<float>& features) {

    size_t data_size = price_data.size();
    if (data_size < 20) {
        return false;
    }

    // 成交量移动平均
    float vol_ma5 = 0.0f, vol_ma20 = 0.0f;
    for (int i = 0; i < 5; i++) {
        vol_ma5 += price_data[data_size - 1 - i].volume;
    }
    vol_ma5 /= 5.0f;

    for (int i = 0; i < 20; i++) {
        vol_ma20 += price_data[data_size - 1 - i].volume;
    }
    vol_ma20 /= 20.0f;

    features.push_back(price_data.back().volume / vol_ma5 - 1.0f);  // 当日成交量相对5日均量
    features.push_back(price_data.back().volume / vol_ma20 - 1.0f); // 当日成交量相对20日均量
    features.push_back(vol_ma5 / vol_ma20 - 1.0f);                  // 5日均量相对20日均量

    // 量价关系
    if (data_size >= 2) {
        float price_change = (price_data.back().close - price_data[data_size - 2].close) /
                            price_data[data_size - 2].close;
        float volume_change = (price_data.back().volume - price_data[data_size - 2].volume) /
                             price_data[data_size - 2].volume;

        features.push_back(price_change * volume_change);  // 量价协同性
    }

    // 成交量波动率
    if (data_size >= 10) {
        std::vector<float> vol_changes;
        for (size_t i = data_size - 9; i < data_size; i++) {
            float change = (price_data[i].volume - price_data[i-1].volume) / price_data[i-1].volume;
            vol_changes.push_back(change);
        }

        float mean = 0.0f;
        for (float change : vol_changes) {
            mean += change;
        }
        mean /= vol_changes.size();

        float variance = 0.0f;
        for (float change : vol_changes) {
            variance += (change - mean) * (change - mean);
        }

        float vol_volatility = std::sqrt(variance / vol_changes.size());
        features.push_back(vol_volatility);
    }

    return true;
}

bool V100FeatureEngine::extractVolatilityFeatures(const std::vector<GPUPriceData>& price_data,
                                                  const FeatureConfig& config,
                                                  std::vector<float>& features) {

    size_t data_size = price_data.size();
    if (data_size < 30) {
        return false;
    }

    // 计算不同周期的波动率
    std::vector<int> periods = {5, 10, 20};
    for (int period : periods) {
        if (data_size > period) {
            std::vector<float> returns;
            for (int i = 1; i <= period; i++) {
                float ret = std::log(price_data[data_size - i].close / price_data[data_size - i - 1].close);
                returns.push_back(ret);
            }

            float mean = 0.0f;
            for (float ret : returns) {
                mean += ret;
            }
            mean /= returns.size();

            float variance = 0.0f;
            for (float ret : returns) {
                variance += (ret - mean) * (ret - mean);
            }

            float volatility = std::sqrt(variance / returns.size()) * std::sqrt(252.0f);
            features.push_back(volatility);
        }
    }

    // 波动率比率
    if (features.size() >= 3) {
        features.push_back(features[0] / features[2]);  // 5日波动率 / 20日波动率
    }

    // ATR (Average True Range)
    if (data_size >= 15) {
        float atr_sum = 0.0f;
        for (int i = 1; i <= 14; i++) {
            const auto& current = price_data[data_size - i];
            const auto& previous = price_data[data_size - i - 1];

            float tr = std::max({
                current.high - current.low,
                std::abs(current.high - previous.close),
                std::abs(current.low - previous.close)
            });
            atr_sum += tr;
        }

        float atr = atr_sum / 14.0f;
        features.push_back(atr / price_data.back().close);
    }

    return true;
}

bool V100FeatureEngine::extractPatternFeatures(const std::vector<GPUPriceData>& price_data,
                                               const FeatureConfig& config,
                                               std::vector<float>& features) {

    size_t data_size = price_data.size();
    if (data_size < 10) {
        return false;
    }

    const auto& current = price_data.back();

    // 检测经典K线形态

    // 1. 锤子线
    float body = std::abs(current.close - current.open);
    float lower_shadow = std::min(current.open, current.close) - current.low;
    float upper_shadow = current.high - std::max(current.open, current.close);

    bool is_hammer = (lower_shadow > 2 * body) && (upper_shadow < 0.5 * body);
    features.push_back(is_hammer ? 1.0f : 0.0f);

    // 2. 十字星
    float range = current.high - current.low;
    bool is_doji = (body / range) < 0.1f;
    features.push_back(is_doji ? 1.0f : 0.0f);

    // 3. 吞没形态
    if (data_size >= 2) {
        const auto& previous = price_data[data_size - 2];

        bool current_bullish = current.close > current.open;
        bool previous_bearish = previous.close < previous.open;

        bool is_engulfing = current_bullish && previous_bearish &&
                           current.open < previous.close && current.close > previous.open;
        features.push_back(is_engulfing ? 1.0f : 0.0f);
    }

    // 4. 跳空形态
    if (data_size >= 2) {
        const auto& previous = price_data[data_size - 2];

        float gap_up = current.low - previous.high;
        float gap_down = previous.low - current.high;

        bool has_gap = (gap_up > 0) || (gap_down > 0);
        features.push_back(has_gap ? 1.0f : 0.0f);
    }

    // 5. 趋势强度
    if (data_size >= 10) {
        float start_price = price_data[data_size - 10].close;
        float end_price = current.close;
        float trend_strength = (end_price - start_price) / start_price;
        features.push_back(trend_strength);
    }

    return true;
}

bool V100FeatureEngine::extractMarketFeatures(const std::vector<GPUPriceData>& price_data,
                                              const FeatureConfig& config,
                                              std::vector<float>& features) {

    size_t data_size = price_data.size();
    if (data_size < 50) {
        return false;
    }

    // 市场状态特征

    // 1. 趋势方向
    float long_trend = (price_data.back().close - price_data[data_size - 50].close) /
                      price_data[data_size - 50].close;
    float short_trend = (price_data.back().close - price_data[data_size - 20].close) /
                       price_data[data_size - 20].close;

    features.push_back(long_trend);
    features.push_back(short_trend);
    features.push_back(short_trend - long_trend);  // 趋势加速度

    // 2. 市场波动状态
    float recent_volatility = 0.0f;
    float historical_volatility = 0.0f;

    // 计算最近10天波动率
    for (int i = 1; i <= 10; i++) {
        float ret = std::log(price_data[data_size - i].close / price_data[data_size - i - 1].close);
        recent_volatility += ret * ret;
    }
    recent_volatility = std::sqrt(recent_volatility / 10) * std::sqrt(252);

    // 计算历史50天波动率
    for (int i = 1; i <= 50; i++) {
        float ret = std::log(price_data[data_size - i].close / price_data[data_size - i - 1].close);
        historical_volatility += ret * ret;
    }
    historical_volatility = std::sqrt(historical_volatility / 50) * std::sqrt(252);

    features.push_back(recent_volatility);
    features.push_back(historical_volatility);
    features.push_back(recent_volatility / historical_volatility);  // 波动率比率

    // 3. 价格位置
    float highest_50 = price_data[data_size - 50].high;
    float lowest_50 = price_data[data_size - 50].low;

    for (int i = 1; i < 50; i++) {
        highest_50 = std::max(highest_50, price_data[data_size - i].high);
        lowest_50 = std::min(lowest_50, price_data[data_size - i].low);
    }

    float price_position = (price_data.back().close - lowest_50) / (highest_50 - lowest_50);
    features.push_back(price_position);

    return true;
}

bool V100FeatureEngine::normalizeFeatures(std::vector<float>& features) {
    if (features.empty()) {
        return false;
    }

    // Z-score标准化
    float mean = 0.0f;
    for (float feature : features) {
        mean += feature;
    }
    mean /= features.size();

    float variance = 0.0f;
    for (float feature : features) {
        variance += (feature - mean) * (feature - mean);
    }
    variance /= features.size();

    float std_dev = std::sqrt(variance);
    if (std_dev > 0) {
        for (float& feature : features) {
            feature = (feature - mean) / std_dev;
        }
    }

    return true;
}

} // namespace gpu
} // namespace astroboy
