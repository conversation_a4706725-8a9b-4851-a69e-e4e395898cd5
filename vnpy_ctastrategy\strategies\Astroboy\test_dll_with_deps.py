#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试DLL并解决依赖问题
"""

import os
import sys
import ctypes
import numpy as np
import time
from pathlib import Path

def install_vcredist():
    """安装Visual C++运行时库"""
    print("检查Visual C++运行时库...")
    
    # 检查常见的运行时库文件
    system32 = Path("C:/Windows/System32")
    vcredist_files = [
        "msvcp140.dll",
        "vcruntime140.dll", 
        "vcruntime140_1.dll",
        "msvcp140_1.dll",
        "msvcp140_2.dll"
    ]
    
    missing_files = []
    for file in vcredist_files:
        if not (system32 / file).exists():
            missing_files.append(file)
    
    if missing_files:
        print(f"缺少运行时库文件: {missing_files}")
        print("请安装 Microsoft Visual C++ Redistributable")
        print("下载地址: https://aka.ms/vs/17/release/vc_redist.x64.exe")
        return False
    else:
        print("Visual C++运行时库检查通过")
        return True

def test_dll_with_full_path():
    """使用完整路径测试DLL"""
    print("=== 使用完整路径测试DLL ===")
    
    # 获取当前目录的完整路径
    current_dir = Path(__file__).parent.absolute()
    
    # 尝试不同的DLL文件
    dll_candidates = [
        current_dir / "astroboy_working.dll",
        current_dir / "simple_build" / "atomboy_signal.dll",
        current_dir / "astroboy_fixed.dll"
    ]
    
    for dll_path in dll_candidates:
        if dll_path.exists():
            print(f"尝试加载: {dll_path}")
            
            try:
                # 使用完整路径加载
                lib = ctypes.CDLL(str(dll_path))
                
                # 测试基本功能
                lib.getVersion.restype = ctypes.c_char_p
                lib.getVersion.argtypes = []
                
                version = lib.getVersion()
                print(f"✓ 成功加载DLL: {dll_path.name}")
                print(f"  版本: {version.decode('utf-8')}")
                
                # 测试其他功能
                lib.add.restype = ctypes.c_int
                lib.add.argtypes = [ctypes.c_int, ctypes.c_int]
                
                result = lib.add(5, 3)
                print(f"  加法测试: 5 + 3 = {result}")
                
                return lib, str(dll_path)
                
            except Exception as e:
                print(f"✗ 加载失败: {dll_path.name}")
                print(f"  错误: {str(e)}")
                continue
    
    print("所有DLL加载失败")
    return None, None

def test_dll_functions(lib):
    """测试DLL函数"""
    print("\n=== 测试DLL函数 ===")
    
    try:
        # 测试Hello函数
        if hasattr(lib, 'printHello'):
            lib.printHello.restype = None
            lib.printHello.argtypes = []
            lib.printHello()
            print("✓ printHello 测试通过")
        
        # 检查技术指标函数
        indicator_functions = ['calculateMA', 'calculateRSI', 'generateSignal']
        available_functions = []
        
        for func_name in indicator_functions:
            if hasattr(lib, func_name):
                available_functions.append(func_name)
                print(f"✓ {func_name} 可用")
            else:
                print(f"✗ {func_name} 不可用")
        
        # 测试移动平均计算
        if 'calculateMA' in available_functions:
            print("\n测试移动平均计算...")
            lib.calculateMA.restype = ctypes.c_double
            lib.calculateMA.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
            
            # 测试数据
            prices = [100.0, 101.0, 102.0, 101.5, 103.0, 102.5, 104.0, 103.5, 105.0, 104.5]
            price_array = (ctypes.c_double * len(prices))(*prices)
            
            ma5 = lib.calculateMA(price_array, len(prices), 5)
            ma10 = lib.calculateMA(price_array, len(prices), 10)
            
            print(f"  MA5: {ma5:.4f}")
            print(f"  MA10: {ma10:.4f}")
        
        # 测试RSI计算
        if 'calculateRSI' in available_functions:
            print("\n测试RSI计算...")
            lib.calculateRSI.restype = ctypes.c_double
            lib.calculateRSI.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
            
            rsi = lib.calculateRSI(price_array, len(prices), 14)
            print(f"  RSI: {rsi:.4f}")
        
        # 测试信号生成
        if 'generateSignal' in available_functions:
            print("\n测试信号生成...")
            lib.generateSignal.restype = ctypes.c_int
            lib.generateSignal.argtypes = [ctypes.c_double, ctypes.c_double, ctypes.c_double]
            
            signal_types = {0: "NONE", 1: "BUY", 2: "SELL", 3: "HOLD"}
            
            test_cases = [
                (100.0, 102.0, 98.0, "大幅波动"),
                (100.0, 100.5, 99.5, "小幅波动"),
                (100.0, 100.0, 100.0, "无波动"),
                (100.0, 105.0, 95.0, "极大波动")
            ]
            
            for open_p, high_p, low_p, desc in test_cases:
                signal = lib.generateSignal(open_p, high_p, low_p)
                signal_name = signal_types.get(signal, "UNKNOWN")
                print(f"  {desc}: 开{open_p} 高{high_p} 低{low_p} -> {signal_name}")
        
        return True
        
    except Exception as e:
        print(f"函数测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def performance_test(lib):
    """性能测试"""
    print("\n=== 性能测试 ===")
    
    if not hasattr(lib, 'calculateMA'):
        print("跳过性能测试 - calculateMA不可用")
        return
    
    try:
        # 生成大量测试数据
        np.random.seed(42)
        data_sizes = [100, 500, 1000, 5000]
        
        for data_size in data_sizes:
            print(f"\n测试数据量: {data_size}")
            
            # 生成价格数据
            prices = []
            base_price = 100.0
            
            for i in range(data_size):
                change = np.random.normal(0, 0.02)
                base_price *= (1 + change)
                prices.append(base_price)
            
            price_array = (ctypes.c_double * len(prices))(*prices)
            
            # 测试不同周期的MA计算
            periods = [5, 10, 20, 50]
            start_time = time.time()
            
            results = []
            for period in periods:
                if len(prices) >= period:
                    ma_result = lib.calculateMA(price_array, len(prices), period)
                    results.append((period, ma_result))
            
            end_time = time.time()
            computation_time = (end_time - start_time) * 1000
            
            print(f"  计算用时: {computation_time:.2f}ms")
            print(f"  平均每次: {computation_time/len(periods):.2f}ms")
            
            for period, result in results:
                print(f"    MA{period}: {result:.4f}")
    
    except Exception as e:
        print(f"性能测试失败: {str(e)}")

def create_final_wrapper(dll_path):
    """创建最终的包装器"""
    print(f"\n=== 创建最终包装器 ===")
    
    wrapper_code = f'''#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
阿童木信号生成器最终版本Python包装器
自动生成于编译测试过程
"""

import os
import sys
import ctypes
import numpy as np
from typing import List, Optional, Union, Dict, Any
from pathlib import Path

class AstroboySignalGenerator:
    """阿童木信号生成器最终版本"""
    
    def __init__(self, dll_path: Optional[str] = None):
        if dll_path is None:
            # 使用测试通过的DLL路径
            dll_path = r"{dll_path}"
        
        if not os.path.exists(dll_path):
            raise FileNotFoundError(f"找不到DLL文件: {{dll_path}}")
        
        self.dll_path = dll_path
        self.lib = None
        self.available_functions = []
        self._load_dll()
    
    def _load_dll(self):
        """加载DLL"""
        try:
            # 使用绝对路径加载DLL
            self.lib = ctypes.CDLL(self.dll_path)
            self._setup_function_prototypes()
            self._detect_available_functions()
        except Exception as e:
            raise RuntimeError(f"加载DLL失败: {{str(e)}}")
    
    def _setup_function_prototypes(self):
        """设置函数原型"""
        # 基本功能
        self.lib.getVersion.restype = ctypes.c_char_p
        self.lib.getVersion.argtypes = []
        
        self.lib.add.restype = ctypes.c_int
        self.lib.add.argtypes = [ctypes.c_int, ctypes.c_int]
        
        if hasattr(self.lib, 'printHello'):
            self.lib.printHello.restype = None
            self.lib.printHello.argtypes = []
        
        # 技术指标计算
        if hasattr(self.lib, 'calculateMA'):
            self.lib.calculateMA.restype = ctypes.c_double
            self.lib.calculateMA.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
        
        if hasattr(self.lib, 'calculateRSI'):
            self.lib.calculateRSI.restype = ctypes.c_double
            self.lib.calculateRSI.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
        
        if hasattr(self.lib, 'generateSignal'):
            self.lib.generateSignal.restype = ctypes.c_int
            self.lib.generateSignal.argtypes = [ctypes.c_double, ctypes.c_double, ctypes.c_double]
    
    def _detect_available_functions(self):
        """检测可用函数"""
        test_functions = [
            'calculateMA', 'calculateRSI', 'generateSignal', 
            'calculateMACD', 'calculateBollingerBands', 'calculateKDJ',
            'printHello'
        ]
        
        for func_name in test_functions:
            if hasattr(self.lib, func_name):
                self.available_functions.append(func_name)
    
    def get_version(self) -> str:
        """获取版本信息"""
        return self.lib.getVersion().decode('utf-8')
    
    def add(self, a: int, b: int) -> int:
        """加法测试"""
        return self.lib.add(a, b)
    
    def print_hello(self):
        """打印Hello"""
        if 'printHello' in self.available_functions:
            self.lib.printHello()
        else:
            print("Hello from Astroboy Signal Generator!")
    
    def calculate_ma(self, prices: List[float], period: int) -> float:
        """计算移动平均线"""
        if 'calculateMA' not in self.available_functions:
            raise NotImplementedError("calculateMA函数不可用")
        
        if len(prices) < period:
            raise ValueError(f"数据长度{{len(prices)}}小于周期{{period}}")
        
        price_array = (ctypes.c_double * len(prices))(*prices)
        return self.lib.calculateMA(price_array, len(prices), period)
    
    def calculate_rsi(self, prices: List[float], period: int = 14) -> float:
        """计算RSI"""
        if 'calculateRSI' not in self.available_functions:
            raise NotImplementedError("calculateRSI函数不可用")
        
        if len(prices) < period + 1:
            raise ValueError(f"数据长度{{len(prices)}}不足，需要至少{{period + 1}}条数据")
        
        price_array = (ctypes.c_double * len(prices))(*prices)
        return self.lib.calculateRSI(price_array, len(prices), period)
    
    def generate_signal(self, open_price: float, high_price: float, low_price: float) -> int:
        """生成信号"""
        if 'generateSignal' not in self.available_functions:
            raise NotImplementedError("generateSignal函数不可用")
        
        return self.lib.generateSignal(open_price, high_price, low_price)
    
    def get_signal_name(self, signal_code: int) -> str:
        """获取信号名称"""
        signal_names = {{0: "NONE", 1: "BUY", 2: "SELL", 3: "HOLD"}}
        return signal_names.get(signal_code, "UNKNOWN")
    
    def get_available_functions(self) -> List[str]:
        """获取可用函数列表"""
        return self.available_functions.copy()
    
    def analyze_market(self, prices: List[float], periods: List[int] = None) -> Dict[str, Any]:
        """市场分析"""
        if periods is None:
            periods = [5, 10, 20, 50]
        
        results = {{
            'prices_count': len(prices),
            'latest_price': prices[-1] if prices else 0,
            'ma_results': {{}},
            'rsi': None,
            'signal': None,
            'available_functions': self.available_functions
        }}
        
        # 计算多周期MA
        if 'calculateMA' in self.available_functions:
            for period in periods:
                if len(prices) >= period:
                    try:
                        ma_value = self.calculate_ma(prices, period)
                        results['ma_results'][f'MA{{period}}'] = ma_value
                    except Exception as e:
                        results['ma_results'][f'MA{{period}}'] = f"计算失败: {{str(e)}}"
        
        # 计算RSI
        if 'calculateRSI' in self.available_functions and len(prices) >= 15:
            try:
                results['rsi'] = self.calculate_rsi(prices, 14)
            except Exception as e:
                results['rsi'] = f"计算失败: {{str(e)}}"
        
        # 生成信号
        if 'generateSignal' in self.available_functions and len(prices) >= 3:
            try:
                # 使用最近3天的数据生成信号
                recent_prices = prices[-3:]
                open_p = recent_prices[0]
                high_p = max(recent_prices)
                low_p = min(recent_prices)
                
                signal_code = self.generate_signal(open_p, high_p, low_p)
                results['signal'] = {{
                    'code': signal_code,
                    'name': self.get_signal_name(signal_code),
                    'input': {{'open': open_p, 'high': high_p, 'low': low_p}}
                }}
            except Exception as e:
                results['signal'] = f"计算失败: {{str(e)}}"
        
        return results
    
    def batch_calculate_ma(self, prices: List[float], periods: List[int]) -> Dict[int, float]:
        """批量计算移动平均线"""
        results = {{}}
        
        if 'calculateMA' not in self.available_functions:
            return results
        
        for period in periods:
            if len(prices) >= period:
                try:
                    ma_value = self.calculate_ma(prices, period)
                    results[period] = ma_value
                except Exception as e:
                    results[period] = None
        
        return results

# 兼容性别名
AtomBoySignalGenerator = AstroboySignalGenerator

# 便捷函数
def create_generator(dll_path: Optional[str] = None) -> AstroboySignalGenerator:
    """创建信号生成器实例"""
    return AstroboySignalGenerator(dll_path)

def quick_analysis(prices: List[float]) -> Dict[str, Any]:
    """快速市场分析"""
    generator = create_generator()
    return generator.analyze_market(prices)
'''
    
    try:
        with open("astroboy_wrapper_final.py", "w", encoding="utf-8") as f:
            f.write(wrapper_code)
        
        print("最终包装器已创建: astroboy_wrapper_final.py")
        return True
        
    except Exception as e:
        print(f"创建包装器失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("阿童木信号生成器DLL依赖修复和测试")
    print("=" * 60)
    
    # 切换到脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"工作目录: {os.getcwd()}")
    
    # 检查运行时库
    if not install_vcredist():
        print("请安装Visual C++运行时库后重试")
        return
    
    # 测试DLL加载
    lib, dll_path = test_dll_with_full_path()
    
    if lib is None:
        print("\n所有DLL测试失败")
        print("可能的解决方案:")
        print("1. 安装 Microsoft Visual C++ Redistributable")
        print("2. 重新编译DLL")
        print("3. 检查DLL文件完整性")
        return
    
    # 测试DLL函数
    if test_dll_functions(lib):
        print("\n✓ DLL函数测试通过")
        
        # 性能测试
        performance_test(lib)
        
        # 创建最终包装器
        if create_final_wrapper(dll_path):
            print("\n=== 阿童木信号生成器编译完成 ===")
            print(f"✓ 成功的DLL: {dll_path}")
            print("✓ 最终包装器: astroboy_wrapper_final.py")
            print("\n使用方法:")
            print("from astroboy_wrapper_final import AstroboySignalGenerator")
            print("generator = AstroboySignalGenerator()")
            print("print(generator.get_version())")
            print("print(generator.analyze_market([100, 101, 102, 101.5, 103]))")
        
    else:
        print("\n✗ DLL函数测试失败")

if __name__ == "__main__":
    main()
