#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
阿童木信号生成器修复版DLL测试脚本
"""

import os
import sys
import ctypes
import numpy as np
import time

def test_fixed_dll():
    """测试修复版DLL"""
    print("=== 阿童木信号生成器修复版测试 ===")
    
    # 查找DLL文件
    dll_paths = [
        "astroboy_fixed.dll",
        "build_fixed/bin/Release/astroboy_fixed.dll",
        "build_fixed/Release/astroboy_fixed.dll"
    ]
    
    dll_path = None
    for path in dll_paths:
        if os.path.exists(path):
            dll_path = path
            break
    
    if not dll_path:
        print("错误: 找不到修复版DLL文件")
        print("请先运行 build_fixed.bat 编译DLL")
        return False
    
    try:
        # 加载DLL
        print(f"加载DLL: {dll_path}")
        lib = ctypes.CDLL(dll_path)
        
        # 设置函数原型
        print("设置函数原型...")
        
        # 基本函数
        lib.getVersion.restype = ctypes.c_char_p
        lib.getVersion.argtypes = []
        
        lib.add.restype = ctypes.c_int
        lib.add.argtypes = [ctypes.c_int, ctypes.c_int]
        
        lib.printHello.restype = None
        lib.printHello.argtypes = []
        
        # 测试基本功能
        print("\n1. 测试版本信息...")
        version = lib.getVersion()
        print(f"   版本: {version.decode('utf-8')}")
        
        print("2. 测试加法函数...")
        result = lib.add(5, 3)
        print(f"   5 + 3 = {result}")
        
        print("3. 测试Hello函数...")
        lib.printHello()
        
        # 测试技术指标计算
        print("\n4. 测试技术指标计算...")
        
        # 移动平均线
        if hasattr(lib, 'calculateMA'):
            lib.calculateMA.restype = ctypes.c_double
            lib.calculateMA.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
            
            # 准备测试数据
            prices = [100.0, 101.0, 102.0, 101.5, 103.0, 102.5, 104.0, 103.5, 105.0, 104.5]
            price_array = (ctypes.c_double * len(prices))(*prices)
            
            ma5 = lib.calculateMA(price_array, len(prices), 5)
            print(f"   MA5: {ma5:.4f}")
        
        # RSI
        if hasattr(lib, 'calculateRSI'):
            lib.calculateRSI.restype = ctypes.c_double
            lib.calculateRSI.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
            
            rsi = lib.calculateRSI(price_array, len(prices), 14)
            print(f"   RSI: {rsi:.4f}")
        
        # 信号生成
        if hasattr(lib, 'generateSignal'):
            print("\n5. 测试信号生成...")
            lib.generateSignal.restype = ctypes.c_int
            lib.generateSignal.argtypes = [ctypes.c_double, ctypes.c_double, ctypes.c_double]
            
            signal = lib.generateSignal(100.0, 101.0, 99.0)
            signal_types = {0: "NONE", 1: "BUY", 2: "SELL", 3: "HOLD"}
            print(f"   信号结果: {signal} ({signal_types.get(signal, 'UNKNOWN')})")
        
        # 批量数据测试
        print("\n6. 批量数据性能测试...")
        
        # 生成大量测试数据
        np.random.seed(42)
        large_data_size = 1000
        large_prices = []
        base_price = 100.0
        
        for i in range(large_data_size):
            change = np.random.normal(0, 0.02)
            base_price *= (1 + change)
            large_prices.append(base_price)
        
        large_price_array = (ctypes.c_double * len(large_prices))(*large_prices)
        
        # 测试批量MA计算性能
        if hasattr(lib, 'calculateMA'):
            start_time = time.time()
            
            for period in [5, 10, 20, 50]:
                ma_result = lib.calculateMA(large_price_array, len(large_prices), period)
                print(f"   MA{period}: {ma_result:.4f}")
            
            end_time = time.time()
            print(f"   批量计算用时: {(end_time - start_time)*1000:.2f}ms")
        
        print("\n=== 修复版DLL测试通过 ===")
        return True
        
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_wrapper_compatibility():
    """测试包装器兼容性"""
    print("\n=== 包装器兼容性测试 ===")
    
    try:
        # 尝试创建一个简化的包装器类
        class FixedAtomBoySignalGenerator:
            def __init__(self, dll_path="astroboy_fixed.dll"):
                self.dll_path = dll_path
                self.lib = None
                self._load_dll()
            
            def _load_dll(self):
                if not os.path.exists(self.dll_path):
                    raise FileNotFoundError(f"找不到DLL文件: {self.dll_path}")
                
                self.lib = ctypes.CDLL(self.dll_path)
                
                # 设置函数原型
                self.lib.getVersion.restype = ctypes.c_char_p
                self.lib.getVersion.argtypes = []
                
                self.lib.add.restype = ctypes.c_int
                self.lib.add.argtypes = [ctypes.c_int, ctypes.c_int]
            
            def get_version(self):
                return self.lib.getVersion().decode('utf-8')
            
            def add(self, a, b):
                return self.lib.add(a, b)
        
        # 测试包装器
        generator = FixedAtomBoySignalGenerator()
        print(f"包装器版本: {generator.get_version()}")
        print(f"包装器加法测试: {generator.add(10, 20)}")
        
        print("=== 包装器兼容性测试通过 ===")
        return True
        
    except Exception as e:
        print(f"包装器测试错误: {str(e)}")
        return False

def create_fixed_wrapper():
    """创建修复版包装器"""
    print("\n=== 创建修复版包装器 ===")
    
    wrapper_code = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
阿童木信号生成器修复版Python包装器
"""

import os
import sys
import ctypes

class FixedAtomBoySignalGenerator:
    """修复版阿童木信号生成器"""
    
    def __init__(self, dll_path=None):
        if dll_path is None:
            # 自动查找DLL
            current_dir = os.path.dirname(os.path.abspath(__file__))
            possible_paths = [
                os.path.join(current_dir, "astroboy_fixed.dll"),
                os.path.join(current_dir, "atomboy_signal.dll"),
                "astroboy_fixed.dll",
                "atomboy_signal.dll"
            ]
            
            for path in possible_paths:
                if os.path.exists(path):
                    dll_path = path
                    break
        
        if not dll_path or not os.path.exists(dll_path):
            raise FileNotFoundError(f"找不到DLL文件: {dll_path}")
        
        self.dll_path = dll_path
        self.lib = None
        self._load_dll()
    
    def _load_dll(self):
        """加载DLL"""
        try:
            self.lib = ctypes.CDLL(self.dll_path)
            self._setup_function_prototypes()
        except Exception as e:
            raise RuntimeError(f"加载DLL失败: {str(e)}")
    
    def _setup_function_prototypes(self):
        """设置函数原型"""
        # 基本功能
        self.lib.getVersion.restype = ctypes.c_char_p
        self.lib.getVersion.argtypes = []
        
        self.lib.add.restype = ctypes.c_int
        self.lib.add.argtypes = [ctypes.c_int, ctypes.c_int]
        
        self.lib.printHello.restype = None
        self.lib.printHello.argtypes = []
        
        # 技术指标计算
        if hasattr(self.lib, 'calculateMA'):
            self.lib.calculateMA.restype = ctypes.c_double
            self.lib.calculateMA.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
        
        if hasattr(self.lib, 'calculateRSI'):
            self.lib.calculateRSI.restype = ctypes.c_double
            self.lib.calculateRSI.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
        
        if hasattr(self.lib, 'generateSignal'):
            self.lib.generateSignal.restype = ctypes.c_int
            self.lib.generateSignal.argtypes = [ctypes.c_double, ctypes.c_double, ctypes.c_double]
    
    def get_version(self):
        """获取版本信息"""
        return self.lib.getVersion().decode('utf-8')
    
    def add(self, a, b):
        """加法测试"""
        return self.lib.add(a, b)
    
    def print_hello(self):
        """打印Hello"""
        self.lib.printHello()
    
    def calculate_ma(self, prices, period):
        """计算移动平均线"""
        if not hasattr(self.lib, 'calculateMA'):
            raise NotImplementedError("calculateMA函数不可用")
        
        price_array = (ctypes.c_double * len(prices))(*prices)
        return self.lib.calculateMA(price_array, len(prices), period)
    
    def calculate_rsi(self, prices, period=14):
        """计算RSI"""
        if not hasattr(self.lib, 'calculateRSI'):
            raise NotImplementedError("calculateRSI函数不可用")
        
        price_array = (ctypes.c_double * len(prices))(*prices)
        return self.lib.calculateRSI(price_array, len(prices), period)
    
    def generate_signal(self, open_price, high_price, low_price):
        """生成信号"""
        if not hasattr(self.lib, 'generateSignal'):
            raise NotImplementedError("generateSignal函数不可用")
        
        return self.lib.generateSignal(open_price, high_price, low_price)

# 兼容性别名
AtomBoySignalGenerator = FixedAtomBoySignalGenerator
'''
    
    try:
        with open("atomboy_wrapper_fixed.py", "w", encoding="utf-8") as f:
            f.write(wrapper_code)
        
        print("修复版包装器已创建: atomboy_wrapper_fixed.py")
        return True
        
    except Exception as e:
        print(f"创建包装器失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("阿童木信号生成器修复版DLL测试")
    print("=" * 50)
    
    # 切换到脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"工作目录: {os.getcwd()}")
    
    # 运行测试
    basic_ok = test_fixed_dll()
    
    if basic_ok:
        wrapper_ok = test_wrapper_compatibility()
        if wrapper_ok:
            create_fixed_wrapper()
    else:
        print("基础测试失败，跳过后续测试")
    
    print("\n测试完成")
