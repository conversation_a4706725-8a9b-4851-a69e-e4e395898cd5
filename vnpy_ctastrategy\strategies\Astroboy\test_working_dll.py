#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试工作版本的DLL
"""

import os
import sys
import ctypes
import numpy as np
import time

def test_working_dll():
    """测试工作版本DLL"""
    print("=== 阿童木信号生成器工作版本测试 ===")
    
    # 查找DLL文件
    dll_paths = [
        "astroboy_working.dll",
        "simple_build/atomboy_signal.dll"
    ]
    
    dll_path = None
    for path in dll_paths:
        if os.path.exists(path):
            dll_path = path
            break
    
    if not dll_path:
        print("错误: 找不到工作版本DLL文件")
        return False
    
    try:
        # 加载DLL
        print(f"加载DLL: {dll_path}")
        lib = ctypes.CDLL(dll_path)
        
        # 设置函数原型
        print("设置函数原型...")
        
        # 基本函数
        lib.getVersion.restype = ctypes.c_char_p
        lib.getVersion.argtypes = []
        
        lib.add.restype = ctypes.c_int
        lib.add.argtypes = [ctypes.c_int, ctypes.c_int]
        
        lib.printHello.restype = None
        lib.printHello.argtypes = []
        
        # 测试基本功能
        print("\n1. 测试版本信息...")
        version = lib.getVersion()
        print(f"   版本: {version.decode('utf-8')}")
        
        print("2. 测试加法函数...")
        result = lib.add(5, 3)
        print(f"   5 + 3 = {result}")
        
        print("3. 测试Hello函数...")
        lib.printHello()
        
        # 测试技术指标计算
        print("\n4. 测试技术指标计算...")
        
        # 检查可用函数
        available_functions = []
        test_functions = [
            'calculateMA', 'calculateRSI', 'generateSignal', 
            'calculateMACD', 'calculateBollingerBands', 'calculateKDJ'
        ]
        
        for func_name in test_functions:
            if hasattr(lib, func_name):
                available_functions.append(func_name)
                print(f"   ✓ {func_name} 可用")
            else:
                print(f"   ✗ {func_name} 不可用")
        
        # 测试可用的函数
        if 'calculateMA' in available_functions:
            print("\n5. 测试移动平均计算...")
            lib.calculateMA.restype = ctypes.c_double
            lib.calculateMA.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
            
            # 准备测试数据
            prices = [100.0, 101.0, 102.0, 101.5, 103.0, 102.5, 104.0, 103.5, 105.0, 104.5]
            price_array = (ctypes.c_double * len(prices))(*prices)
            
            ma5 = lib.calculateMA(price_array, len(prices), 5)
            print(f"   MA5: {ma5:.4f}")
            
            ma10 = lib.calculateMA(price_array, len(prices), 10)
            print(f"   MA10: {ma10:.4f}")
        
        if 'calculateRSI' in available_functions:
            print("\n6. 测试RSI计算...")
            lib.calculateRSI.restype = ctypes.c_double
            lib.calculateRSI.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
            
            rsi = lib.calculateRSI(price_array, len(prices), 14)
            print(f"   RSI: {rsi:.4f}")
        
        if 'generateSignal' in available_functions:
            print("\n7. 测试信号生成...")
            lib.generateSignal.restype = ctypes.c_int
            lib.generateSignal.argtypes = [ctypes.c_double, ctypes.c_double, ctypes.c_double]
            
            signal = lib.generateSignal(100.0, 101.0, 99.0)
            signal_types = {0: "NONE", 1: "BUY", 2: "SELL", 3: "HOLD"}
            print(f"   信号结果: {signal} ({signal_types.get(signal, 'UNKNOWN')})")
            
            # 测试多个价格点
            test_cases = [
                (100.0, 102.0, 98.0),  # 大幅波动
                (100.0, 100.5, 99.5),  # 小幅波动
                (100.0, 100.0, 100.0), # 无波动
            ]
            
            for i, (open_p, high_p, low_p) in enumerate(test_cases):
                signal = lib.generateSignal(open_p, high_p, low_p)
                print(f"   测试{i+1}: 开{open_p} 高{high_p} 低{low_p} -> {signal_types.get(signal, 'UNKNOWN')}")
        
        # 性能测试
        print("\n8. 性能测试...")
        
        # 生成大量测试数据
        np.random.seed(42)
        large_data_size = 1000
        large_prices = []
        base_price = 100.0
        
        for i in range(large_data_size):
            change = np.random.normal(0, 0.02)
            base_price *= (1 + change)
            large_prices.append(base_price)
        
        large_price_array = (ctypes.c_double * len(large_prices))(*large_prices)
        
        # 测试批量计算性能
        if 'calculateMA' in available_functions:
            start_time = time.time()
            
            ma_results = []
            for period in [5, 10, 20, 50]:
                ma_result = lib.calculateMA(large_price_array, len(large_prices), period)
                ma_results.append((period, ma_result))
            
            end_time = time.time()
            
            print(f"   批量MA计算结果:")
            for period, result in ma_results:
                print(f"     MA{period}: {result:.4f}")
            print(f"   计算用时: {(end_time - start_time)*1000:.2f}ms")
        
        print("\n=== 工作版本DLL测试通过 ===")
        return True
        
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def create_working_wrapper():
    """创建工作版本包装器"""
    print("\n=== 创建工作版本包装器 ===")
    
    wrapper_code = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
阿童木信号生成器工作版本Python包装器
"""

import os
import sys
import ctypes
import numpy as np
from typing import List, Optional, Union

class AstroboySignalGenerator:
    """阿童木信号生成器工作版本"""
    
    def __init__(self, dll_path: Optional[str] = None):
        if dll_path is None:
            # 自动查找DLL
            current_dir = os.path.dirname(os.path.abspath(__file__))
            possible_paths = [
                os.path.join(current_dir, "astroboy_working.dll"),
                os.path.join(current_dir, "simple_build", "atomboy_signal.dll"),
                os.path.join(current_dir, "atomboy_signal.dll"),
                "astroboy_working.dll",
                "atomboy_signal.dll"
            ]
            
            for path in possible_paths:
                if os.path.exists(path):
                    dll_path = path
                    break
        
        if not dll_path or not os.path.exists(dll_path):
            raise FileNotFoundError(f"找不到DLL文件: {dll_path}")
        
        self.dll_path = dll_path
        self.lib = None
        self.available_functions = []
        self._load_dll()
    
    def _load_dll(self):
        """加载DLL"""
        try:
            self.lib = ctypes.CDLL(self.dll_path)
            self._setup_function_prototypes()
            self._detect_available_functions()
        except Exception as e:
            raise RuntimeError(f"加载DLL失败: {str(e)}")
    
    def _setup_function_prototypes(self):
        """设置函数原型"""
        # 基本功能
        self.lib.getVersion.restype = ctypes.c_char_p
        self.lib.getVersion.argtypes = []
        
        self.lib.add.restype = ctypes.c_int
        self.lib.add.argtypes = [ctypes.c_int, ctypes.c_int]
        
        self.lib.printHello.restype = None
        self.lib.printHello.argtypes = []
        
        # 技术指标计算
        if hasattr(self.lib, 'calculateMA'):
            self.lib.calculateMA.restype = ctypes.c_double
            self.lib.calculateMA.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
        
        if hasattr(self.lib, 'calculateRSI'):
            self.lib.calculateRSI.restype = ctypes.c_double
            self.lib.calculateRSI.argtypes = [ctypes.POINTER(ctypes.c_double), ctypes.c_int, ctypes.c_int]
        
        if hasattr(self.lib, 'generateSignal'):
            self.lib.generateSignal.restype = ctypes.c_int
            self.lib.generateSignal.argtypes = [ctypes.c_double, ctypes.c_double, ctypes.c_double]
    
    def _detect_available_functions(self):
        """检测可用函数"""
        test_functions = [
            'calculateMA', 'calculateRSI', 'generateSignal', 
            'calculateMACD', 'calculateBollingerBands', 'calculateKDJ'
        ]
        
        for func_name in test_functions:
            if hasattr(self.lib, func_name):
                self.available_functions.append(func_name)
    
    def get_version(self) -> str:
        """获取版本信息"""
        return self.lib.getVersion().decode('utf-8')
    
    def add(self, a: int, b: int) -> int:
        """加法测试"""
        return self.lib.add(a, b)
    
    def print_hello(self):
        """打印Hello"""
        self.lib.printHello()
    
    def calculate_ma(self, prices: List[float], period: int) -> float:
        """计算移动平均线"""
        if 'calculateMA' not in self.available_functions:
            raise NotImplementedError("calculateMA函数不可用")
        
        if len(prices) < period:
            raise ValueError(f"数据长度{len(prices)}小于周期{period}")
        
        price_array = (ctypes.c_double * len(prices))(*prices)
        return self.lib.calculateMA(price_array, len(prices), period)
    
    def calculate_rsi(self, prices: List[float], period: int = 14) -> float:
        """计算RSI"""
        if 'calculateRSI' not in self.available_functions:
            raise NotImplementedError("calculateRSI函数不可用")
        
        if len(prices) < period + 1:
            raise ValueError(f"数据长度{len(prices)}不足，需要至少{period + 1}条数据")
        
        price_array = (ctypes.c_double * len(prices))(*prices)
        return self.lib.calculateRSI(price_array, len(prices), period)
    
    def generate_signal(self, open_price: float, high_price: float, low_price: float) -> int:
        """生成信号"""
        if 'generateSignal' not in self.available_functions:
            raise NotImplementedError("generateSignal函数不可用")
        
        return self.lib.generateSignal(open_price, high_price, low_price)
    
    def get_signal_name(self, signal_code: int) -> str:
        """获取信号名称"""
        signal_names = {0: "NONE", 1: "BUY", 2: "SELL", 3: "HOLD"}
        return signal_names.get(signal_code, "UNKNOWN")
    
    def get_available_functions(self) -> List[str]:
        """获取可用函数列表"""
        return self.available_functions.copy()
    
    def analyze_market(self, prices: List[float], periods: List[int] = None) -> dict:
        """市场分析"""
        if periods is None:
            periods = [5, 10, 20, 50]
        
        results = {
            'prices_count': len(prices),
            'latest_price': prices[-1] if prices else 0,
            'ma_results': {},
            'rsi': None,
            'signal': None
        }
        
        # 计算多周期MA
        if 'calculateMA' in self.available_functions:
            for period in periods:
                if len(prices) >= period:
                    ma_value = self.calculate_ma(prices, period)
                    results['ma_results'][f'MA{period}'] = ma_value
        
        # 计算RSI
        if 'calculateRSI' in self.available_functions and len(prices) >= 15:
            results['rsi'] = self.calculate_rsi(prices, 14)
        
        # 生成信号
        if 'generateSignal' in self.available_functions and len(prices) >= 3:
            # 使用最近3天的数据生成信号
            recent_prices = prices[-3:]
            open_p = recent_prices[0]
            high_p = max(recent_prices)
            low_p = min(recent_prices)
            
            signal_code = self.generate_signal(open_p, high_p, low_p)
            results['signal'] = {
                'code': signal_code,
                'name': self.get_signal_name(signal_code)
            }
        
        return results

# 兼容性别名
AtomBoySignalGenerator = AstroboySignalGenerator
'''
    
    try:
        with open("astroboy_wrapper.py", "w", encoding="utf-8") as f:
            f.write(wrapper_code)
        
        print("工作版本包装器已创建: astroboy_wrapper.py")
        return True
        
    except Exception as e:
        print(f"创建包装器失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("阿童木信号生成器工作版本DLL测试")
    print("=" * 50)
    
    # 切换到脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"工作目录: {os.getcwd()}")
    
    # 运行测试
    success = test_working_dll()
    
    if success:
        create_working_wrapper()
        print("\n=== 全部测试通过 ===")
        print("阿童木信号生成器已准备就绪!")
        print("\n使用方法:")
        print("from astroboy_wrapper import AstroboySignalGenerator")
        print("generator = AstroboySignalGenerator()")
        print("print(generator.get_version())")
    else:
        print("\n=== 测试失败 ===")
    
    print("\n测试完成")
