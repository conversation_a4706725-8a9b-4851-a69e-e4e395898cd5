#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
阿童木V100信号生成器VNPY集成模块
用于在VNPY策略中使用阿童木信号生成器
"""

import os
import sys
from pathlib import Path
from typing import List, Dict, Any, Optional

# 添加阿童木模块路径
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

try:
    from astroboy_v100_wrapper import AstroboyV100SignalGenerator
    ASTROBOY_AVAILABLE = True
except ImportError:
    try:
        from astroboy_wrapper_complete import AstroboySignalGenerator as AstroboyV100SignalGenerator
        ASTROBOY_AVAILABLE = True
    except ImportError:
        ASTROBOY_AVAILABLE = False
        print("警告: 阿童木信号生成器不可用")

class VNPYAstroboyAdapter:
    """VNPY阿童木适配器"""

    def __init__(self, enable_gpu: bool = True):
        if not ASTROBOY_AVAILABLE:
            raise RuntimeError("阿童木信号生成器不可用，请检查DLL文件")

        self.generator = AstroboyV100SignalGenerator(enable_gpu=enable_gpu)
        self.signal_history = []
        self.analysis_cache = {}

        print(f"阿童木信号生成器初始化成功")
        print(f"版本: {self.generator.get_version()}")
        print(f"GPU信息: {self.generator.get_gpu_info()}")

    def analyze_bars(self, bars: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析K线数据"""
        if not bars:
            return {}

        # 提取价格数据
        prices = [float(bar.get('close', 0)) for bar in bars]

        if len(prices) < 5:
            return {'error': '数据不足，需要至少5根K线'}

        try:
            # 使用V100加速分析
            analysis = self.generator.analyze_market_v100(prices)

            # 添加VNPY特定信息
            analysis['vnpy_compatible'] = True
            analysis['bar_count'] = len(bars)
            analysis['latest_bar'] = bars[-1] if bars else None

            # 缓存结果
            cache_key = f"{len(bars)}_{prices[-1]}"
            self.analysis_cache[cache_key] = analysis

            return analysis

        except Exception as e:
            return {'error': f'分析失败: {str(e)}'}

    def generate_trading_signal(self, bars: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成交易信号"""
        if len(bars) < 3:
            return {'signal': 'NONE', 'reason': '数据不足'}

        try:
            # 获取最近3根K线
            recent_bars = bars[-3:]

            open_price = float(recent_bars[0].get('close', 0))
            high_price = max(float(bar.get('high', 0)) for bar in recent_bars)
            low_price = min(float(bar.get('low', 0)) for bar in recent_bars)

            # 生成信号
            signal_code = self.generator.generate_signal(open_price, high_price, low_price)
            signal_name = self.generator.get_signal_name(signal_code)

            # 计算技术指标支持
            prices = [float(bar.get('close', 0)) for bar in bars]

            support_info = {}
            if len(prices) >= 20:
                try:
                    ma20 = self.generator.calculate_ma(prices, 20)
                    current_price = prices[-1]
                    support_info['ma20_position'] = 'above' if current_price > ma20 else 'below'
                    support_info['ma20_value'] = ma20
                except:
                    pass

            if len(prices) >= 15:
                try:
                    rsi = self.generator.calculate_rsi(prices, 14)
                    support_info['rsi'] = rsi
                    support_info['rsi_level'] = 'overbought' if rsi > 70 else ('oversold' if rsi < 30 else 'neutral')
                except:
                    pass

            signal_result = {
                'signal': signal_name,
                'signal_code': signal_code,
                'confidence': self._calculate_confidence(signal_code, support_info),
                'support_info': support_info,
                'input_data': {
                    'open': open_price,
                    'high': high_price,
                    'low': low_price
                },
                'timestamp': bars[-1].get('datetime', None) if bars else None
            }

            # 记录信号历史
            self.signal_history.append(signal_result)
            if len(self.signal_history) > 100:  # 保持最近100个信号
                self.signal_history.pop(0)

            return signal_result

        except Exception as e:
            return {'signal': 'NONE', 'error': f'信号生成失败: {str(e)}'}

    def _calculate_confidence(self, signal_code: int, support_info: Dict[str, Any]) -> float:
        """计算信号置信度"""
        if signal_code == 0:  # NONE
            return 0.0

        confidence = 0.5  # 基础置信度

        # RSI支持
        if 'rsi_level' in support_info:
            if signal_code == 1 and support_info['rsi_level'] == 'oversold':  # BUY + 超卖
                confidence += 0.3
            elif signal_code == 2 and support_info['rsi_level'] == 'overbought':  # SELL + 超买
                confidence += 0.3
            elif support_info['rsi_level'] == 'neutral':
                confidence += 0.1

        # MA20支持
        if 'ma20_position' in support_info:
            if signal_code == 1 and support_info['ma20_position'] == 'below':  # BUY + 价格低于MA20
                confidence += 0.2
            elif signal_code == 2 and support_info['ma20_position'] == 'above':  # SELL + 价格高于MA20
                confidence += 0.2

        return min(confidence, 1.0)

    def get_signal_history(self, count: int = 10) -> List[Dict[str, Any]]:
        """获取信号历史"""
        return self.signal_history[-count:] if self.signal_history else []

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        if hasattr(self.generator, 'get_performance_stats'):
            return self.generator.get_performance_stats()
        else:
            return {'message': '性能统计不可用'}

    def benchmark_performance(self, iterations: int = 50) -> Dict[str, Any]:
        """性能基准测试"""
        if hasattr(self.generator, 'benchmark_performance'):
            return self.generator.benchmark_performance(iterations)
        else:
            return {'message': '性能基准测试不可用'}

# VNPY策略辅助函数
def create_astroboy_adapter(enable_gpu: bool = True) -> Optional[VNPYAstroboyAdapter]:
    """创建阿童木适配器"""
    try:
        return VNPYAstroboyAdapter(enable_gpu)
    except Exception as e:
        print(f"创建阿童木适配器失败: {str(e)}")
        return None

def quick_signal_analysis(bars: List[Dict[str, Any]], enable_gpu: bool = True) -> Dict[str, Any]:
    """快速信号分析"""
    adapter = create_astroboy_adapter(enable_gpu)
    if adapter:
        return adapter.generate_trading_signal(bars)
    else:
        return {'signal': 'NONE', 'error': '适配器创建失败'}
