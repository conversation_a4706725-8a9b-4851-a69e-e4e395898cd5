# VNPY 真实机器学习策略使用指南

## 🎯 概述

真实机器学习策略 (`RealMLStrategy`) 是一个使用真实市场数据训练机器学习模型的高级交易策略。它支持：

- ✅ **真实数据训练**: 使用历史K线数据训练模型
- ✅ **在线学习**: 实时学习市场变化，持续优化模型
- ✅ **多种算法**: 支持LightGBM、随机森林等算法
- ✅ **自动特征工程**: 自动提取20个技术分析特征
- ✅ **智能风险管理**: 止损止盈、仓位控制
- ✅ **性能监控**: 实时监控模型准确率和交易表现

## 📁 文件结构

```
vnpy_ctastrategy/strategies/
├── real_ml_strategy.py              # 🚀 真实ML策略 (主策略)
├── signal_system/
│   ├── real_ml_system.py            # 真实ML信号系统
│   └── models/                      # 模型保存目录 (自动创建)
│       └── [symbol]_[model]_model.pkl
├── test_real_ml.py                  # 测试脚本
└── REAL_ML_GUIDE.md                 # 本使用指南
```

## 🚀 快速开始

### 1. 环境准备

确保已安装必要的机器学习库：

```bash
pip install lightgbm scikit-learn
```

### 2. 在VNPY中使用

1. **打开VeighNa Station**
2. **启动VeighNa Trader**
3. **连接数据源和交易接口**
4. **打开CTA策略模块**
5. **添加策略**:
   - **策略类名**: `RealMLStrategy`
   - **本地代码**: 如 `rb2501.SHFE`
   - **设置参数** (见下方参数说明)
6. **初始化策略** (会自动加载历史数据训练模型)
7. **启动策略**

## 🔧 参数配置

### 核心参数

| 参数名 | 默认值 | 说明 |
|--------|--------|------|
| `signal_threshold` | 0.65 | 信号置信度阈值 (0.5-0.9) |
| `position_size` | 1 | 基础仓位大小 (手) |
| `stop_loss_pct` | 2.0 | 止损百分比 (%) |
| `take_profit_pct` | 4.0 | 止盈百分比 (%) |
| `max_position` | 3 | 最大持仓手数 |

### ML参数

| 参数名 | 默认值 | 说明 |
|--------|--------|------|
| `model_type` | "lightgbm" | 模型类型 ("lightgbm", "random_forest") |
| `training_days` | 30 | 训练数据天数 |
| `retrain_interval` | 100 | 重训练间隔 (样本数) |
| `online_learning` | True | 是否启用在线学习 |

### 风险控制参数

| 参数名 | 默认值 | 说明 |
|--------|--------|------|
| `daily_max_trades` | 10 | 日最大交易次数 |
| `max_loss_pct` | 5.0 | 最大亏损百分比 (%) |

## 📊 特征工程

策略自动提取20个技术分析特征：

### 价格特征 (3个)
- 当前价格变化率
- 5周期价格变化率  
- 10周期价格变化率

### 移动平均特征 (2个)
- 相对SMA5偏离度
- 相对SMA20偏离度

### 技术指标特征 (6个)
- 归一化RSI
- MACD柱状图
- 布林带位置
- 相对ATR
- 成交量比率
- 波动率

### 动量特征 (2个)
- 5周期动量
- 10周期动量

### 高级特征 (7个)
- 趋势强度
- 支撑阻力位置
- K线形态
- 跳空比率
- 高低位比率
- 收盘价位置
- 成交量趋势

## 🧠 机器学习流程

### 1. 数据收集
- 策略启动时自动收集历史K线数据
- 实时收集新的K线数据

### 2. 特征提取
- 从K线数据自动计算技术指标
- 提取20维特征向量

### 3. 标签生成
- 基于未来5个周期的价格变化生成标签
- 盈利阈值: +0.3%, 亏损阈值: -0.2%
- 标签: 1=买入, -1=卖出, 0=持有

### 4. 模型训练
- 使用历史数据训练初始模型
- 支持LightGBM和随机森林算法
- 自动评估模型准确率

### 5. 在线学习
- 实时收集交易结果
- 定期重训练模型
- 持续优化预测性能

## 📈 交易逻辑

### 信号生成
1. 提取当前市场特征
2. 使用训练好的模型预测
3. 获得信号值 (-1, 0, 1) 和置信度

### 开仓条件
- 信号置信度 >= 阈值
- 未达到日交易次数限制
- 未达到最大亏损限制
- 当前无持仓

### 平仓条件
- 信号反转
- 触发止损 (亏损 >= 止损百分比)
- 触发止盈 (盈利 >= 止盈百分比)

## 📊 性能监控

策略提供实时性能监控：

### 模型指标
- `model_accuracy`: 模型准确率
- `training_samples`: 训练样本数
- `prediction_count`: 预测次数

### 交易指标
- `daily_trades`: 日交易次数
- `daily_pnl`: 日盈亏
- 胜率、平均盈亏等

## 🎯 推荐配置

### 新手配置 (保守)
```python
signal_threshold = 0.75      # 较高阈值
position_size = 1            # 小仓位
stop_loss_pct = 1.5          # 较小止损
take_profit_pct = 3.0        # 较小止盈
model_type = "random_forest" # 稳定算法
online_learning = False      # 关闭在线学习
```

### 标准配置 (推荐)
```python
signal_threshold = 0.65      # 默认阈值
position_size = 1            # 标准仓位
stop_loss_pct = 2.0          # 标准止损
take_profit_pct = 4.0        # 标准止盈
model_type = "lightgbm"      # 高性能算法
online_learning = True       # 启用在线学习
```

### 激进配置 (高级)
```python
signal_threshold = 0.55      # 较低阈值
position_size = 2            # 较大仓位
stop_loss_pct = 2.5          # 较大止损
take_profit_pct = 5.0        # 较大止盈
model_type = "lightgbm"      # 高性能算法
online_learning = True       # 启用在线学习
retrain_interval = 50        # 更频繁重训练
```

## ⚠️ 注意事项

### 1. 数据要求
- 至少需要30天历史数据进行初始训练
- 建议使用1分钟K线数据
- 确保数据质量和连续性

### 2. 计算资源
- LightGBM模型训练需要一定计算资源
- 在线学习会持续消耗CPU
- 建议在性能较好的机器上运行

### 3. 风险控制
- 首次使用建议在模拟环境测试
- 设置合理的止损止盈参数
- 监控模型准确率变化

### 4. 模型维护
- 定期检查模型性能
- 市场环境变化时考虑重新训练
- 保存重要的模型版本

## 🔧 故障排除

### 1. 模型训练失败
- 检查历史数据是否充足 (>100根K线)
- 确认机器学习库已正确安装
- 查看策略日志中的错误信息

### 2. 预测异常
- 检查特征提取是否正常
- 确认模型文件是否损坏
- 重新训练模型

### 3. 性能下降
- 检查市场环境是否发生重大变化
- 考虑调整模型参数
- 增加训练数据量

## 📞 技术支持

如果遇到问题：

1. **运行测试脚本**: `python test_real_ml.py`
2. **查看策略日志**: 检查VNPY日志输出
3. **检查模型文件**: 确认模型是否正确保存
4. **验证环境**: 确认所有依赖库已安装

---

**现在您可以使用真实数据训练的机器学习策略进行交易了！** 🚀

记住：机器学习策略需要充足的历史数据和持续的优化，建议先在回测环境中验证效果。
