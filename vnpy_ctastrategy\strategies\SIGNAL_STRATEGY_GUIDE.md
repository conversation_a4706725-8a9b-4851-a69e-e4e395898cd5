# VNPY 4.1.0 信号生成器策略使用指南

## 文件结构说明

按照您的要求，现在文件已经重新组织为标准的VNPY策略结构：

```
vnpy_ctastrategy/strategies/
├── signal_strategy_safe.py         # ⭐ 安全版信号策略 (推荐)
├── enhanced_signal_strategy_safe.py # 🚀 安全版增强策略 (高级用户)
├── signal_strategy.py              # 基础版信号策略
├── enhanced_signal_strategy.py     # 增强版信号策略 (可能有日志问题)
├── signal_system/                  # 信号系统支持文件夹
│   ├── __init__.py                 # Python包初始化文件
│   └── signal_api.py               # 信号系统API
└── SIGNAL_STRATEGY_GUIDE.md        # 本使用指南
```

## 策略说明

### 1. SignalStrategySafe (安全版) ⭐ 推荐使用

**文件**: `signal_strategy_safe.py`
**类名**: `SignalStrategySafe`
**作者**: Signal System Safe v1.0

**特点**:
- 使用信号生成器作为唯一信号源
- 避免日志格式化问题
- 稳定的交易逻辑
- 完善的风险控制
- 易于理解和配置

### 2. SignalStrategy (基础版)

**文件**: `signal_strategy.py`
**类名**: `SignalStrategy`
**作者**: Signal System v1.0

**特点**:
- 使用信号生成器作为唯一信号源
- 简洁稳定的交易逻辑
- 完善的风险控制
- 易于理解和配置

**参数**:
- `signal_threshold` (0.65): 信号置信度阈值
- `position_size` (1): 基础仓位大小
- `stop_loss_pct` (2.0): 止损百分比
- `take_profit_pct` (4.0): 止盈百分比
- `max_position` (3): 最大持仓手数
- `daily_max_trades` (10): 日最大交易次数
- `max_loss_pct` (5.0): 最大亏损百分比

### 3. EnhancedSignalStrategySafe (安全版增强) 🚀 高级用户推荐

**文件**: `enhanced_signal_strategy_safe.py`
**类名**: `EnhancedSignalStrategySafe`
**作者**: Enhanced Signal System Safe v2.0

**特点**:
- ✅ 避免日志格式化问题
- ✅ 集成VNPY 4.1.0的Alpha模块
- ✅ 支持Alpha158特征工程
- ✅ 智能风险管理
- ✅ 自适应参数调整
- ✅ 追踪止损功能

**参数** (14个):
- `signal_threshold` (0.65): 信号阈值
- `position_ratio` (0.2): 仓位比例
- `stop_loss_ratio` (0.015): 止损比例
- `take_profit_ratio` (0.04): 止盈比例
- `trailing_stop` (True): 追踪止损
- `trailing_ratio` (0.6): 追踪止损比例
- `use_alpha_features` (True): 使用Alpha158特征
- `feature_window` (30): 特征计算窗口
- `min_confidence` (0.6): 最小置信度
- `max_position_ratio` (0.5): 最大仓位比例
- `daily_loss_limit` (0.02): 日损失限制
- `max_drawdown` (0.05): 最大回撤
- `adaptive_threshold` (True): 自适应阈值
- `performance_window` (20): 性能评估窗口

### 4. EnhancedSignalStrategy (增强版) ⚠️ 可能有日志问题

**文件**: `enhanced_signal_strategy.py`
**类名**: `EnhancedSignalStrategy`
**作者**: Enhanced Signal System v2.0

**注意**: 此版本可能遇到日志格式化错误，建议使用安全版本。

## 在VNPY中使用策略

### 方法一：通过VeighNa Station (推荐)

1. **启动VeighNa Station**
2. **打开VeighNa Trader**
3. **连接交易接口** (如CTP、IB等)
4. **打开CTA策略模块**
5. **添加策略**:
   - 策略类名:
     - `SignalStrategySafe` (推荐，适合新手)
     - `EnhancedSignalStrategySafe` (高级功能，适合有经验用户)
   - 本地代码: 如 `rb2501.SHFE` (螺纹钢主力合约)
   - 设置参数
6. **启动策略**

### 方法二：通过代码启动

```python
from vnpy.event import EventEngine
from vnpy.trader.engine import MainEngine
from vnpy.trader.ui import MainWindow, create_qapp

from vnpy_ctp import CtpGateway
from vnpy_ctastrategy import CtaStrategyApp

# 导入策略
from vnpy_ctastrategy.strategies.signal_strategy_safe import SignalStrategySafe

def main():
    qapp = create_qapp()
    
    event_engine = EventEngine()
    main_engine = MainEngine(event_engine)
    
    main_engine.add_gateway(CtpGateway)
    main_engine.add_app(CtaStrategyApp)
    
    main_window = MainWindow(main_engine, event_engine)
    main_window.showMaximized()
    
    qapp.exec()

if __name__ == "__main__":
    main()
```

## 参数配置建议

### 保守型配置 (适合新手)
```
signal_threshold = 0.75      # 较高阈值，减少交易频率
position_size = 1            # 小仓位
stop_loss_pct = 1.5          # 较小止损
take_profit_pct = 3.0        # 较小止盈
daily_max_trades = 5         # 限制交易次数
```

### 平衡型配置 (推荐)
```
signal_threshold = 0.65      # 默认阈值
position_size = 1            # 标准仓位
stop_loss_pct = 2.0          # 标准止损
take_profit_pct = 4.0        # 标准止盈
daily_max_trades = 10        # 适中交易次数
```

### 激进型配置 (适合有经验用户)
```
signal_threshold = 0.55      # 较低阈值，增加交易频率
position_size = 2            # 较大仓位
stop_loss_pct = 2.5          # 较大止损
take_profit_pct = 5.0        # 较大止盈
daily_max_trades = 15        # 更多交易机会
```

## 信号系统说明

### 信号生成原理
1. **特征提取**: 从K线数据中提取10个关键特征
   - 价格特征: 涨跌幅、振幅、相对均线位置
   - 动量特征: 短期和中期价格动量
   - 技术指标: RSI、ATR、MACD
   - 成交量特征: 相对成交量变化

2. **信号生成**: 基于深度学习模型或模拟算法
   - 输出信号值: 1=多头, -1=空头, 0=中性
   - 输出置信度: 0.0-1.0

3. **交易决策**: 根据信号和置信度执行交易
   - 只有置信度 >= 阈值时才交易
   - 支持信号反转平仓

### 风险控制机制
1. **止损止盈**: 固定百分比止损止盈
2. **仓位控制**: 最大持仓限制
3. **交易频率**: 日交易次数限制
4. **亏损控制**: 日亏损限制

## 监控和调试

### 策略日志
策略运行时会输出详细日志：
```
[SignalStrategy] 信号: 多头信号(置信度:0.756)
[SignalStrategy] 开多仓: 3850.0, 信号: 多头信号(置信度:0.756)
[SignalStrategy] 交易执行: 买 1手 @3850.00
```

### 性能统计
策略停止时会输出统计信息：
```
交易统计: 总交易15次, 胜率66.67%, 平均盈亏0.12
```

## 常见问题解决

### 1. 策略无法启动
- 检查策略文件是否在正确位置
- 确认Python路径设置
- 查看VNPY日志错误信息

### 2. 无信号生成
- 检查信号阈值设置是否过高
- 确认市场数据正常接收
- 查看策略日志中的特征提取信息

### 3. 频繁交易
- 提高信号阈值 (如从0.65调到0.75)
- 减少日交易次数限制
- 增加止损止盈幅度

### 4. 策略不交易
- 降低信号阈值 (如从0.75调到0.65)
- 检查仓位限制设置
- 确认风险控制参数

## 版本更新说明

### v1.0 (2025-07-11)
- ✅ 重新组织文件结构，符合VNPY标准
- ✅ 策略文件放在 `strategies/` 文件夹
- ✅ 支持文件放在 `strategies/signal_system/` 文件夹
- ✅ 完全兼容VNPY 4.1.0
- ✅ 通过所有功能测试
- ✅ 提供基础版和增强版两个策略

## 技术支持

如遇到问题：
1. 查看策略运行日志
2. 运行测试脚本: `python test_new_structure.py`
3. 检查VNPY版本兼容性
4. 参考VNPY官方文档

---

**免责声明**: 本策略仅供学习和研究使用，实盘交易存在风险，请谨慎使用。
