#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查策略状态和模型文件
"""

import os
import sys
from datetime import datetime

def check_strategy_files():
    """检查策略文件"""
    print("=" * 60)
    print("检查策略文件")
    print("=" * 60)
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 检查策略文件
    strategy_files = [
        ("real_ml_strategy.py", "真实ML策略"),
        ("signal_system/real_ml_system.py", "真实ML信号系统"),
        ("signal_system/__init__.py", "信号系统包初始化"),
        ("test_real_ml.py", "ML系统测试脚本"),
        ("test_commodity_extraction.py", "商品代码测试脚本"),
        ("REAL_ML_GUIDE.md", "使用指南")
    ]
    
    for file_path, description in strategy_files:
        full_path = os.path.join(current_dir, file_path)
        if os.path.exists(full_path):
            size = os.path.getsize(full_path)
            print(f"✓ {description}: {file_path} ({size} bytes)")
        else:
            print(f"✗ {description}: {file_path} (不存在)")
    
    return True

def check_model_directory():
    """检查模型目录"""
    print("\n" + "=" * 60)
    print("检查模型目录")
    print("=" * 60)
    
    current_dir = os.path.dirname(os.path.abspath(__file__))
    model_dir = os.path.join(current_dir, "signal_system", "models")
    
    if os.path.exists(model_dir):
        print(f"✓ 模型目录存在: {model_dir}")
        
        # 列出现有模型文件
        model_files = [f for f in os.listdir(model_dir) if f.endswith('.pkl')]
        if model_files:
            print("现有模型文件:")
            for model_file in model_files:
                model_path = os.path.join(model_dir, model_file)
                size = os.path.getsize(model_path)
                mtime = datetime.fromtimestamp(os.path.getmtime(model_path))
                print(f"  - {model_file} ({size} bytes, 修改时间: {mtime.strftime('%Y-%m-%d %H:%M:%S')})")
        else:
            print("  (暂无模型文件，将在策略运行时自动创建)")
    else:
        print(f"⚠️  模型目录不存在，将自动创建: {model_dir}")
        try:
            os.makedirs(model_dir, exist_ok=True)
            print(f"✓ 模型目录创建成功")
        except Exception as e:
            print(f"✗ 模型目录创建失败: {e}")
    
    return True

def check_dependencies():
    """检查依赖库"""
    print("\n" + "=" * 60)
    print("检查依赖库")
    print("=" * 60)
    
    dependencies = [
        ("numpy", "NumPy"),
        ("pandas", "Pandas"),
        ("lightgbm", "LightGBM"),
        ("sklearn", "Scikit-learn"),
        ("vnpy_ctastrategy", "VNPY CTA策略")
    ]
    
    available_count = 0
    
    for module_name, display_name in dependencies:
        try:
            module = __import__(module_name)
            version = getattr(module, '__version__', 'Unknown')
            print(f"✓ {display_name}: v{version}")
            available_count += 1
        except ImportError:
            print(f"✗ {display_name}: 未安装")
    
    print(f"\n依赖库状态: {available_count}/{len(dependencies)} 可用")
    
    if available_count == len(dependencies):
        print("🎉 所有依赖库都已安装!")
    else:
        print("⚠️  部分依赖库缺失，可能影响策略运行")
    
    return available_count == len(dependencies)

def check_strategy_import():
    """检查策略导入"""
    print("\n" + "=" * 60)
    print("检查策略导入")
    print("=" * 60)
    
    try:
        from real_ml_strategy import RealMLStrategy
        print("✓ RealMLStrategy 导入成功")
        print(f"  作者: {RealMLStrategy.author}")
        print(f"  参数数量: {len(RealMLStrategy.parameters)}")
        print(f"  变量数量: {len(RealMLStrategy.variables)}")
        
        # 测试商品代码提取
        strategy = RealMLStrategy.__new__(RealMLStrategy)
        test_symbol = "rb2510.SHFE"
        commodity_code = strategy._extract_commodity_code(test_symbol)
        print(f"  商品代码提取测试: {test_symbol} -> {commodity_code}")
        
        return True
        
    except Exception as e:
        print(f"✗ RealMLStrategy 导入失败: {e}")
        return False

def simulate_model_creation():
    """模拟模型创建过程"""
    print("\n" + "=" * 60)
    print("模拟模型创建过程")
    print("=" * 60)
    
    # 模拟不同合约的模型路径
    test_contracts = [
        "rb2510.SHFE",
        "rb2501.SHFE", 
        "cu2501.SHFE",
        "IF2501.CFFEX",
        "m2501.DCE",
        "MA2501.CZCE"
    ]
    
    try:
        from real_ml_strategy import RealMLStrategy
        
        current_dir = os.path.dirname(os.path.abspath(__file__))
        model_dir = os.path.join(current_dir, "signal_system", "models")
        
        print("模拟合约 -> 模型文件映射:")
        print("-" * 40)
        
        for contract in test_contracts:
            strategy = RealMLStrategy.__new__(RealMLStrategy)
            strategy.model_type = "lightgbm"
            
            commodity_code = strategy._extract_commodity_code(contract)
            model_file = f"{commodity_code}_lightgbm_model.pkl"
            model_path = os.path.join(model_dir, model_file)
            
            print(f"{contract:15} -> {model_file}")
        
        print("-" * 40)
        print("说明:")
        print("- 同一商品的不同合约共享模型文件")
        print("- rb2510 和 rb2501 都使用 rb_lightgbm_model.pkl")
        print("- 模型会在策略首次运行时自动创建")
        
        return True
        
    except Exception as e:
        print(f"✗ 模拟失败: {e}")
        return False

def check_vnpy_integration():
    """检查VNPY集成"""
    print("\n" + "=" * 60)
    print("检查VNPY集成")
    print("=" * 60)
    
    try:
        # 检查VNPY组件
        from vnpy_ctastrategy import CtaTemplate, BarGenerator, ArrayManager
        print("✓ VNPY CTA组件导入成功")
        
        # 检查策略继承
        from real_ml_strategy import RealMLStrategy
        if issubclass(RealMLStrategy, CtaTemplate):
            print("✓ RealMLStrategy 正确继承 CtaTemplate")
        else:
            print("✗ RealMLStrategy 继承关系错误")
            return False
        
        # 检查必要方法
        required_methods = ['on_init', 'on_start', 'on_stop', 'on_tick', 'on_bar', 'on_trade']
        for method in required_methods:
            if hasattr(RealMLStrategy, method):
                print(f"✓ 找到方法: {method}")
            else:
                print(f"✗ 缺少方法: {method}")
                return False
        
        print("✓ VNPY集成检查完成")
        return True
        
    except Exception as e:
        print(f"✗ VNPY集成检查失败: {e}")
        return False

def run_status_check():
    """运行状态检查"""
    print("VNPY 真实ML策略状态检查")
    print("=" * 70)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行检查
    checks = [
        ("策略文件", check_strategy_files),
        ("模型目录", check_model_directory),
        ("依赖库", check_dependencies),
        ("策略导入", check_strategy_import),
        ("模型创建", simulate_model_creation),
        ("VNPY集成", check_vnpy_integration)
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"✗ {check_name} 检查异常: {e}")
            results.append((check_name, False))
    
    # 输出总结
    print("\n" + "=" * 70)
    print("状态检查总结")
    print("=" * 70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for check_name, result in results:
        status = "✓ 正常" if result else "✗ 异常"
        print(f"{status} {check_name}")
    
    print(f"\n总体状态: {passed}/{total} 检查通过")
    
    if passed == total:
        print("🎉 所有检查通过！策略可以正常使用。")
        print("\n使用步骤:")
        print("1. 在VNPY中添加策略: RealMLStrategy")
        print("2. 设置合约代码，如: rb2510.SHFE")
        print("3. 策略会自动创建对应的模型: rb_lightgbm_model.pkl")
        print("4. 首次运行会使用历史数据训练模型")
        print("5. 后续运行会加载已有模型并进行在线学习")
    elif passed >= total * 0.8:
        print("⚠️  大部分检查通过，基本可用。")
        print("请关注失败的检查项目。")
    else:
        print("❌ 多项检查失败，请修复问题后再使用。")
    
    return passed, total

if __name__ == "__main__":
    try:
        passed, total = run_status_check()
        
        if passed == total:
            sys.exit(0)
        elif passed >= total * 0.8:
            sys.exit(1)
        else:
            sys.exit(2)
            
    except KeyboardInterrupt:
        print("\n检查被用户中断")
        sys.exit(3)
    except Exception as e:
        print(f"\n检查异常: {e}")
        sys.exit(4)
