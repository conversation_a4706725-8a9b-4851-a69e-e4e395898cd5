#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强版信号生成器策略 - 集成VNPY 4.1.0 Alpha模块
使用信号生成器作为唯一信号源，支持更多高级功能
"""

import os
import sys
import numpy as np
from datetime import datetime
from typing import List, Tuple, Dict, Any

# 导入VNPY组件
from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    ArrayManager
)

# 尝试导入VNPY Alpha模块
try:
    from vnpy.alpha.dataset import Alpha158Dataset
    from vnpy.alpha.lab import AlphaLab
    VNPY_ALPHA_AVAILABLE = True
except ImportError:
    VNPY_ALPHA_AVAILABLE = False

# 导入信号系统API
try:
    from .signal_system import SignalSystemAPI
    SIGNAL_API_AVAILABLE = True
except ImportError:
    try:
        from signal_system import SignalSystemAPI
        SIGNAL_API_AVAILABLE = True
    except ImportError:
        SIGNAL_API_AVAILABLE = False


class EnhancedSignalStrategy(CtaTemplate):
    """
    增强版信号生成器策略
    
    特点：
    1. 集成VNPY 4.1.0的Alpha模块
    2. 使用深度学习信号生成器
    3. 支持Alpha158特征工程
    4. 智能风险管理
    5. 自适应参数调整
    """
    
    author = "Enhanced Signal System v2.0"
    
    # 策略参数
    signal_threshold = 0.65      # 信号阈值
    position_ratio = 0.2         # 仓位比例
    stop_loss_ratio = 0.015      # 止损比例
    take_profit_ratio = 0.04     # 止盈比例
    trailing_stop = True         # 追踪止损
    trailing_ratio = 0.6         # 追踪止损比例
    
    # Alpha模块参数
    use_alpha_features = True    # 使用Alpha158特征
    feature_window = 30          # 特征计算窗口
    min_confidence = 0.6         # 最小置信度
    
    # 风险管理参数
    max_position_ratio = 0.5     # 最大仓位比例
    daily_loss_limit = 0.02      # 日损失限制
    max_drawdown = 0.05          # 最大回撤
    
    # 自适应参数
    adaptive_threshold = True    # 自适应阈值
    performance_window = 20      # 性能评估窗口
    
    # 变量
    signal_value = 0
    signal_confidence = 0.0
    entry_price = 0.0
    high_since_entry = 0.0
    low_since_entry = 0.0
    daily_pnl = 0.0
    max_drawdown_value = 0.0
    current_threshold = 0.65
    
    # 参数列表
    parameters = [
        "signal_threshold", "position_ratio", "stop_loss_ratio", "take_profit_ratio",
        "trailing_stop", "trailing_ratio", "use_alpha_features", "feature_window",
        "min_confidence", "max_position_ratio", "daily_loss_limit", "max_drawdown",
        "adaptive_threshold", "performance_window"
    ]
    
    # 变量列表
    variables = [
        "signal_value", "signal_confidence", "entry_price", "high_since_entry",
        "low_since_entry", "daily_pnl", "max_drawdown_value", "current_threshold"
    ]
    
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        """初始化策略"""
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        
        # 创建K线生成器和技术指标管理器
        self.bg = BarGenerator(self.on_bar, 1, self.on_1min_bar)
        self.am = ArrayManager(size=200)  # 增大缓存以支持更多特征计算
        
        # 初始化信号系统
        self.signal_api = None
        self.alpha_lab = None
        self.alpha_dataset = None
        
        if SIGNAL_API_AVAILABLE:
            try:
                # 查找模型文件
                model_path = self._find_model_file()
                
                # 初始化信号API
                self.signal_api = SignalSystemAPI(
                    model_path=model_path,
                    use_mock=False  # 尝试使用真实系统
                )
                
                if self.signal_api.is_initialized():
                    self.write_log("信号系统初始化成功")
                    
                    # 获取系统信息
                    info = self.signal_api.get_system_info()
                    self.write_log(f"系统信息: {info}")
                else:
                    self.write_log("信号系统初始化失败")
                    
            except Exception as e:
                self.write_log(f"初始化信号系统异常: {e}")
                self.signal_api = None
        else:
            self.write_log("信号API不可用")
        
        # 初始化Alpha模块
        if VNPY_ALPHA_AVAILABLE and self.use_alpha_features:
            try:
                self.alpha_lab = AlphaLab()
                self.alpha_dataset = Alpha158Dataset()
                self.write_log("VNPY Alpha模块初始化成功")
            except Exception as e:
                self.write_log(f"初始化Alpha模块失败: {e}")
        
        # 初始化数据缓存
        self.price_data = {
            'open': [],
            'high': [],
            'low': [],
            'close': [],
            'volume': []
        }
        
        # 性能跟踪
        self.trade_results = []
        self.daily_start_capital = 0
        self.last_trade_time = None
        
        # 风险控制
        self.daily_trades = 0
        self.max_daily_trades = 10
        
        self.write_log("增强版信号策略初始化完成")
    
    def _find_model_file(self) -> str:
        """查找模型文件"""
        # 获取signal_system文件夹路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        signal_system_dir = os.path.join(current_dir, "signal_system")
        
        # 查找模型文件
        model_files = [
            "model.bin", "model.lgb", "model.pt", "best_model.pt"
        ]
        
        for filename in model_files:
            model_path = os.path.join(signal_system_dir, filename)
            if os.path.exists(model_path):
                self.write_log(f"找到模型文件: {model_path}")
                return model_path
        
        self.write_log("未找到模型文件，将使用默认配置")
        return ""
    
    def on_init(self):
        """策略初始化回调"""
        self.write_log("策略初始化")
        self.load_bar(30)  # 加载30天历史数据
    
    def on_start(self):
        """策略启动回调"""
        self.write_log("策略启动")
        self.daily_start_capital = self.get_pos() * 100000  # 假设每手10万
    
    def on_stop(self):
        """策略停止回调"""
        self.write_log("策略停止")
        
        # 输出性能统计
        if self.trade_results:
            win_rate = sum(1 for r in self.trade_results if r > 0) / len(self.trade_results)
            avg_return = np.mean(self.trade_results)
            self.write_log(f"交易统计 - 胜率: {win_rate:.2%}, 平均收益: {avg_return:.4f}")
    
    def on_tick(self, tick: TickData):
        """Tick数据回调"""
        self.bg.update_tick(tick)
    
    def on_1min_bar(self, bar: BarData):
        """1分钟K线回调"""
        # 更新技术指标
        self.am.update_bar(bar)
        if not self.am.inited:
            return
        
        # 更新价格数据缓存
        self._update_price_data(bar)
        
        # 提取特征并获取信号
        features = self._extract_enhanced_features(bar)
        if features:
            self.signal_value, self.signal_confidence = self._get_signal(features)
            
            # 自适应阈值调整
            if self.adaptive_threshold:
                self._adjust_threshold()
            
            # 执行交易逻辑
            self._execute_trading_logic(bar)
        
        # 风险管理
        self._risk_management(bar)
        
        # 更新策略状态
        self.put_event()
    
    def on_bar(self, bar: BarData):
        """K线数据回调（由bg生成的1分钟K线）"""
        pass  # 实际处理在on_1min_bar中
    
    def _update_price_data(self, bar: BarData):
        """更新价格数据缓存"""
        self.price_data['open'].append(bar.open_price)
        self.price_data['high'].append(bar.high_price)
        self.price_data['low'].append(bar.low_price)
        self.price_data['close'].append(bar.close_price)
        self.price_data['volume'].append(bar.volume)
        
        # 保持固定窗口大小
        max_size = max(self.feature_window * 2, 100)
        for key in self.price_data:
            if len(self.price_data[key]) > max_size:
                self.price_data[key].pop(0)
    
    def _extract_enhanced_features(self, bar: BarData) -> List[float]:
        """提取增强特征"""
        try:
            features = []
            
            # 基础技术指标特征
            if self.am.inited:
                # 价格特征
                close = bar.close_price
                features.extend([
                    (close / self.am.open_array[-1] - 1),  # 当前K线涨跌幅
                    (bar.high_price / bar.low_price - 1),   # 当前K线振幅
                    (close / self.am.sma(5) - 1),           # 相对SMA5偏离
                    (close / self.am.sma(20) - 1),          # 相对SMA20偏离
                ])
                
                # 动量特征
                if len(self.am.close_array) >= 10:
                    momentum_5 = close / self.am.close_array[-5] - 1
                    momentum_10 = close / self.am.close_array[-10] - 1
                    features.extend([momentum_5, momentum_10])
                else:
                    features.extend([0.0, 0.0])
                
                # 技术指标特征
                rsi = self.am.rsi(14)
                atr = self.am.atr(14)
                macd, signal, hist = self.am.macd(12, 26, 9)
                
                features.extend([
                    (rsi - 50) / 50,                        # 归一化RSI
                    atr / close if close > 0 else 0,        # 相对ATR
                    hist,                                   # MACD柱状图
                ])
                
                # 布林带特征
                try:
                    boll_up, boll_mid, boll_down = self.am.boll(20, 2)
                    bb_position = (close - boll_down) / (boll_up - boll_down) if (boll_up - boll_down) > 0 else 0.5
                    bb_width = (boll_up - boll_down) / boll_mid if boll_mid > 0 else 0
                    features.extend([bb_position, bb_width])
                except:
                    features.extend([0.5, 0.0])
                
                # 成交量特征
                if len(self.am.volume_array) >= 5:
                    vol_ratio = bar.volume / np.mean(self.am.volume_array[-5:]) if np.mean(self.am.volume_array[-5:]) > 0 else 1.0
                    features.append(vol_ratio - 1)
                else:
                    features.append(0.0)
            
            # Alpha158特征（如果可用）
            if self.use_alpha_features and self.alpha_dataset and len(self.price_data['close']) >= self.feature_window:
                try:
                    alpha_features = self._extract_alpha158_features()
                    if alpha_features:
                        # 选择最重要的Alpha特征（避免特征过多）
                        selected_alpha = alpha_features[:10] if len(alpha_features) >= 10 else alpha_features
                        features.extend(selected_alpha)
                except Exception as e:
                    self.write_log(f"提取Alpha特征失败: {e}")
            
            # 确保特征向量长度一致
            while len(features) < 20:
                features.append(0.0)
            
            return features[:20]  # 限制特征数量
            
        except Exception as e:
            self.write_log(f"提取特征失败: {e}")
            return [0.0] * 20
    
    def _extract_alpha158_features(self) -> List[float]:
        """提取Alpha158特征"""
        try:
            if not VNPY_ALPHA_AVAILABLE:
                return []
            
            # 转换数据格式
            import pandas as pd
            df = pd.DataFrame(self.price_data)
            
            # 提取特征
            features = self.alpha_dataset.extract_features(df)
            
            if isinstance(features, np.ndarray):
                return features.tolist()
            elif isinstance(features, list):
                return features
            else:
                return []
                
        except Exception as e:
            self.write_log(f"提取Alpha158特征失败: {e}")
            return []
    
    def _get_signal(self, features: List[float]) -> Tuple[int, float]:
        """获取交易信号"""
        try:
            if self.signal_api and self.signal_api.is_initialized():
                return self.signal_api.predict(features)
            else:
                # 简单的技术指标信号
                return self._simple_signal(features)
        except Exception as e:
            self.write_log(f"获取信号失败: {e}")
            return 0, 0.0
    
    def _simple_signal(self, features: List[float]) -> Tuple[int, float]:
        """简单信号生成（备用方案）"""
        if len(features) < 4:
            return 0, 0.0
        
        # 基于前几个特征的简单逻辑
        price_change = features[0]  # 价格变化
        sma5_dev = features[2]      # SMA5偏离
        sma20_dev = features[3]     # SMA20偏离
        
        # 趋势信号
        if price_change > 0.005 and sma5_dev > 0 and sma20_dev > 0:
            return 1, min(0.8, 0.6 + abs(price_change) * 20)
        elif price_change < -0.005 and sma5_dev < 0 and sma20_dev < 0:
            return -1, min(0.8, 0.6 + abs(price_change) * 20)
        else:
            return 0, 0.5
    
    def _adjust_threshold(self):
        """自适应阈值调整"""
        if len(self.trade_results) < self.performance_window:
            return
        
        # 计算最近的交易表现
        recent_results = self.trade_results[-self.performance_window:]
        win_rate = sum(1 for r in recent_results if r > 0) / len(recent_results)
        avg_return = np.mean(recent_results)
        
        # 根据表现调整阈值
        if win_rate > 0.6 and avg_return > 0:
            # 表现良好，降低阈值以增加交易频率
            self.current_threshold = max(0.55, self.current_threshold - 0.01)
        elif win_rate < 0.4 or avg_return < 0:
            # 表现不佳，提高阈值以减少交易频率
            self.current_threshold = min(0.8, self.current_threshold + 0.01)
        
        self.write_log(f"阈值调整: {self.current_threshold:.3f}, 胜率: {win_rate:.2%}")
    
    def _execute_trading_logic(self, bar: BarData):
        """执行交易逻辑"""
        try:
            # 检查是否满足交易条件
            if not self._can_trade():
                return
            
            current_pos = self.pos
            target_pos = 0
            
            # 根据信号计算目标仓位
            if abs(self.signal_confidence) >= max(self.current_threshold, self.min_confidence):
                # 计算仓位大小
                position_size = self._calculate_position_size(bar.close_price)
                
                if self.signal_value == 1:
                    target_pos = position_size
                elif self.signal_value == -1:
                    target_pos = -position_size
            
            # 止损止盈检查
            if current_pos != 0:
                if self._check_stop_loss(bar, current_pos) or self._check_take_profit(bar, current_pos):
                    target_pos = 0
            
            # 执行交易
            self._execute_orders(bar, current_pos, target_pos)
            
        except Exception as e:
            self.write_log(f"执行交易逻辑失败: {e}")
    
    def _can_trade(self) -> bool:
        """检查是否可以交易"""
        # 检查日交易次数限制
        if self.daily_trades >= self.max_daily_trades:
            return False
        
        # 检查日损失限制
        if abs(self.daily_pnl) >= self.daily_loss_limit:
            return False
        
        # 检查最大回撤
        if self.max_drawdown_value >= self.max_drawdown:
            return False
        
        return True
    
    def _calculate_position_size(self, price: float) -> int:
        """计算仓位大小"""
        try:
            # 基础仓位
            base_size = int(100000 * self.position_ratio / price)  # 假设账户资金100万
            
            # 根据置信度调整
            confidence_factor = min(self.signal_confidence / 0.8, 1.0)
            adjusted_size = int(base_size * confidence_factor)
            
            # 应用最大仓位限制
            max_size = int(100000 * self.max_position_ratio / price)
            final_size = min(adjusted_size, max_size)
            
            return max(1, final_size)  # 至少1手
            
        except Exception as e:
            self.write_log(f"计算仓位大小失败: {e}")
            return 1

    def _check_stop_loss(self, bar: BarData, current_pos: int) -> bool:
        """检查止损"""
        if current_pos == 0 or self.entry_price == 0:
            return False

        if current_pos > 0:
            # 多头止损
            stop_price = self.entry_price * (1 - self.stop_loss_ratio)

            if self.trailing_stop and self.high_since_entry > 0:
                # 追踪止损
                trail_stop = self.high_since_entry * (1 - self.trailing_ratio * self.stop_loss_ratio)
                stop_price = max(stop_price, trail_stop)

            if bar.low_price <= stop_price:
                self.write_log(f"多头止损触发: {stop_price:.2f}")
                return True

        elif current_pos < 0:
            # 空头止损
            stop_price = self.entry_price * (1 + self.stop_loss_ratio)

            if self.trailing_stop and self.low_since_entry > 0:
                # 追踪止损
                trail_stop = self.low_since_entry * (1 + self.trailing_ratio * self.stop_loss_ratio)
                stop_price = min(stop_price, trail_stop)

            if bar.high_price >= stop_price:
                self.write_log(f"空头止损触发: {stop_price:.2f}")
                return True

        return False

    def _check_take_profit(self, bar: BarData, current_pos: int) -> bool:
        """检查止盈"""
        if current_pos == 0 or self.entry_price == 0:
            return False

        if current_pos > 0:
            # 多头止盈
            take_profit_price = self.entry_price * (1 + self.take_profit_ratio)
            if bar.high_price >= take_profit_price:
                self.write_log(f"多头止盈触发: {take_profit_price:.2f}")
                return True

        elif current_pos < 0:
            # 空头止盈
            take_profit_price = self.entry_price * (1 - self.take_profit_ratio)
            if bar.low_price <= take_profit_price:
                self.write_log(f"空头止盈触发: {take_profit_price:.2f}")
                return True

        return False

    def _execute_orders(self, bar: BarData, current_pos: int, target_pos: int):
        """执行订单"""
        if target_pos == current_pos:
            return

        # 更新入场后的最高/最低价
        if current_pos > 0:
            self.high_since_entry = max(self.high_since_entry, bar.high_price)
        elif current_pos < 0:
            self.low_since_entry = min(self.low_since_entry, bar.low_price)

        # 执行交易
        if target_pos > current_pos:
            # 买入
            volume = target_pos - current_pos
            self.buy(bar.close_price, volume)

            if current_pos <= 0:
                # 新开多仓
                self.entry_price = bar.close_price
                self.high_since_entry = bar.high_price
                self.low_since_entry = 0
                self.write_log(f"开多仓: 价格={bar.close_price:.2f}, 数量={volume}, 信号={self.signal_value}, 置信度={self.signal_confidence:.3f}")

        elif target_pos < current_pos:
            # 卖出
            volume = current_pos - target_pos
            self.sell(bar.close_price, volume)

            if current_pos >= 0:
                # 新开空仓
                self.entry_price = bar.close_price
                self.low_since_entry = bar.low_price
                self.high_since_entry = 0
                self.write_log(f"开空仓: 价格={bar.close_price:.2f}, 数量={volume}, 信号={self.signal_value}, 置信度={self.signal_confidence:.3f}")

        # 更新交易计数
        self.daily_trades += 1
        self.last_trade_time = bar.datetime

    def _risk_management(self, bar: BarData):
        """风险管理"""
        try:
            # 更新日PnL
            current_time = bar.datetime
            if self.last_trade_time and current_time.date() != self.last_trade_time.date():
                # 新的一天，重置日计数器
                self.daily_trades = 0
                self.daily_pnl = 0.0
                self.daily_start_capital = self.pos * bar.close_price

            # 计算当前PnL
            if self.pos != 0:
                current_value = self.pos * bar.close_price
                self.daily_pnl = (current_value - self.daily_start_capital) / abs(self.daily_start_capital)

            # 更新最大回撤
            if self.daily_pnl < 0:
                self.max_drawdown_value = max(self.max_drawdown_value, abs(self.daily_pnl))
            else:
                # 盈利时重置回撤
                self.max_drawdown_value = max(0, self.max_drawdown_value - 0.001)

        except Exception as e:
            self.write_log(f"风险管理计算失败: {e}")

    def on_order(self, order: OrderData):
        """订单状态更新回调"""
        pass

    def on_trade(self, trade: TradeData):
        """成交回调"""
        try:
            # 更新持仓
            self.pos = self.get_pos()

            # 记录交易结果
            if self.entry_price > 0:
                if trade.direction.value == "多":
                    pnl_ratio = (trade.price - self.entry_price) / self.entry_price
                else:
                    pnl_ratio = (self.entry_price - trade.price) / self.entry_price

                self.trade_results.append(pnl_ratio)

                # 保持结果列表大小
                if len(self.trade_results) > 100:
                    self.trade_results.pop(0)

            self.write_log(f"交易执行: {trade.direction} {trade.volume}手 @{trade.price:.2f}")
            self.put_event()

        except Exception as e:
            self.write_log(f"处理交易回调失败: {e}")

    def on_stop_order(self, stop_order: StopOrder):
        """停止单回调"""
        pass
