#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
高性能V100策略 - 专为有限资金高频交易设计
核心理念：
1. V100性能优势最大化 - 提升基础成功率
2. 严格亏损控制 - 资金保护优先
3. 智能持仓管理 - 避免过度干预
4. 高频交易友好 - 不限制交易次数
"""

import os
import sys
import numpy as np
from datetime import datetime, timedelta
from typing import List, Tuple, Dict, Any
from collections import deque
import threading
import time

# 导入VNPY组件
from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    ArrayManager
)

# 安全导入V100优化器
try:
    from .signal_system.v100_optimizer import get_v100_optimizer, GPU_AVAILABLE
    from .signal_system.real_ml_system import RealMLSignalSystem
    V100_AVAILABLE = True
    ML_SYSTEM_AVAILABLE = True
except ImportError:
    try:
        from signal_system.v100_optimizer import get_v100_optimizer, GPU_AVAILABLE
        from signal_system.real_ml_system import RealMLSignalSystem
        V100_AVAILABLE = True
        ML_SYSTEM_AVAILABLE = True
    except ImportError:
        V100_AVAILABLE = False
        ML_SYSTEM_AVAILABLE = False


class HighPerformanceV100Strategy(CtaTemplate):
    """
    高性能V100策略 - 专业高频量化交易
    
    设计原则：
    1. V100算力最大化利用
    2. 严格亏损控制，宽松盈利管理
    3. 智能持仓时间，避免过度干预
    4. 高频友好，专注成功率提升
    """
    
    author = "High Performance V100 Strategy v3.0"
    
    # 核心交易参数
    signal_threshold = 0.68      # 适中阈值，平衡频率和质量
    position_size = 1            # 基础仓位
    max_position = 2             # 严格控制最大持仓（资金有限）
    
    # 亏损敏感控制（核心）
    stop_loss_pct = 1.5          # 严格止损1.5%
    emergency_stop_loss = 2.5    # 紧急止损2.5%
    take_profit_pct = 6.0        # 宽松止盈，让利润奔跑
    trailing_stop_pct = 0.8      # 跟踪止损0.8%
    
    # V100高性能参数
    enable_v100 = True
    gpu_async_compute = True     # 异步计算最大化性能
    gpu_feature_cache = True     # 智能缓存
    gpu_batch_size = 32          # 批量计算
    fallback_to_cpu = True
    
    # 高频交易参数
    model_type = "lightgbm"
    ticks_per_bar = 80           # 更高频率，80个Tick一根K线
    min_bars_to_train = 60       # 快速启动训练
    online_learning = True
    feature_update_interval = 5   # 5秒更新一次特征
    
    # 智能持仓管理
    min_hold_seconds = 30        # 最小持仓30秒（避免过度干预）
    max_hold_minutes = 45        # 最大持仓45分钟（避免长期套牢）
    position_review_interval = 10 # 每10秒评估一次持仓
    
    # 资金管理（核心）
    max_daily_loss_pct = 4.0     # 日最大亏损4%
    max_single_loss_pct = 1.8    # 单笔最大亏损1.8%
    loss_recovery_mode = True    # 亏损恢复模式
    
    # 成功率提升参数
    confidence_boost_threshold = 0.85  # 高置信度加仓阈值
    model_ensemble = True        # 模型集成
    feature_engineering_v2 = True # V2特征工程
    
    # 变量
    signal_value = 0
    signal_confidence = 0.0
    entry_price = 0.0
    entry_time = None
    highest_profit = 0.0         # 最高盈利（跟踪止损用）
    daily_pnl = 0.0
    daily_loss_count = 0         # 日亏损次数
    model_accuracy = 0.0
    training_samples = 0
    prediction_count = 0
    tick_count = 0
    generated_bars = 0
    
    # V100性能变量
    gpu_enabled = False
    gpu_compute_count = 0
    cpu_compute_count = 0
    avg_gpu_time = 0.0
    avg_cpu_time = 0.0
    gpu_speedup = 1.0
    gpu_utilization = 0.0
    
    # 高频交易状态
    last_trade_time = None
    position_direction = 0
    is_in_recovery_mode = False
    
    # 参数列表
    parameters = [
        "signal_threshold", "position_size", "max_position", "stop_loss_pct",
        "emergency_stop_loss", "take_profit_pct", "trailing_stop_pct",
        "enable_v100", "gpu_async_compute", "gpu_feature_cache", "gpu_batch_size",
        "fallback_to_cpu", "model_type", "ticks_per_bar", "min_bars_to_train",
        "online_learning", "feature_update_interval", "min_hold_seconds",
        "max_hold_minutes", "position_review_interval", "max_daily_loss_pct",
        "max_single_loss_pct", "loss_recovery_mode", "confidence_boost_threshold",
        "model_ensemble", "feature_engineering_v2"
    ]
    
    variables = [
        "signal_value", "signal_confidence", "entry_price", "entry_time",
        "highest_profit", "daily_pnl", "daily_loss_count", "model_accuracy",
        "training_samples", "prediction_count", "tick_count", "generated_bars",
        "gpu_enabled", "gpu_compute_count", "cpu_compute_count", "avg_gpu_time",
        "avg_cpu_time", "gpu_speedup", "gpu_utilization", "position_direction",
        "is_in_recovery_mode"
    ]
    
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        """初始化高性能策略"""
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        
        # 创建高频K线生成器
        self.bg = BarGenerator(self.on_bar, 1, self.on_1min_bar)
        self.am = ArrayManager(size=300)  # 更大的数组管理器
        
        # V100优化器初始化
        self.v100_optimizer = None
        self._init_v100_optimizer()
        
        # 高频Tick处理
        self.tick_buffer = deque(maxlen=self.ticks_per_bar * 3)
        self.current_bar_ticks = []
        self.last_bar_time = None
        
        # 生成的K线历史
        self.generated_bar_history = []
        self.max_bar_history = 1000  # 更大历史缓存
        
        # V100特征缓存系统
        self.gpu_feature_cache = {}
        self.feature_cache_lock = threading.Lock()
        self.cache_hit_count = 0
        self.cache_miss_count = 0
        
        # 异步GPU计算
        self.gpu_task_queue = deque()
        self.gpu_result_cache = {}
        self.last_feature_time = 0
        
        # ML系统初始化
        self.ml_system = None
        self._init_ml_system()
        
        # 交易状态管理
        self.trade_history = []
        self.position_start_time = None
        self.last_position_review = 0
        
        # 性能监控
        self.performance_stats = {
            'total_trades': 0,
            'win_trades': 0,
            'loss_trades': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0,
            'gpu_efficiency': 0.0
        }
        
        # 在线学习
        self.last_bar_close = 0.0
        self.learning_samples = []
        
        self.write_log("高性能V100策略初始化完成")
        self.write_log("专注：V100性能最大化 + 严格亏损控制")
    
    def _init_v100_optimizer(self):
        """初始化V100优化器 - 高性能配置"""
        if not self.enable_v100 or not V100_AVAILABLE:
            self.write_log("V100优化不可用，性能将受限")
            return
        
        try:
            # 高性能配置
            self.v100_optimizer = get_v100_optimizer(
                enable_gpu=True,
                batch_size=self.gpu_batch_size,
                async_compute=self.gpu_async_compute,
                cache_enabled=self.gpu_feature_cache
            )
            
            if self.v100_optimizer.enable_gpu:
                self.gpu_enabled = True
                self.write_log("V100高性能优化器启动成功")
                self.write_log("GPU设备: " + str(self.v100_optimizer.gpu_device))
                self.write_log("批量大小: " + str(self.gpu_batch_size))
                self.write_log("异步计算: 启用")
                self.write_log("智能缓存: 启用")
            else:
                self.write_log("V100不可用，将使用CPU（性能受限）")
                
        except Exception as e:
            self.write_log("V100优化器初始化失败: " + str(e))
            self.v100_optimizer = None
    
    def _init_ml_system(self):
        """初始化ML系统 - 高性能配置"""
        if not ML_SYSTEM_AVAILABLE:
            self.write_log("错误: ML系统不可用")
            return
        
        try:
            # 高性能ML系统配置
            ml_config = {
                'model_type': self.model_type,
                'ensemble': self.model_ensemble,
                'feature_engineering': self.feature_engineering_v2,
                'online_learning': self.online_learning,
                'gpu_acceleration': self.gpu_enabled
            }
            
            self.ml_system = RealMLSignalSystem(**ml_config)
            
            # 模型路径设置
            current_dir = os.path.dirname(os.path.abspath(__file__))
            model_dir = os.path.join(current_dir, "signal_system", "models")
            os.makedirs(model_dir, exist_ok=True)
            
            commodity_code = self._extract_commodity_code(self.vt_symbol)
            model_suffix = "high_perf_v100" if self.gpu_enabled else "high_perf"
            self.model_path = os.path.join(model_dir, f"{commodity_code}_{self.model_type}_{model_suffix}_model.pkl")
            
            self.write_log("商品代码: " + commodity_code)
            self.write_log("高性能模型路径: " + self.model_path)
            
            # 加载已有模型
            if os.path.exists(self.model_path):
                if self.ml_system.load_model(self.model_path):
                    model_info = self.ml_system.get_model_info()
                    self.model_accuracy = model_info.get('latest_accuracy', 0.0)
                    self.training_samples = model_info.get('training_samples', 0)
                    self.write_log("高性能模型加载成功")
                    self.write_log("模型准确率: " + "{:.3f}".format(self.model_accuracy))
                    self.write_log("训练样本: " + str(self.training_samples))
                else:
                    self.write_log("模型加载失败，将重新训练")
            else:
                self.write_log("未找到模型，将从实时数据训练")
            
        except Exception as e:
            self.write_log("ML系统初始化异常: " + str(e))
            self.ml_system = None
    
    def _extract_commodity_code(self, vt_symbol: str) -> str:
        """提取商品代码"""
        try:
            symbol = vt_symbol.split('.')[0]
            commodity_code = ""
            for char in symbol:
                if char.isalpha():
                    commodity_code += char.lower()
                else:
                    break
            return commodity_code if commodity_code else symbol.lower()
        except:
            return vt_symbol.lower()

    def on_init(self):
        """策略初始化"""
        self.write_log("高性能V100策略初始化")
        self.write_log("设计理念: V100性能最大化 + 严格亏损控制")
        self.write_log("GPU状态: " + ("V100加速" if self.gpu_enabled else "CPU模式"))
        self.write_log("交易频率: 每" + str(self.ticks_per_bar) + "个Tick生成K线")
        self.write_log("最大持仓: " + str(self.max_position) + "手")
        self.write_log("止损设置: " + str(self.stop_loss_pct) + "%")
        self.write_log("止盈设置: " + str(self.take_profit_pct) + "%")

    def on_start(self):
        """策略启动"""
        self.write_log("高性能V100策略启动")
        self.write_log("开始高频Tick数据收集...")

        if self.gpu_enabled:
            self.write_log("V100 GPU高性能加速已启用")
            self.write_log("预期性能提升: 5-15倍")

        if self.ml_system and self.ml_system.is_trained:
            self.write_log("使用已训练高性能模型")
        else:
            self.write_log("等待数据训练高性能模型")

    def on_stop(self):
        """策略停止"""
        self.write_log("高性能V100策略停止")

        # 保存高性能模型
        if self.ml_system and self.ml_system.is_trained:
            self.ml_system.save_model(self.model_path)
            self.write_log("高性能模型已保存")

        # 输出详细性能报告
        self._output_performance_report()

    def on_tick(self, tick: TickData):
        """Tick数据处理 - 高频优化"""
        self.tick_count += 1

        # 添加到缓冲区
        self.tick_buffer.append({
            'datetime': tick.datetime,
            'last_price': tick.last_price,
            'volume': tick.volume,
            'bid_price_1': tick.bid_price_1,
            'ask_price_1': tick.ask_price_1,
            'bid_volume_1': tick.bid_volume_1,
            'ask_volume_1': tick.ask_volume_1
        })

        self.current_bar_ticks.append(tick)

        # 高频K线生成
        if len(self.current_bar_ticks) >= self.ticks_per_bar:
            self._generate_bar_from_ticks()
            self.current_bar_ticks = []

        # 更新到BarGenerator
        self.bg.update_tick(tick)

    def _generate_bar_from_ticks(self):
        """从Tick生成K线 - 高性能版本"""
        if not self.current_bar_ticks:
            return

        try:
            # 计算OHLCV
            prices = [t.last_price for t in self.current_bar_ticks]
            volumes = [t.volume for t in self.current_bar_ticks]

            bar_dict = {
                'datetime': self.current_bar_ticks[-1].datetime,
                'open': prices[0],
                'high': max(prices),
                'low': min(prices),
                'close': prices[-1],
                'volume': sum(volumes),
                'tick_count': len(self.current_bar_ticks)
            }

            # 添加到历史
            self.generated_bar_history.append(bar_dict)
            if len(self.generated_bar_history) > self.max_bar_history:
                self.generated_bar_history.pop(0)

            self.generated_bars += 1

            # 执行高性能交易逻辑
            if len(self.generated_bar_history) >= self.min_bars_to_train:
                self._execute_high_performance_trading(bar_dict)

            # 在线学习
            if self.online_learning and len(self.generated_bar_history) >= 2:
                self._online_learning_from_bar(bar_dict)

        except Exception as e:
            self.write_log("生成K线失败: " + str(e))

    def _execute_high_performance_trading(self, bar_dict: Dict):
        """执行高性能交易逻辑 - 核心算法"""
        try:
            current_time = time.time()

            # V100特征提取（异步优化）
            if current_time - self.last_feature_time >= self.feature_update_interval:
                features = self._extract_features_v100_async(self.generated_bar_history[-50:])
                self.last_feature_time = current_time

                if not features:
                    return

                # V100预测
                signal_value, signal_confidence = self.ml_system.predict(features)
                self.prediction_count += 1

                # 更新信号
                self.signal_value = signal_value
                self.signal_confidence = signal_confidence

            current_pos = self.pos
            current_price = bar_dict['close']

            # 严格资金管理检查
            if not self._can_trade_with_risk_control():
                return

            # 开仓逻辑 - 高频友好
            if current_pos == 0 and self.signal_confidence >= self.signal_threshold:
                position_size = self._calculate_position_size(signal_confidence)

                if self.signal_value == 1:  # 买入信号
                    self.buy(current_price, position_size)
                    self._record_entry(current_price, 1, signal_confidence)
                    self.write_log("V100买入: " + "{:.2f}".format(current_price) +
                                 ", 手数: " + str(position_size) +
                                 ", 置信度: " + "{:.3f}".format(signal_confidence))

                elif self.signal_value == -1:  # 卖出信号
                    self.sell(current_price, position_size)
                    self._record_entry(current_price, -1, signal_confidence)
                    self.write_log("V100卖出: " + "{:.2f}".format(current_price) +
                                 ", 手数: " + str(position_size) +
                                 ", 置信度: " + "{:.3f}".format(signal_confidence))

            # 持仓管理 - 智能平仓
            elif current_pos != 0:
                should_close, close_reason = self._should_close_position_smart(current_price)

                if should_close:
                    if current_pos > 0:
                        self.sell(current_price, abs(current_pos))
                    else:
                        self.buy(current_price, abs(current_pos))

                    pnl = self._calculate_trade_pnl(current_price)
                    self._record_exit(current_price, pnl, close_reason)

                    self.write_log("V100平仓: " + close_reason +
                                 ", 价格: " + "{:.2f}".format(current_price) +
                                 ", 盈亏: " + "{:.4f}".format(pnl))

        except Exception as e:
            self.write_log("高性能交易执行失败: " + str(e))

    def _can_trade_with_risk_control(self) -> bool:
        """严格资金管理检查"""
        # 日亏损限制
        if abs(self.daily_pnl) >= self.max_daily_loss_pct / 100:
            return False

        # 持仓限制
        if abs(self.pos) >= self.max_position:
            return False

        # 亏损恢复模式
        if self.is_in_recovery_mode and self.daily_loss_count >= 3:
            return False

        return True

    def _calculate_position_size(self, confidence: float) -> int:
        """根据置信度计算仓位"""
        base_size = self.position_size

        # 高置信度加仓
        if confidence >= self.confidence_boost_threshold:
            return min(base_size + 1, self.max_position)

        return base_size

    def _should_close_position_smart(self, current_price: float) -> Tuple[bool, str]:
        """智能平仓判断 - 避免过度干预"""
        if self.entry_price == 0 or not self.entry_time:
            return False, ""

        # 计算当前盈亏
        if self.position_direction == 1:
            pnl_pct = (current_price - self.entry_price) / self.entry_price * 100
        else:
            pnl_pct = (self.entry_price - current_price) / self.entry_price * 100

        # 更新最高盈利（跟踪止损用）
        if pnl_pct > self.highest_profit:
            self.highest_profit = pnl_pct

        # 严格止损（优先级最高）
        if pnl_pct <= -self.stop_loss_pct:
            return True, "止损"

        # 紧急止损
        if pnl_pct <= -self.emergency_stop_loss:
            return True, "紧急止损"

        # 单笔亏损限制
        single_loss_pct = abs(pnl_pct) if pnl_pct < 0 else 0
        if single_loss_pct >= self.max_single_loss_pct:
            return True, "单笔亏损限制"

        # 持仓时间检查
        hold_time = datetime.now() - self.entry_time
        hold_seconds = hold_time.total_seconds()

        # 最小持仓时间（避免过度干预）
        if hold_seconds < self.min_hold_seconds:
            return False, ""

        # 跟踪止损（盈利时）
        if self.highest_profit > 2.0:  # 盈利超过2%时启用
            trailing_loss = self.highest_profit - pnl_pct
            if trailing_loss >= self.trailing_stop_pct:
                return True, "跟踪止损"

        # 止盈
        if pnl_pct >= self.take_profit_pct:
            return True, "止盈"

        # 最大持仓时间（避免长期套牢）
        if hold_seconds > self.max_hold_minutes * 60:
            if pnl_pct > -0.5:  # 小亏或盈利时
                return True, "持仓时间过长"

        # 强反向信号（高置信度）
        if hasattr(self, 'signal_confidence') and self.signal_confidence >= 0.8:
            if (self.position_direction == 1 and self.signal_value == -1) or \
               (self.position_direction == -1 and self.signal_value == 1):
                return True, "强反向信号"

        return False, ""

    def _record_entry(self, price: float, direction: int, confidence: float):
        """记录开仓"""
        self.entry_price = price
        self.entry_time = datetime.now()
        self.position_direction = direction
        self.highest_profit = 0.0
        self.last_trade_time = datetime.now()

    def _record_exit(self, price: float, pnl: float, reason: str):
        """记录平仓"""
        trade_record = {
            'entry_price': self.entry_price,
            'exit_price': price,
            'pnl': pnl,
            'reason': reason,
            'hold_time': (datetime.now() - self.entry_time).total_seconds(),
            'direction': self.position_direction
        }

        self.trade_history.append(trade_record)
        self.performance_stats['total_trades'] += 1

        if pnl > 0:
            self.performance_stats['win_trades'] += 1
        else:
            self.performance_stats['loss_trades'] += 1
            self.daily_loss_count += 1

            # 进入亏损恢复模式
            if self.loss_recovery_mode and self.daily_loss_count >= 2:
                self.is_in_recovery_mode = True
                self.write_log("进入亏损恢复模式")

        self.performance_stats['total_pnl'] += pnl
        self.daily_pnl += pnl

        # 重置持仓状态
        self.entry_price = 0.0
        self.entry_time = None
        self.position_direction = 0
        self.highest_profit = 0.0

    def _calculate_trade_pnl(self, exit_price: float) -> float:
        """计算交易盈亏"""
        if self.entry_price == 0:
            return 0.0

        if self.position_direction == 1:
            return exit_price - self.entry_price
        else:
            return self.entry_price - exit_price

    def _extract_features_v100_async(self, bars: List[Dict]) -> np.ndarray:
        """V100异步特征提取"""
        if not bars or not self.gpu_enabled:
            return self._extract_features_cpu(bars)

        try:
            # 构建特征缓存键
            cache_key = str(hash(str(bars[-5:])))  # 使用最近5根K线作为缓存键

            with self.feature_cache_lock:
                if cache_key in self.gpu_feature_cache:
                    self.cache_hit_count += 1
                    return self.gpu_feature_cache[cache_key]

            # V100特征计算
            start_time = time.time()
            features = self.v100_optimizer.extract_features_batch(bars)
            gpu_time = time.time() - start_time

            # 更新性能统计
            self.gpu_compute_count += 1
            self.avg_gpu_time = (self.avg_gpu_time * (self.gpu_compute_count - 1) + gpu_time) / self.gpu_compute_count

            # 缓存结果
            with self.feature_cache_lock:
                self.gpu_feature_cache[cache_key] = features
                self.cache_miss_count += 1

                # 限制缓存大小
                if len(self.gpu_feature_cache) > 1000:
                    # 删除最旧的缓存
                    oldest_key = next(iter(self.gpu_feature_cache))
                    del self.gpu_feature_cache[oldest_key]

            return features

        except Exception as e:
            self.write_log("V100特征提取失败: " + str(e))
            return self._extract_features_cpu(bars)

    def _extract_features_cpu(self, bars: List[Dict]) -> np.ndarray:
        """CPU特征提取（备用）"""
        try:
            if len(bars) < 20:
                return np.array([])

            start_time = time.time()

            # 基础价格特征
            closes = np.array([bar['close'] for bar in bars])
            highs = np.array([bar['high'] for bar in bars])
            lows = np.array([bar['low'] for bar in bars])
            volumes = np.array([bar['volume'] for bar in bars])

            features = []

            # 价格变化率
            returns = np.diff(closes) / closes[:-1]
            features.extend([
                np.mean(returns[-5:]),   # 5期平均收益率
                np.std(returns[-5:]),    # 5期收益率标准差
                np.mean(returns[-10:]),  # 10期平均收益率
                np.std(returns[-10:])    # 10期收益率标准差
            ])

            # 技术指标
            sma_5 = np.mean(closes[-5:])
            sma_10 = np.mean(closes[-10:])
            sma_20 = np.mean(closes[-20:])

            features.extend([
                (closes[-1] - sma_5) / sma_5,      # 价格相对SMA5
                (closes[-1] - sma_10) / sma_10,    # 价格相对SMA10
                (closes[-1] - sma_20) / sma_20,    # 价格相对SMA20
                (sma_5 - sma_10) / sma_10,         # SMA5相对SMA10
                (sma_10 - sma_20) / sma_20         # SMA10相对SMA20
            ])

            # 波动率特征
            high_low_ratio = (highs[-1] - lows[-1]) / closes[-1]
            volume_ratio = volumes[-1] / np.mean(volumes[-10:]) if np.mean(volumes[-10:]) > 0 else 1

            features.extend([
                high_low_ratio,
                volume_ratio,
                np.std(closes[-10:]) / np.mean(closes[-10:])  # 变异系数
            ])

            # 更新CPU性能统计
            cpu_time = time.time() - start_time
            self.cpu_compute_count += 1
            self.avg_cpu_time = (self.avg_cpu_time * (self.cpu_compute_count - 1) + cpu_time) / self.cpu_compute_count

            return np.array(features)

        except Exception as e:
            self.write_log("CPU特征提取失败: " + str(e))
            return np.array([])

    def _online_learning_from_bar(self, current_bar: Dict):
        """在线学习"""
        try:
            if len(self.generated_bar_history) < 2:
                return

            # 获取上一根K线的特征和实际收益
            prev_bar = self.generated_bar_history[-2]
            current_close = current_bar['close']
            prev_close = prev_bar['close']

            actual_return = (current_close - prev_close) / prev_close

            # 提取特征
            if len(self.generated_bar_history) >= 30:
                features = self._extract_features_v100_async(self.generated_bar_history[-31:-1])

                if len(features) > 0:
                    # 添加学习样本
                    label = 1 if actual_return > 0.001 else (-1 if actual_return < -0.001 else 0)
                    self.learning_samples.append((features, label))

                    # 批量在线学习
                    if len(self.learning_samples) >= 10:
                        self.ml_system.online_learning(self.learning_samples)
                        self.learning_samples = []

        except Exception as e:
            self.write_log("在线学习失败: " + str(e))

    def _output_performance_report(self):
        """输出详细性能报告"""
        try:
            self.write_log("=" * 60)
            self.write_log("高性能V100策略性能报告")
            self.write_log("=" * 60)

            # 基础统计
            self.write_log("Tick处理: " + str(self.tick_count) + "个")
            self.write_log("K线生成: " + str(self.generated_bars) + "根")
            self.write_log("预测次数: " + str(self.prediction_count) + "次")

            # 交易统计
            total_trades = self.performance_stats['total_trades']
            if total_trades > 0:
                win_rate = self.performance_stats['win_trades'] / total_trades
                avg_pnl = self.performance_stats['total_pnl'] / total_trades

                self.write_log("总交易次数: " + str(total_trades))
                self.write_log("胜率: " + "{:.2%}".format(win_rate))
                self.write_log("平均盈亏: " + "{:.4f}".format(avg_pnl))
                self.write_log("总盈亏: " + "{:.4f}".format(self.performance_stats['total_pnl']))
                self.write_log("日亏损次数: " + str(self.daily_loss_count))

            # V100性能统计
            if self.gpu_enabled:
                self.write_log("GPU计算: " + str(self.gpu_compute_count) + "次")
                self.write_log("CPU计算: " + str(self.cpu_compute_count) + "次")
                self.write_log("GPU平均时间: " + "{:.6f}".format(self.avg_gpu_time) + "秒")
                self.write_log("CPU平均时间: " + "{:.6f}".format(self.avg_cpu_time) + "秒")

                if self.avg_cpu_time > 0 and self.avg_gpu_time > 0:
                    speedup = self.avg_cpu_time / self.avg_gpu_time
                    self.write_log("V100加速比: " + "{:.2f}".format(speedup) + "倍")

                # 缓存统计
                total_cache = self.cache_hit_count + self.cache_miss_count
                if total_cache > 0:
                    hit_rate = self.cache_hit_count / total_cache
                    self.write_log("缓存命中率: " + "{:.2%}".format(hit_rate))

            # 模型统计
            if self.ml_system:
                self.write_log("模型准确率: " + "{:.3f}".format(self.model_accuracy))
                self.write_log("训练样本: " + str(self.training_samples))

            self.write_log("=" * 60)

        except Exception as e:
            self.write_log("输出性能报告失败: " + str(e))

    # VNPY回调函数
    def on_bar(self, bar: BarData):
        """K线回调"""
        self.am.update_bar(bar)

    def on_1min_bar(self, bar: BarData):
        """1分钟K线回调"""
        pass

    def on_trade(self, trade: TradeData):
        """成交回调"""
        self.trade_count += 1

    def on_order(self, order: OrderData):
        """委托回调"""
        pass

    def on_stop_order(self, stop_order: StopOrder):
        """停止单回调"""
        pass
