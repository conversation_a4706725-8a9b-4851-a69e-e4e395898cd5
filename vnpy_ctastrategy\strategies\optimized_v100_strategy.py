#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化版V100策略 - 解决频繁交易问题
专业量化策略优化版本
"""

import os
import sys
import numpy as np
from datetime import datetime, timedelta
from typing import List, Tuple, Dict, Any
from collections import deque

# 导入VNPY组件
from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    ArrayManager
)

# 安全导入V100优化器
try:
    from .signal_system.v100_optimizer import get_v100_optimizer, GPU_AVAILABLE
    from .signal_system.real_ml_system import RealMLSignalSystem
    V100_AVAILABLE = True
    ML_SYSTEM_AVAILABLE = True
except ImportError:
    try:
        from signal_system.v100_optimizer import get_v100_optimizer, GPU_AVAILABLE
        from signal_system.real_ml_system import RealMLSignalSystem
        V100_AVAILABLE = True
        ML_SYSTEM_AVAILABLE = True
    except ImportError:
        V100_AVAILABLE = False
        ML_SYSTEM_AVAILABLE = False


class OptimizedV100Strategy(CtaTemplate):
    """
    优化版V100策略 - 专业量化交易策略
    
    优化重点：
    1. 降低交易频率 - 避免过度交易
    2. 增强信号稳定性 - 多重确认机制
    3. 改进平仓逻辑 - 趋势跟踪优化
    4. 强化风险控制 - 多层风险管理
    """
    
    author = "Optimized V100 Strategy v2.0"
    
    # 策略参数 - 优化版
    signal_threshold = 0.75      # 提高信号阈值，减少噪音交易
    signal_confirm_bars = 2      # 信号确认K线数（新增）
    position_size = 1            # 基础仓位大小
    stop_loss_pct = 2.5          # 止损百分比（略微放宽）
    take_profit_pct = 5.0        # 止盈百分比（提高盈亏比）
    max_position = 3             # 最大持仓手数
    
    # V100优化参数
    enable_v100 = True           # 启用V100优化
    gpu_async_compute = True     # 异步GPU计算
    gpu_feature_cache = True     # GPU特征缓存
    fallback_to_cpu = True       # 自动降级到CPU
    
    # Tick模式参数 - 优化版
    model_type = "lightgbm"      # 模型类型
    ticks_per_bar = 200          # 增加到200个Tick生成一根K线（降频）
    min_bars_to_train = 80       # 增加训练数据要求
    online_learning = True       # 在线学习开关
    
    # 风险控制参数 - 强化版
    daily_max_trades = 6         # 降低日最大交易次数
    max_loss_pct = 3.0           # 降低最大亏损百分比
    max_consecutive_losses = 3   # 最大连续亏损次数（新增）
    min_hold_minutes = 5         # 最小持仓时间（分钟）（新增）
    volatility_filter = True     # 波动率过滤（新增）
    
    # 信号稳定性参数（新增）
    signal_history_size = 5      # 信号历史记录数量
    signal_consistency_ratio = 0.6  # 信号一致性比例
    
    # 变量
    signal_value = 0
    signal_confidence = 0.0
    entry_price = 0.0
    entry_time = None            # 开仓时间（新增）
    daily_trades = 0
    daily_pnl = 0.0
    model_accuracy = 0.0
    training_samples = 0
    prediction_count = 0
    tick_count = 0
    generated_bars = 0
    consecutive_losses = 0       # 连续亏损次数（新增）
    
    # V100性能变量
    gpu_enabled = False
    gpu_compute_count = 0
    cpu_compute_count = 0
    avg_gpu_time = 0.0
    avg_cpu_time = 0.0
    gpu_speedup = 1.0
    
    # 信号历史（新增）
    signal_history = []
    last_signal_time = None
    
    # 参数和变量列表
    parameters = [
        "signal_threshold", "signal_confirm_bars", "position_size", "stop_loss_pct", 
        "take_profit_pct", "max_position", "enable_v100", "gpu_async_compute", 
        "gpu_feature_cache", "fallback_to_cpu", "model_type", "ticks_per_bar", 
        "min_bars_to_train", "online_learning", "daily_max_trades", "max_loss_pct",
        "max_consecutive_losses", "min_hold_minutes", "volatility_filter",
        "signal_history_size", "signal_consistency_ratio"
    ]
    variables = [
        "signal_value", "signal_confidence", "entry_price", "entry_time", "daily_trades",
        "daily_pnl", "model_accuracy", "training_samples", "prediction_count",
        "tick_count", "generated_bars", "consecutive_losses", "gpu_enabled", 
        "gpu_compute_count", "cpu_compute_count", "avg_gpu_time", "avg_cpu_time", 
        "gpu_speedup"
    ]
    
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        """初始化策略"""
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        
        # 创建K线生成器和技术指标管理器
        self.bg = BarGenerator(self.on_bar, 1, self.on_1min_bar)
        self.am = ArrayManager(size=200)
        
        # V100优化器
        self.v100_optimizer = None
        self._init_v100_optimizer()
        
        # Tick数据处理
        self.tick_buffer = deque(maxlen=self.ticks_per_bar * 2)
        self.current_bar_ticks = []
        self.last_bar_time = None
        
        # 生成的K线数据
        self.generated_bar_history = []
        self.max_bar_history = 500
        
        # 特征缓存（GPU优化）
        self.feature_cache = {}
        self.cache_hit_count = 0
        self.cache_miss_count = 0
        
        # 异步计算任务
        self.pending_gpu_tasks = {}
        self.last_feature_compute_time = 0
        
        # 初始化ML系统
        self.ml_system = None
        self._init_ml_system()
        
        # 交易状态
        self.position_direction = 0
        self.last_trade_time = None
        
        # 性能统计
        self.trade_results = []
        self.total_pnl = 0.0
        self.win_count = 0
        self.trade_count = 0
        
        # 在线学习
        self.last_bar_close = 0.0
        
        # 信号历史初始化
        self.signal_history = deque(maxlen=self.signal_history_size)
        
        self.write_log("优化版V100策略初始化完成")
    
    def _init_v100_optimizer(self):
        """初始化V100优化器"""
        if not self.enable_v100 or not V100_AVAILABLE:
            self.write_log("V100优化已禁用或不可用")
            return
        
        try:
            self.v100_optimizer = get_v100_optimizer(enable_gpu=True)
            
            if self.v100_optimizer.enable_gpu:
                self.gpu_enabled = True
                self.write_log("优化版V100优化器初始化成功")
                self.write_log("GPU设备: " + str(self.v100_optimizer.gpu_device))
            else:
                self.write_log("V100不可用，将使用CPU计算")
                
        except Exception as e:
            self.write_log("V100优化器初始化失败: " + str(e))
            self.v100_optimizer = None
    
    def _init_ml_system(self):
        """初始化ML系统"""
        if not ML_SYSTEM_AVAILABLE:
            self.write_log("错误: ML系统不可用")
            return
        
        try:
            # 创建ML系统
            self.ml_system = RealMLSignalSystem(model_type=self.model_type)
            
            # 设置模型保存路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            model_dir = os.path.join(current_dir, "signal_system", "models")
            os.makedirs(model_dir, exist_ok=True)
            
            # 提取商品代码
            commodity_code = self._extract_commodity_code(self.vt_symbol)
            model_suffix = "optimized_v100" if self.gpu_enabled else "optimized"
            self.model_path = os.path.join(model_dir, f"{commodity_code}_{self.model_type}_{model_suffix}_model.pkl")
            
            self.write_log("商品代码: " + commodity_code)
            self.write_log("优化模型路径: " + self.model_path)
            
            # 尝试加载已有模型
            if os.path.exists(self.model_path):
                if self.ml_system.load_model(self.model_path):
                    model_info = self.ml_system.get_model_info()
                    self.model_accuracy = model_info.get('latest_accuracy', 0.0)
                    self.training_samples = model_info.get('training_samples', 0)
                    self.write_log("成功加载优化模型")
                    self.write_log("模型准确率: " + "{:.3f}".format(self.model_accuracy))
                else:
                    self.write_log("加载优化模型失败")
            else:
                self.write_log("未找到优化模型，将从实时数据训练")
            
        except Exception as e:
            self.write_log("初始化ML系统异常: " + str(e))
            self.ml_system = None
    
    def _extract_commodity_code(self, vt_symbol: str) -> str:
        """提取商品代码"""
        try:
            symbol = vt_symbol.split('.')[0]
            commodity_code = ""
            for char in symbol:
                if char.isalpha():
                    commodity_code += char.lower()
                else:
                    break
            return commodity_code if commodity_code else symbol.lower()
        except:
            return vt_symbol.lower()
    
    def on_init(self):
        """策略初始化回调"""
        self.write_log("优化版V100策略初始化")
        self.write_log("GPU状态: " + ("启用" if self.gpu_enabled else "禁用"))
        self.write_log("优化特性: 降频交易、信号确认、趋势跟踪")
        self.write_log("每" + str(self.ticks_per_bar) + "个Tick生成一根K线")
        self.write_log("需要" + str(self.signal_confirm_bars) + "根K线确认信号")
        self.write_log("收集" + str(self.min_bars_to_train) + "根K线后开始训练")
    
    def on_start(self):
        """策略启动回调"""
        self.write_log("优化版V100策略启动")
        self.write_log("开始收集Tick数据...")
        
        if self.gpu_enabled:
            self.write_log("V100 GPU加速已启用")
        
        if self.ml_system and self.ml_system.is_trained:
            self.write_log("使用已训练模型，可立即开始交易")
        else:
            self.write_log("等待收集足够数据进行模型训练")
    
    def on_stop(self):
        """策略停止回调"""
        self.write_log("优化版V100策略停止")
        
        # 保存模型
        if self.ml_system and self.ml_system.is_trained:
            self.ml_system.save_model(self.model_path)
            self.write_log("优化模型已保存")
        
        # 输出性能统计
        self._output_performance_stats()
        
        # 关闭V100优化器
        if self.v100_optimizer:
            try:
                # 获取最终性能统计
                stats = self.v100_optimizer.get_performance_stats()
                self.gpu_speedup = stats.get('speedup_ratio', 1.0)
                self.write_log("V100性能提升: " + "{:.2f}".format(self.gpu_speedup) + "倍")
            except:
                pass

    def _check_signal_stability(self, new_signal: int, confidence: float) -> bool:
        """检查信号稳定性 - 关键优化"""
        if confidence < self.signal_threshold:
            return False

        # 添加到信号历史
        self.signal_history.append((new_signal, confidence, datetime.now()))

        # 需要足够的历史信号
        if len(self.signal_history) < self.signal_confirm_bars:
            return False

        # 检查信号一致性
        recent_signals = [s[0] for s in list(self.signal_history)[-self.signal_confirm_bars:]]
        consistent_signals = sum(1 for s in recent_signals if s == new_signal)
        consistency_ratio = consistent_signals / len(recent_signals)

        return consistency_ratio >= self.signal_consistency_ratio

    def _can_trade_enhanced(self) -> Tuple[bool, str]:
        """增强版交易检查"""
        # 基础检查
        if self.daily_trades >= self.daily_max_trades:
            return False, "达到日交易次数限制"

        if abs(self.daily_pnl) >= self.max_loss_pct / 100:
            return False, "达到日最大亏损限制"

        # 连续亏损检查
        if self.consecutive_losses >= self.max_consecutive_losses:
            return False, "连续亏损次数过多"

        # 最小持仓时间检查
        if self.entry_time and self.pos != 0:
            hold_time = datetime.now() - self.entry_time
            if hold_time.total_seconds() < self.min_hold_minutes * 60:
                return False, "未达到最小持仓时间"

        # 波动率过滤
        if self.volatility_filter and len(self.generated_bar_history) >= 20:
            recent_bars = self.generated_bar_history[-20:]
            volatility = np.std([bar['close'] for bar in recent_bars])
            avg_price = np.mean([bar['close'] for bar in recent_bars])
            vol_ratio = volatility / avg_price

            if vol_ratio > 0.05:  # 波动率过高
                return False, "市场波动率过高"

        return True, "可以交易"

    def _execute_optimized_trading(self, bar_dict: Dict):
        """执行优化版交易逻辑"""
        try:
            # 需要足够的历史数据
            if len(self.generated_bar_history) < max(30, self.min_bars_to_train):
                return

            # 提取特征进行预测
            recent_bars = self.generated_bar_history[-30:]
            current_features = self._extract_features_v100(recent_bars)

            if not current_features:
                return

            # 获取预测结果
            signal_value, signal_confidence = self.ml_system.predict(current_features)
            self.prediction_count += 1

            # 检查信号稳定性（关键优化）
            if not self._check_signal_stability(signal_value, signal_confidence):
                return

            # 更新信号变量
            self.signal_value = signal_value
            self.signal_confidence = signal_confidence

            # 增强版交易检查
            can_trade, reason = self._can_trade_enhanced()
            if not can_trade:
                return

            current_pos = self.pos
            current_price = bar_dict['close']

            # 开仓逻辑 - 更严格的条件
            if current_pos == 0:
                if signal_value == 1 and signal_confidence >= self.signal_threshold:
                    self.buy(current_price, self.position_size)
                    self.entry_price = current_price
                    self.entry_time = datetime.now()
                    self.position_direction = 1
                    self.daily_trades += 1
                    self.write_log("优化买入: " + "{:.2f}".format(current_price) +
                                 ", 置信度: " + "{:.3f}".format(signal_confidence))

                elif signal_value == -1 and signal_confidence >= self.signal_threshold:
                    self.sell(current_price, self.position_size)
                    self.entry_price = current_price
                    self.entry_time = datetime.now()
                    self.position_direction = -1
                    self.daily_trades += 1
                    self.write_log("优化卖出: " + "{:.2f}".format(current_price) +
                                 ", 置信度: " + "{:.3f}".format(signal_confidence))

            # 优化版平仓逻辑
            elif current_pos != 0:
                should_close, close_reason = self._should_close_position(current_price, signal_value, signal_confidence)

                if should_close:
                    if current_pos > 0:
                        self.sell(current_price, abs(current_pos))
                    else:
                        self.buy(current_price, abs(current_pos))

                    # 计算交易结果
                    pnl = self._calculate_trade_pnl(current_price)
                    self.trade_results.append(pnl)

                    if pnl > 0:
                        self.win_count += 1
                        self.consecutive_losses = 0
                    else:
                        self.consecutive_losses += 1

                    self.write_log("优化平仓: " + close_reason + ", 价格: " + "{:.2f}".format(current_price) +
                                 ", 盈亏: " + "{:.2f}".format(pnl))

                    self._reset_position()
                    self.daily_trades += 1

        except Exception as e:
            self.write_log("执行优化交易失败: " + str(e))

    def _should_close_position(self, current_price: float, signal_value: int, signal_confidence: float) -> Tuple[bool, str]:
        """优化版平仓判断"""
        if self.entry_price == 0:
            return False, ""

        # 计算盈亏百分比
        if self.position_direction == 1:
            pnl_pct = (current_price - self.entry_price) / self.entry_price * 100
        else:
            pnl_pct = (self.entry_price - current_price) / self.entry_price * 100

        # 止损止盈
        if pnl_pct <= -self.stop_loss_pct:
            return True, "止损"
        elif pnl_pct >= self.take_profit_pct:
            return True, "止盈"

        # 强信号反转（需要高置信度）
        if signal_confidence >= self.signal_threshold + 0.1:  # 更高的反转阈值
            if self.position_direction == 1 and signal_value == -1:
                return True, "强信号反转"
            elif self.position_direction == -1 and signal_value == 1:
                return True, "强信号反转"

        # 持仓时间过长（防止长期套牢）
        if self.entry_time:
            hold_time = datetime.now() - self.entry_time
            if hold_time.total_seconds() > 3600:  # 1小时
                if abs(pnl_pct) < 0.5:  # 微利或微亏
                    return True, "持仓时间过长"

        return False, ""

    def _calculate_trade_pnl(self, exit_price: float) -> float:
        """计算交易盈亏"""
        if self.entry_price == 0:
            return 0.0

        if self.position_direction == 1:
            return exit_price - self.entry_price
        else:
            return self.entry_price - exit_price

    def _reset_position(self):
        """重置持仓状态"""
        self.position_direction = 0
        self.entry_price = 0.0
        self.entry_time = None

    def _output_performance_stats(self):
        """输出性能统计"""
        try:
            self.write_log("=" * 50)
            self.write_log("优化版V100性能统计")
            self.write_log("=" * 50)

            # 基础统计
            self.write_log("Tick总数: " + str(self.tick_count))
            self.write_log("生成K线: " + str(self.generated_bars) + "根")
            self.write_log("交易次数: " + str(self.trade_count))
            self.write_log("连续亏损: " + str(self.consecutive_losses) + "次")

            # 交易统计
            if self.trade_results:
                win_rate = self.win_count / len(self.trade_results)
                avg_pnl = np.mean(self.trade_results)
                max_profit = max(self.trade_results)
                max_loss = min(self.trade_results)

                self.write_log("交易胜率: " + "{:.2%}".format(win_rate))
                self.write_log("平均盈亏: " + "{:.4f}".format(avg_pnl))
                self.write_log("最大盈利: " + "{:.4f}".format(max_profit))
                self.write_log("最大亏损: " + "{:.4f}".format(max_loss))

            # GPU统计
            if self.gpu_enabled:
                self.write_log("GPU计算: " + str(self.gpu_compute_count) + "次")
                self.write_log("CPU计算: " + str(self.cpu_compute_count) + "次")
                if self.gpu_speedup > 1:
                    self.write_log("V100加速比: " + "{:.2f}".format(self.gpu_speedup) + "倍")

            self.write_log("=" * 50)

        except Exception as e:
            self.write_log("输出性能统计失败: " + str(e))
