#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
真实机器学习策略 - 使用真实数据训练和实时学习
支持自动模型训练、在线学习和性能优化
"""

import os
import sys
import numpy as np
from datetime import datetime, timedelta
from typing import List, Tuple, Dict, Any

# 导入VNPY组件
from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    ArrayManager
)

# 导入真实ML系统
try:
    from .signal_system.real_ml_system import RealMLSignalSystem
    ML_SYSTEM_AVAILABLE = True
except ImportError:
    try:
        from signal_system.real_ml_system import RealMLSignalSystem
        ML_SYSTEM_AVAILABLE = True
    except ImportError:
        ML_SYSTEM_AVAILABLE = False


class RealMLStrategy(CtaTemplate):
    """
    真实机器学习策略
    
    特点：
    1. 使用真实历史数据训练模型
    2. 支持实时在线学习
    3. 自动特征工程
    4. 智能风险管理
    5. 性能监控和优化
    """
    
    author = "Real ML Strategy v1.0"
    
    # 策略参数
    signal_threshold = 0.65      # 信号置信度阈值
    position_size = 1            # 基础仓位大小
    stop_loss_pct = 2.0          # 止损百分比
    take_profit_pct = 4.0        # 止盈百分比
    max_position = 3             # 最大持仓手数
    
    # ML参数
    model_type = "lightgbm"      # 模型类型
    training_days = 30           # 训练数据天数
    retrain_interval = 100       # 重训练间隔
    online_learning = True       # 在线学习开关
    
    # 风险控制参数
    daily_max_trades = 10        # 日最大交易次数
    max_loss_pct = 5.0           # 最大亏损百分比
    
    # 变量
    signal_value = 0
    signal_confidence = 0.0
    entry_price = 0.0
    daily_trades = 0
    daily_pnl = 0.0
    model_accuracy = 0.0
    training_samples = 0
    prediction_count = 0
    
    # 参数和变量列表
    parameters = [
        "signal_threshold", "position_size", "stop_loss_pct", "take_profit_pct",
        "max_position", "model_type", "training_days", "retrain_interval",
        "online_learning", "daily_max_trades", "max_loss_pct"
    ]
    variables = [
        "signal_value", "signal_confidence", "entry_price", "daily_trades",
        "daily_pnl", "model_accuracy", "training_samples", "prediction_count"
    ]
    
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        """初始化策略"""
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        
        # 创建K线生成器和技术指标管理器
        self.bg = BarGenerator(self.on_bar, 1, self.on_1min_bar)
        self.am = ArrayManager(size=200)
        
        # 初始化ML系统
        self.ml_system = None
        self._init_ml_system()
        
        # 交易状态
        self.position_direction = 0  # 1=多头, -1=空头, 0=无仓位
        self.last_trade_time = None
        
        # 数据收集
        self.bar_history = []
        self.max_history_size = 1000
        
        # 性能统计
        self.trade_results = []
        self.total_pnl = 0.0
        self.win_count = 0
        self.trade_count = 0
        
        # 在线学习
        self.last_bar_close = 0.0
        self.learning_buffer = []
        
        self.write_log("真实ML策略初始化完成")

    def _extract_commodity_code(self, vt_symbol: str) -> str:
        """
        提取商品代码

        Args:
            vt_symbol: 如 "rb2510.SHFE"

        Returns:
            str: 商品代码，如 "rb"
        """
        try:
            # 移除交易所后缀
            symbol = vt_symbol.split('.')[0]  # rb2510.SHFE -> rb2510

            # 提取字母部分作为商品代码
            commodity_code = ""
            for char in symbol:
                if char.isalpha():
                    commodity_code += char.lower()
                else:
                    break

            # 如果没有提取到字母，使用完整symbol
            if not commodity_code:
                commodity_code = symbol.lower()

            # 安全的日志输出（只在策略运行时输出）
            if hasattr(self, 'cta_engine') and self.cta_engine:
                self.write_log("从 " + vt_symbol + " 提取商品代码: " + commodity_code)

            return commodity_code

        except Exception as e:
            # 安全的错误处理
            if hasattr(self, 'cta_engine') and self.cta_engine:
                self.write_log("提取商品代码失败: " + str(e))
            return vt_symbol.split('.')[0].lower() if '.' in vt_symbol else vt_symbol.lower()
    
    def _init_ml_system(self):
        """初始化ML系统"""
        if not ML_SYSTEM_AVAILABLE:
            self.write_log("错误: 真实ML系统不可用")
            return
        
        try:
            # 创建ML系统
            self.ml_system = RealMLSignalSystem(model_type=self.model_type)
            
            # 设置模型保存路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            model_dir = os.path.join(current_dir, "signal_system", "models")
            os.makedirs(model_dir, exist_ok=True)

            # 提取商品代码（如 rb2510.SHFE -> rb）
            commodity_code = self._extract_commodity_code(self.vt_symbol)
            self.model_path = os.path.join(model_dir, f"{commodity_code}_{self.model_type}_model.pkl")

            self.write_log("商品代码: " + commodity_code)
            self.write_log("模型路径: " + self.model_path)
            
            # 尝试加载已有模型
            if os.path.exists(self.model_path):
                if self.ml_system.load_model(self.model_path):
                    model_info = self.ml_system.get_model_info()
                    self.model_accuracy = model_info.get('latest_accuracy', 0.0)
                    self.training_samples = model_info.get('training_samples', 0)
                    self.write_log("成功加载已有模型")
                    self.write_log("模型准确率: " + "{:.3f}".format(self.model_accuracy))
                    self.write_log("训练样本数: " + str(self.training_samples))
                else:
                    self.write_log("加载模型失败，将重新训练")
            else:
                self.write_log("未找到已有模型，将使用历史数据训练")
            
        except Exception as e:
            self.write_log("初始化ML系统异常: " + str(e))
            self.ml_system = None
    
    def on_init(self):
        """策略初始化回调"""
        self.write_log("策略初始化")
        self.load_bar(self.training_days)  # 加载训练数据
    
    def on_start(self):
        """策略启动回调"""
        self.write_log("策略启动")

        # 检查ML系统状态
        if self.ml_system:
            if self.ml_system.is_trained:
                model_info = self.ml_system.get_model_info()
                self.write_log("使用已训练模型，准确率: " + "{:.3f}".format(model_info.get('latest_accuracy', 0.0)))
            else:
                self.write_log("模型未训练，等待历史数据加载完成后自动训练")
        else:
            self.write_log("ML系统不可用，策略将无法正常工作")
    
    def on_stop(self):
        """策略停止回调"""
        self.write_log("策略停止")
        
        # 保存模型
        if self.ml_system and self.ml_system.is_trained:
            self.ml_system.save_model(self.model_path)
            self.write_log("模型已保存")
        
        # 输出统计信息
        if self.trade_count > 0:
            win_rate = self.win_count / self.trade_count
            avg_pnl = self.total_pnl / self.trade_count
            self.write_log("交易统计:")
            self.write_log("总交易次数: " + str(self.trade_count))
            self.write_log("胜率: " + "{:.2%}".format(win_rate))
            self.write_log("平均盈亏: " + "{:.4f}".format(avg_pnl))
            self.write_log("模型准确率: " + "{:.3f}".format(self.model_accuracy))
    
    def on_tick(self, tick: TickData):
        """Tick数据回调"""
        self.bg.update_tick(tick)
    
    def on_1min_bar(self, bar: BarData):
        """1分钟K线回调"""
        # 更新技术指标
        self.am.update_bar(bar)
        if not self.am.inited:
            return

        # 收集历史数据
        self._collect_bar_data(bar)

        # 检查ML系统
        if not self.ml_system:
            self.write_log("ML系统不可用")
            return

        # 如果模型未训练且有足够数据，进行训练
        if not self.ml_system.is_trained:
            if len(self.bar_history) >= 200:
                self.write_log("历史数据充足(" + str(len(self.bar_history)) + "根K线)，开始训练模型")
                self._train_initial_model()
            elif len(self.bar_history) % 50 == 0:  # 每50根K线输出一次进度
                self.write_log("正在收集历史数据: " + str(len(self.bar_history)) + "/200")

        # 如果模型已训练，进行预测和交易
        if self.ml_system.is_trained:
            self._execute_ml_trading(bar)

        # 在线学习
        if self.online_learning and self.last_bar_close > 0:
            self._online_learning(bar)

        self.last_bar_close = bar.close_price

        # 更新界面
        self.put_event()
    
    def on_bar(self, bar: BarData):
        """K线数据回调（由bg生成）"""
        pass  # 实际处理在on_1min_bar中
    
    def _collect_bar_data(self, bar: BarData):
        """收集K线数据"""
        try:
            bar_dict = {
                'datetime': bar.datetime,
                'open': bar.open_price,
                'high': bar.high_price,
                'low': bar.low_price,
                'close': bar.close_price,
                'volume': bar.volume
            }
            
            self.bar_history.append(bar_dict)
            
            # 限制历史数据大小
            if len(self.bar_history) > self.max_history_size:
                self.bar_history.pop(0)
                
        except Exception as e:
            self.write_log("收集K线数据失败: " + str(e))
    
    def _train_initial_model(self):
        """训练初始模型"""
        try:
            self.write_log("=" * 50)
            self.write_log("开始训练初始模型...")
            self.write_log("历史数据: " + str(len(self.bar_history)) + " 根K线")

            # 提取特征
            self.write_log("步骤1: 提取特征...")
            features = self.ml_system.extract_features_from_bars(self.bar_history)
            self.write_log("提取特征完成: " + str(len(features)) + " 个特征向量")

            if len(features) < 100:
                self.write_log("特征数据不足(" + str(len(features)) + " < 100)，无法训练模型")
                return

            # 生成标签
            self.write_log("步骤2: 生成标签...")
            labels = self.ml_system.generate_labels_from_bars(self.bar_history)
            self.write_log("生成标签完成: " + str(len(labels)) + " 个标签")

            if len(labels) != len(features):
                self.write_log("特征和标签数量不匹配: " + str(len(features)) + " vs " + str(len(labels)))
                return

            # 显示标签分布
            buy_count = sum(1 for l in labels if l == 1)
            sell_count = sum(1 for l in labels if l == -1)
            hold_count = sum(1 for l in labels if l == 0)
            self.write_log("标签分布: 买入=" + str(buy_count) + ", 卖出=" + str(sell_count) + ", 持有=" + str(hold_count))

            # 训练模型
            self.write_log("步骤3: 训练" + self.model_type + "模型...")
            if self.ml_system.train_model(features, labels):
                model_info = self.ml_system.get_model_info()
                self.model_accuracy = model_info.get('latest_accuracy', 0.0)
                self.training_samples = model_info.get('training_samples', 0)

                self.write_log("🎉 模型训练成功!")
                self.write_log("训练样本数: " + str(self.training_samples))
                self.write_log("模型准确率: " + "{:.3f}".format(self.model_accuracy))

                # 保存模型
                self.write_log("步骤4: 保存模型...")
                if self.ml_system.save_model(self.model_path):
                    self.write_log("✅ 模型已保存到: " + self.model_path)
                else:
                    self.write_log("⚠️ 模型保存失败")

                self.write_log("🚀 模型训练完成，策略可以开始交易!")
            else:
                self.write_log("❌ 模型训练失败")

            self.write_log("=" * 50)

        except Exception as e:
            self.write_log("训练初始模型异常: " + str(e))
            import traceback
            self.write_log("详细错误: " + traceback.format_exc())
    
    def _execute_ml_trading(self, bar: BarData):
        """执行ML交易逻辑"""
        try:
            # 提取当前特征
            if len(self.bar_history) < 50:
                return
            
            # 从最近的K线数据提取特征
            recent_bars = self.bar_history[-50:]  # 使用最近50根K线
            features_list = self.ml_system.extract_features_from_bars(recent_bars)
            
            if not features_list:
                return
            
            # 使用最新的特征进行预测
            current_features = features_list[-1]
            self.signal_value, self.signal_confidence = self.ml_system.predict(current_features)
            self.prediction_count += 1
            
            # 检查交易条件
            if not self._can_trade():
                return
            
            # 检查信号强度
            if abs(self.signal_confidence) < self.signal_threshold:
                return
            
            current_pos = self.pos
            
            # 开仓逻辑
            if current_pos == 0:
                if self.signal_value == 1:  # 买入信号
                    self.buy(bar.close_price, self.position_size)
                    self.entry_price = bar.close_price
                    self.position_direction = 1
                    self.daily_trades += 1
                    self.write_log("ML买入信号: 价格=" + "{:.2f}".format(bar.close_price) + 
                                 ", 置信度=" + "{:.3f}".format(self.signal_confidence))
                    
                elif self.signal_value == -1:  # 卖出信号
                    self.sell(bar.close_price, self.position_size)
                    self.entry_price = bar.close_price
                    self.position_direction = -1
                    self.daily_trades += 1
                    self.write_log("ML卖出信号: 价格=" + "{:.2f}".format(bar.close_price) + 
                                 ", 置信度=" + "{:.3f}".format(self.signal_confidence))
            
            # 平仓逻辑（信号反转或止损止盈）
            elif current_pos != 0:
                should_close = False
                close_reason = ""
                
                # 信号反转
                if current_pos > 0 and self.signal_value == -1:
                    should_close = True
                    close_reason = "信号反转"
                elif current_pos < 0 and self.signal_value == 1:
                    should_close = True
                    close_reason = "信号反转"
                
                # 止损止盈
                if not should_close:
                    pnl_pct = self._calculate_pnl_pct(bar.close_price)
                    if pnl_pct <= -self.stop_loss_pct:
                        should_close = True
                        close_reason = "止损"
                    elif pnl_pct >= self.take_profit_pct:
                        should_close = True
                        close_reason = "止盈"
                
                if should_close:
                    if current_pos > 0:
                        self.sell(bar.close_price, abs(current_pos))
                    else:
                        self.buy(bar.close_price, abs(current_pos))
                    
                    self.write_log("平仓: " + close_reason + ", 价格=" + "{:.2f}".format(bar.close_price))
                    self._reset_position()
                    self.daily_trades += 1
                    
        except Exception as e:
            self.write_log("执行ML交易失败: " + str(e))
    
    def _online_learning(self, bar: BarData):
        """在线学习"""
        try:
            if self.last_bar_close <= 0:
                return
            
            # 计算实际收益率
            actual_return = (bar.close_price - self.last_bar_close) / self.last_bar_close
            
            # 提取上一根K线的特征
            if len(self.bar_history) >= 50:
                recent_bars = self.bar_history[-51:-1]  # 排除当前K线
                features_list = self.ml_system.extract_features_from_bars(recent_bars)
                
                if features_list:
                    last_features = features_list[-1]
                    
                    # 添加在线学习样本
                    self.ml_system.add_online_sample(last_features, actual_return)
                    
        except Exception as e:
            self.write_log("在线学习失败: " + str(e))
    
    def _can_trade(self) -> bool:
        """检查是否可以交易"""
        # 检查日交易次数
        if self.daily_trades >= self.daily_max_trades:
            return False
        
        # 检查日亏损限制
        if abs(self.daily_pnl) >= self.max_loss_pct / 100:
            return False
        
        return True
    
    def _calculate_pnl_pct(self, current_price: float) -> float:
        """计算盈亏百分比"""
        if self.entry_price == 0:
            return 0.0
        
        if self.position_direction == 1:  # 多头
            return (current_price - self.entry_price) / self.entry_price * 100
        else:  # 空头
            return (self.entry_price - current_price) / self.entry_price * 100
    
    def _reset_position(self):
        """重置持仓状态"""
        self.position_direction = 0
        self.entry_price = 0.0
    
    def on_order(self, order: OrderData):
        """订单回调"""
        pass
    
    def on_trade(self, trade: TradeData):
        """成交回调"""
        # 统计交易
        self.trade_count += 1
        
        # 计算盈亏
        if self.entry_price > 0:
            if trade.direction.value == "多":
                pnl = (trade.price - self.entry_price) / self.entry_price
            else:
                pnl = (self.entry_price - trade.price) / self.entry_price
            
            self.total_pnl += pnl
            self.trade_results.append(pnl)
            
            if pnl > 0:
                self.win_count += 1
        
        # 如果完全平仓，重置状态
        if self.pos == 0:
            self._reset_position()
        
        self.write_log("交易执行: " + str(trade.direction) + " " + str(trade.volume) + "手 @" + "{:.2f}".format(trade.price))
        
        # 更新模型信息
        if self.ml_system:
            model_info = self.ml_system.get_model_info()
            self.model_accuracy = model_info.get('latest_accuracy', 0.0)
            self.training_samples = model_info.get('training_samples', 0)
        
        self.put_event()
    
    def on_stop_order(self, stop_order: StopOrder):
        """停止单回调"""
        pass
