#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
安全版信号生成器策略 - 使用信号生成器作为唯一信号源
专为VNPY 4.1.0优化，避免日志格式化问题
"""

import os
import sys
import numpy as np
from datetime import datetime
from typing import List, Tuple

# 导入VNPY组件
from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    ArrayManager
)

# 导入信号系统API
try:
    from .signal_system import SignalSystemAPI
    SIGNAL_API_AVAILABLE = True
except ImportError:
    try:
        from signal_system import SignalSystemAPI
        SIGNAL_API_AVAILABLE = True
    except ImportError:
        SIGNAL_API_AVAILABLE = False


class SignalStrategySafe(CtaTemplate):
    """
    安全版信号生成器策略
    
    特点：
    1. 使用信号生成器作为唯一信号源
    2. 稳定的交易逻辑
    3. 完善的风险控制
    4. 避免日志格式化问题
    """
    
    author = "Signal System Safe v1.0"
    
    # 策略参数
    signal_threshold = 0.65      # 信号置信度阈值
    position_size = 1            # 基础仓位大小
    stop_loss_pct = 2.0          # 止损百分比
    take_profit_pct = 4.0        # 止盈百分比
    max_position = 3             # 最大持仓手数
    
    # 风险控制参数
    daily_max_trades = 10        # 日最大交易次数
    max_loss_pct = 5.0           # 最大亏损百分比
    
    # 变量
    signal_value = 0
    signal_confidence = 0.0
    entry_price = 0.0
    daily_trades = 0
    daily_pnl = 0.0
    last_signal_desc = ""
    
    # 参数和变量列表
    parameters = [
        "signal_threshold", "position_size", "stop_loss_pct", 
        "take_profit_pct", "max_position", "daily_max_trades", "max_loss_pct"
    ]
    variables = [
        "signal_value", "signal_confidence", "entry_price", 
        "daily_trades", "daily_pnl", "last_signal_desc"
    ]
    
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        """初始化策略"""
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        
        # 创建K线生成器和技术指标管理器
        self.bg = BarGenerator(self.on_bar, 1, self.on_1min_bar)
        self.am = ArrayManager(size=100)
        
        # 初始化信号系统
        self.signal_api = None
        self._init_signal_system()
        
        # 交易状态
        self.position_direction = 0  # 1=多头, -1=空头, 0=无仓位
        self.last_trade_time = None
        self.start_capital = 1000000  # 假设初始资金100万
        
        # 性能统计
        self.trade_count = 0
        self.win_count = 0
        self.total_pnl = 0.0
        
        self.write_log("安全版信号策略初始化完成")
    
    def _init_signal_system(self):
        """初始化信号系统"""
        if not SIGNAL_API_AVAILABLE:
            self.write_log("错误: 信号系统API不可用")
            return
        
        try:
            # 查找模型文件
            model_path = self._find_best_model()
            
            # 创建信号API（使用模拟模式确保稳定性）
            self.signal_api = SignalSystemAPI(
                model_path=model_path,
                use_mock=True  # 使用稳定的模拟模式
            )
            
            if self.signal_api.is_initialized():
                # 安全的日志输出，避免格式化问题
                self.write_log("信号系统初始化成功")
                try:
                    info = self.signal_api.get_system_info()
                    api_version = str(info.get('api_version', 'Unknown'))
                    system_type = str(info.get('system_type', 'Unknown'))
                    self.write_log("API版本: " + api_version)
                    self.write_log("系统类型: " + system_type)
                except Exception as e:
                    self.write_log("获取系统信息失败: " + str(e))
            else:
                self.write_log("信号系统初始化失败")
                
        except Exception as e:
            self.write_log("初始化信号系统异常: " + str(e))
            self.signal_api = None
    
    def _find_best_model(self) -> str:
        """查找最佳模型文件"""
        # 获取signal_system文件夹路径
        current_dir = os.path.dirname(os.path.abspath(__file__))
        signal_system_dir = os.path.join(current_dir, "signal_system")
        
        model_candidates = [
            os.path.join(signal_system_dir, "model.bin"),
            os.path.join(signal_system_dir, "best_model.pt"),
            os.path.join(signal_system_dir, "signal_model.bin")
        ]
        
        for candidate in model_candidates:
            if os.path.exists(candidate):
                self.write_log("找到模型文件: " + candidate)
                return candidate
        
        self.write_log("未找到模型文件，使用默认配置")
        return ""
    
    def on_init(self):
        """策略初始化回调"""
        self.write_log("策略初始化")
        self.load_bar(20)  # 加载20天历史数据
    
    def on_start(self):
        """策略启动回调"""
        self.write_log("策略启动")
        self.daily_trades = 0
        self.daily_pnl = 0.0
    
    def on_stop(self):
        """策略停止回调"""
        self.write_log("策略停止")
        
        # 输出统计信息
        if self.trade_count > 0:
            win_rate = self.win_count / self.trade_count
            avg_pnl = self.total_pnl / self.trade_count
            self.write_log("交易统计: 总交易" + str(self.trade_count) + "次")
            self.write_log("胜率: " + "{:.2%}".format(win_rate))
            self.write_log("平均盈亏: " + "{:.2f}".format(avg_pnl))
    
    def on_tick(self, tick: TickData):
        """Tick数据回调"""
        self.bg.update_tick(tick)
    
    def on_1min_bar(self, bar: BarData):
        """1分钟K线回调"""
        # 更新技术指标
        self.am.update_bar(bar)
        if not self.am.inited:
            return
        
        # 检查信号系统
        if not self.signal_api or not self.signal_api.is_initialized():
            return
        
        # 检查是否新的一天
        self._check_new_day(bar)
        
        # 提取特征并获取信号
        features = self._extract_features(bar)
        self.signal_value, self.signal_confidence = self.signal_api.predict(features)
        
        # 更新信号描述
        self.last_signal_desc = self._format_signal_desc()
        
        # 执行交易逻辑
        self._execute_trading(bar)
        
        # 风险控制
        self._risk_management(bar)
        
        # 更新界面
        self.put_event()
    
    def on_bar(self, bar: BarData):
        """K线数据回调（由bg生成）"""
        pass  # 实际处理在on_1min_bar中
    
    def _check_new_day(self, bar: BarData):
        """检查是否新的一天"""
        if self.last_trade_time is None:
            self.last_trade_time = bar.datetime
            return
        
        if bar.datetime.date() != self.last_trade_time.date():
            # 新的一天，重置计数器
            self.daily_trades = 0
            self.daily_pnl = 0.0
            self.write_log("新交易日开始: " + str(bar.datetime.date()))
        
        self.last_trade_time = bar.datetime
    
    def _extract_features(self, bar: BarData) -> List[float]:
        """提取交易特征"""
        try:
            features = []
            
            if self.am.inited:
                close = bar.close_price
                
                # 价格特征
                features.extend([
                    (close / bar.open_price - 1),           # 当前K线涨跌幅
                    (bar.high_price / bar.low_price - 1),   # 当前K线振幅
                    (close / self.am.sma(5) - 1),           # 相对5日均线
                    (close / self.am.sma(20) - 1),          # 相对20日均线
                ])
                
                # 动量特征
                if len(self.am.close_array) >= 10:
                    momentum_5 = close / self.am.close_array[-5] - 1
                    momentum_10 = close / self.am.close_array[-10] - 1
                    features.extend([momentum_5, momentum_10])
                else:
                    features.extend([0.0, 0.0])
                
                # 技术指标
                rsi = self.am.rsi(14)
                atr = self.am.atr(14)
                features.extend([
                    (rsi - 50) / 50,                        # 归一化RSI
                    atr / close if close > 0 else 0,        # 相对ATR
                ])
                
                # MACD和成交量
                try:
                    macd, signal, hist = self.am.macd(12, 26, 9)
                    features.append(hist)
                except:
                    features.append(0.0)
                
                # 成交量比率
                if len(self.am.volume_array) >= 5:
                    vol_ratio = bar.volume / np.mean(self.am.volume_array[-5:])
                    features.append(vol_ratio - 1)
                else:
                    features.append(0.0)
            
            # 确保特征数量一致
            while len(features) < 10:
                features.append(0.0)
            
            return features[:10]
            
        except Exception as e:
            self.write_log("提取特征失败: " + str(e))
            return [0.0] * 10
    
    def _format_signal_desc(self) -> str:
        """格式化信号描述"""
        if self.signal_value == 1:
            return "多头信号(置信度:" + "{:.3f}".format(self.signal_confidence) + ")"
        elif self.signal_value == -1:
            return "空头信号(置信度:" + "{:.3f}".format(self.signal_confidence) + ")"
        else:
            return "中性信号(置信度:" + "{:.3f}".format(self.signal_confidence) + ")"
    
    def _execute_trading(self, bar: BarData):
        """执行交易逻辑"""
        try:
            # 检查交易条件
            if not self._can_trade():
                return
            
            # 检查信号强度
            if abs(self.signal_confidence) < self.signal_threshold:
                return
            
            current_pos = self.pos
            
            # 开仓逻辑
            if current_pos == 0:
                if self.signal_value == 1:  # 开多
                    self.buy(bar.close_price, self.position_size)
                    self.entry_price = bar.close_price
                    self.position_direction = 1
                    self.daily_trades += 1
                    self.write_log("开多仓: " + "{:.2f}".format(bar.close_price) + ", 信号: " + self.last_signal_desc)
                    
                elif self.signal_value == -1:  # 开空
                    self.sell(bar.close_price, self.position_size)
                    self.entry_price = bar.close_price
                    self.position_direction = -1
                    self.daily_trades += 1
                    self.write_log("开空仓: " + "{:.2f}".format(bar.close_price) + ", 信号: " + self.last_signal_desc)
            
            # 平仓逻辑（信号反转）
            elif current_pos != 0:
                should_close = False
                
                if current_pos > 0 and self.signal_value == -1:
                    should_close = True
                elif current_pos < 0 and self.signal_value == 1:
                    should_close = True
                
                if should_close:
                    if current_pos > 0:
                        self.sell(bar.close_price, abs(current_pos))
                        self.write_log("平多仓: " + "{:.2f}".format(bar.close_price))
                    else:
                        self.buy(bar.close_price, abs(current_pos))
                        self.write_log("平空仓: " + "{:.2f}".format(bar.close_price))
                    
                    self._reset_position()
                    self.daily_trades += 1
                    
        except Exception as e:
            self.write_log("执行交易失败: " + str(e))
    
    def _can_trade(self) -> bool:
        """检查是否可以交易"""
        # 检查日交易次数
        if self.daily_trades >= self.daily_max_trades:
            return False
        
        # 检查日亏损限制
        if abs(self.daily_pnl) >= self.max_loss_pct / 100:
            return False
        
        return True
    
    def _risk_management(self, bar: BarData):
        """风险管理"""
        if self.pos == 0 or self.entry_price == 0:
            return
        
        current_price = bar.close_price
        
        # 计算盈亏比例
        if self.position_direction == 1:  # 多头
            pnl_pct = (current_price - self.entry_price) / self.entry_price * 100
        else:  # 空头
            pnl_pct = (self.entry_price - current_price) / self.entry_price * 100
        
        # 止损检查
        if pnl_pct <= -self.stop_loss_pct:
            if self.pos > 0:
                self.sell(current_price, abs(self.pos))
            else:
                self.buy(current_price, abs(self.pos))
            
            self.write_log("止损平仓: 亏损" + "{:.2f}".format(pnl_pct) + "%")
            self._reset_position()
            self.daily_trades += 1
        
        # 止盈检查
        elif pnl_pct >= self.take_profit_pct:
            if self.pos > 0:
                self.sell(current_price, abs(self.pos))
            else:
                self.buy(current_price, abs(self.pos))
            
            self.write_log("止盈平仓: 盈利" + "{:.2f}".format(pnl_pct) + "%")
            self._reset_position()
            self.daily_trades += 1
    
    def _reset_position(self):
        """重置持仓状态"""
        self.position_direction = 0
        self.entry_price = 0.0
    
    def on_order(self, order: OrderData):
        """订单回调"""
        pass
    
    def on_trade(self, trade: TradeData):
        """成交回调"""
        # 更新持仓（pos属性会自动更新，无需手动设置）
        # self.pos 是VNPY框架自动维护的属性
        
        # 统计交易
        self.trade_count += 1
        
        # 计算盈亏
        if self.entry_price > 0:
            if trade.direction.value == "多":
                pnl = (trade.price - self.entry_price) / self.entry_price
            else:
                pnl = (self.entry_price - trade.price) / self.entry_price
            
            self.total_pnl += pnl
            if pnl > 0:
                self.win_count += 1
        
        # 如果完全平仓，重置状态
        if self.pos == 0:
            self._reset_position()
        
        self.write_log("交易执行: " + str(trade.direction) + " " + str(trade.volume) + "手 @" + "{:.2f}".format(trade.price))
        self.put_event()
    
    def on_stop_order(self, stop_order: StopOrder):
        """停止单回调"""
        pass
