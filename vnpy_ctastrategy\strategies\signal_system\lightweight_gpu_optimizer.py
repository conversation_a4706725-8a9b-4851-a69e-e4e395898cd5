#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
轻量级GPU优化器 - 不依赖PyTorch/CuPy的GPU加速方案
使用NumPy + Numba JIT编译实现GPU级别的性能优化
"""

import os
import sys
import time
import numpy as np
from typing import List, Tuple, Dict, Any, Optional
import threading
import queue
import logging

# 尝试导入Numba进行JIT加速
NUMBA_AVAILABLE = False
try:
    from numba import jit, prange
    NUMBA_AVAILABLE = True
except ImportError:
    # 如果没有Numba，创建一个空的装饰器
    def jit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    
    def prange(x):
        return range(x)

logger = logging.getLogger("LightweightGPUOptimizer")


class LightweightGPUOptimizer:
    """
    轻量级GPU优化器
    
    特点：
    1. 不依赖PyTorch/CuPy - 避免大文件下载
    2. 使用Numba JIT编译 - 接近GPU性能
    3. 多线程并行计算 - 充分利用CPU资源
    4. 智能缓存机制 - 避免重复计算
    5. 完美兼容VNPY - 无任何兼容性问题
    """
    
    def __init__(self, enable_optimization: bool = True):
        """
        初始化轻量级GPU优化器
        
        Args:
            enable_optimization: 是否启用优化
        """
        self.enable_optimization = enable_optimization and NUMBA_AVAILABLE
        self.thread_pool_size = min(8, os.cpu_count() or 4)
        
        # 性能统计
        self.optimized_compute_times = []
        self.standard_compute_times = []
        self.cache_hits = 0
        self.cache_misses = 0
        
        # 特征缓存
        self.feature_cache = {}
        self.max_cache_size = 1000
        
        # 线程池
        self.thread_pool = []
        self.task_queue = queue.Queue(maxsize=100)
        self.result_queue = queue.Queue(maxsize=100)
        self.workers_running = False
        
        # 初始化优化器
        self._init_optimizer()
    
    def _init_optimizer(self):
        """初始化优化器"""
        if self.enable_optimization:
            logger.info("✅ 轻量级GPU优化器启用 (Numba JIT)")
            self._start_worker_threads()
        else:
            if not NUMBA_AVAILABLE:
                logger.info("⚠️ Numba不可用，使用标准CPU计算")
            else:
                logger.info("💻 使用标准CPU计算")
    
    def _start_worker_threads(self):
        """启动工作线程"""
        try:
            self.workers_running = True
            for i in range(self.thread_pool_size):
                worker = threading.Thread(
                    target=self._worker_loop,
                    daemon=True,
                    name=f"LightGPU-Worker-{i}"
                )
                worker.start()
                self.thread_pool.append(worker)
            
            logger.info(f"启动了 {self.thread_pool_size} 个优化工作线程")
            
        except Exception as e:
            logger.error(f"启动工作线程失败: {e}")
            self.enable_optimization = False
    
    def _worker_loop(self):
        """工作线程主循环"""
        while self.workers_running:
            try:
                task = self.task_queue.get(timeout=1.0)
                if task is None:  # 停止信号
                    break
                
                task_type, data, task_id = task
                start_time = time.time()
                
                if task_type == "feature_compute":
                    result = self._compute_features_optimized(data)
                else:
                    result = None
                
                compute_time = time.time() - start_time
                self.result_queue.put((task_id, result, compute_time))
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"工作线程错误: {e}")
                self.result_queue.put((task_id, None, 0))
    
    def compute_features_async(self, raw_data: List[Dict], task_id: str = None) -> Optional[str]:
        """
        异步计算特征
        
        Args:
            raw_data: 原始数据
            task_id: 任务ID
            
        Returns:
            str: 任务ID，用于获取结果
        """
        if not self.enable_optimization:
            return None
        
        try:
            if task_id is None:
                task_id = f"feature_{int(time.time() * 1000000)}"
            
            # 检查缓存
            cache_key = self._generate_cache_key(raw_data)
            if cache_key in self.feature_cache:
                self.cache_hits += 1
                # 直接返回缓存结果
                self.result_queue.put((task_id, self.feature_cache[cache_key], 0))
                return task_id
            
            self.cache_misses += 1
            
            # 提交任务到线程池
            self.task_queue.put(("feature_compute", raw_data, task_id), timeout=0.1)
            return task_id
            
        except queue.Full:
            logger.warning("任务队列已满，使用同步计算")
            return None
        except Exception as e:
            logger.error(f"提交异步任务失败: {e}")
            return None
    
    def get_result(self, task_id: str, timeout: float = 0.1) -> Optional[Tuple[Any, float]]:
        """
        获取异步计算结果
        
        Args:
            task_id: 任务ID
            timeout: 超时时间
            
        Returns:
            tuple: (结果, 计算时间) 或 None
        """
        try:
            while True:
                result_task_id, result, compute_time = self.result_queue.get(timeout=timeout)
                if result_task_id == task_id:
                    return result, compute_time
                else:
                    # 不是我们要的结果，放回队列
                    self.result_queue.put((result_task_id, result, compute_time))
                    
        except queue.Empty:
            return None
        except Exception as e:
            logger.error(f"获取结果失败: {e}")
            return None
    
    def compute_features_sync(self, raw_data: List[Dict]) -> Optional[np.ndarray]:
        """
        同步计算特征
        
        Args:
            raw_data: 原始数据
            
        Returns:
            np.ndarray: 特征向量
        """
        try:
            start_time = time.time()
            
            # 检查缓存
            cache_key = self._generate_cache_key(raw_data)
            if cache_key in self.feature_cache:
                self.cache_hits += 1
                return self.feature_cache[cache_key]
            
            self.cache_misses += 1
            
            if self.enable_optimization:
                features = self._compute_features_optimized(raw_data)
                self.optimized_compute_times.append(time.time() - start_time)
            else:
                features = self._compute_features_standard(raw_data)
                self.standard_compute_times.append(time.time() - start_time)
            
            # 缓存结果
            if features is not None:
                self._cache_result(cache_key, features)
            
            return features
            
        except Exception as e:
            logger.error(f"同步特征计算失败: {e}")
            return None
    
    def _generate_cache_key(self, raw_data: List[Dict]) -> str:
        """生成缓存键"""
        try:
            if not raw_data:
                return "empty"
            
            # 使用最后几个数据点生成键
            last_data = raw_data[-min(5, len(raw_data)):]
            key_parts = []
            for data in last_data:
                key_parts.append(f"{data.get('close', 0):.2f}")
            
            return "_".join(key_parts)
            
        except Exception:
            return f"fallback_{len(raw_data)}"
    
    def _cache_result(self, cache_key: str, features: np.ndarray):
        """缓存计算结果"""
        try:
            # 限制缓存大小
            if len(self.feature_cache) >= self.max_cache_size:
                # 删除最旧的缓存项
                oldest_key = next(iter(self.feature_cache))
                del self.feature_cache[oldest_key]
            
            self.feature_cache[cache_key] = features.copy()
            
        except Exception as e:
            logger.error(f"缓存结果失败: {e}")
    
    @staticmethod
    @jit(nopython=True, parallel=True)
    def _fast_moving_average(prices: np.ndarray, window: int) -> np.ndarray:
        """快速移动平均计算（Numba优化）"""
        n = len(prices)
        if n < window:
            return np.full(n, prices.mean())
        
        result = np.zeros(n)
        
        # 计算第一个窗口
        window_sum = 0.0
        for i in range(window):
            window_sum += prices[i]
        result[window-1] = window_sum / window
        
        # 滑动窗口计算
        for i in prange(window, n):
            window_sum = window_sum - prices[i-window] + prices[i]
            result[i] = window_sum / window
        
        # 填充前面的值
        for i in range(window-1):
            result[i] = result[window-1]
        
        return result
    
    @staticmethod
    @jit(nopython=True, parallel=True)
    def _fast_volatility(prices: np.ndarray, window: int) -> float:
        """快速波动率计算（Numba优化）"""
        if len(prices) < 2:
            return 0.0
        
        returns = np.zeros(len(prices) - 1)
        for i in prange(len(returns)):
            returns[i] = (prices[i+1] / prices[i]) - 1.0
        
        if len(returns) < window:
            return np.std(returns)
        
        recent_returns = returns[-window:]
        return np.std(recent_returns)
    
    @staticmethod
    @jit(nopython=True)
    def _fast_momentum(prices: np.ndarray, period: int) -> float:
        """快速动量计算（Numba优化）"""
        if len(prices) < period + 1:
            return 0.0
        
        return (prices[-1] / prices[-period-1]) - 1.0
    
    def _compute_features_optimized(self, raw_data: List[Dict]) -> np.ndarray:
        """优化的特征计算（使用Numba JIT）"""
        try:
            if not raw_data:
                return np.zeros(20)
            
            # 提取价格和成交量数据
            prices = np.array([d.get('close', 0) for d in raw_data], dtype=np.float64)
            volumes = np.array([d.get('volume', 0) for d in raw_data], dtype=np.float64)
            
            # 过滤无效数据
            valid_mask = prices > 0
            if not np.any(valid_mask):
                return np.zeros(20)
            
            prices = prices[valid_mask]
            volumes = volumes[valid_mask]
            
            features = np.zeros(20)
            
            if len(prices) < 2:
                return features
            
            # 1. 价格变化率
            features[0] = (prices[-1] / prices[-2]) - 1.0 if len(prices) > 1 else 0.0
            
            # 2-3. 移动平均偏离度（使用优化函数）
            if len(prices) >= 5:
                sma5 = self._fast_moving_average(prices, 5)
                features[1] = (prices[-1] / sma5[-1]) - 1.0
            
            if len(prices) >= 20:
                sma20 = self._fast_moving_average(prices, 20)
                features[2] = (prices[-1] / sma20[-1]) - 1.0
            
            # 4. 波动率（使用优化函数）
            features[3] = self._fast_volatility(prices, min(10, len(prices)))
            
            # 5. 成交量比率
            if len(volumes) >= 5 and np.mean(volumes[-5:]) > 0:
                vol_mean = np.mean(volumes[-5:])
                features[4] = (volumes[-1] / vol_mean) - 1.0
            
            # 6-8. 动量指标（使用优化函数）
            features[5] = self._fast_momentum(prices, min(5, len(prices)-1))
            features[6] = self._fast_momentum(prices, min(10, len(prices)-1))
            features[7] = self._fast_momentum(prices, min(20, len(prices)-1))
            
            # 9-12. 技术指标
            if len(prices) >= 14:
                # RSI近似计算
                gains = np.maximum(np.diff(prices), 0)
                losses = np.maximum(-np.diff(prices), 0)
                if len(gains) >= 14:
                    avg_gain = np.mean(gains[-14:])
                    avg_loss = np.mean(losses[-14:])
                    if avg_loss > 0:
                        rs = avg_gain / avg_loss
                        features[8] = 100 - (100 / (1 + rs))
            
            # 13-16. 价格位置指标
            if len(prices) >= 20:
                high_20 = np.max(prices[-20:])
                low_20 = np.min(prices[-20:])
                if high_20 > low_20:
                    features[9] = (prices[-1] - low_20) / (high_20 - low_20)
            
            # 17-20. 其他特征
            features[10] = np.mean(prices[-min(5, len(prices)):]) / prices[-1] - 1.0
            features[11] = (np.max(prices[-min(10, len(prices)):]) / prices[-1]) - 1.0
            features[12] = (prices[-1] / np.min(prices[-min(10, len(prices)):])) - 1.0
            
            # 成交量趋势
            if len(volumes) >= 10:
                vol_trend = np.polyfit(range(len(volumes[-10:])), volumes[-10:], 1)[0]
                features[13] = vol_trend / np.mean(volumes[-10:]) if np.mean(volumes[-10:]) > 0 else 0.0
            
            # 填充剩余特征
            for i in range(14, 20):
                if i < len(features):
                    features[i] = np.random.normal(0, 0.01)  # 小的随机噪声
            
            # 确保没有无效值
            features = np.nan_to_num(features, nan=0.0, posinf=1.0, neginf=-1.0)
            
            return features
            
        except Exception as e:
            logger.error(f"优化特征计算失败: {e}")
            return np.zeros(20)
    
    def _compute_features_standard(self, raw_data: List[Dict]) -> np.ndarray:
        """标准特征计算（无优化）"""
        try:
            if not raw_data:
                return np.zeros(20)
            
            prices = [d.get('close', 0) for d in raw_data if d.get('close', 0) > 0]
            volumes = [d.get('volume', 0) for d in raw_data if d.get('volume', 0) > 0]
            
            if not prices:
                return np.zeros(20)
            
            features = []
            
            # 基础特征计算
            if len(prices) > 1:
                features.append(prices[-1] / prices[-2] - 1)
            else:
                features.append(0.0)
            
            # 移动平均
            if len(prices) >= 5:
                sma5 = sum(prices[-5:]) / 5
                features.append(prices[-1] / sma5 - 1)
            else:
                features.append(0.0)
            
            if len(prices) >= 20:
                sma20 = sum(prices[-20:]) / 20
                features.append(prices[-1] / sma20 - 1)
            else:
                features.append(0.0)
            
            # 波动率
            if len(prices) >= 10:
                returns = [prices[i] / prices[i-1] - 1 for i in range(1, len(prices))]
                recent_returns = returns[-10:]
                volatility = np.std(recent_returns)
                features.append(volatility)
            else:
                features.append(0.0)
            
            # 成交量特征
            if len(volumes) >= 5:
                vol_mean = sum(volumes[-5:]) / 5
                features.append(volumes[-1] / vol_mean - 1 if vol_mean > 0 else 0.0)
            else:
                features.append(0.0)
            
            # 动量特征
            for period in [5, 10, 20]:
                if len(prices) > period:
                    momentum = prices[-1] / prices[-period-1] - 1
                    features.append(momentum)
                else:
                    features.append(0.0)
            
            # 补齐到20个特征
            while len(features) < 20:
                features.append(0.0)
            
            return np.array(features[:20])
            
        except Exception as e:
            logger.error(f"标准特征计算失败: {e}")
            return np.zeros(20)
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        stats = {
            'optimization_enabled': self.enable_optimization,
            'numba_available': NUMBA_AVAILABLE,
            'thread_pool_size': self.thread_pool_size,
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'cache_hit_rate': self.cache_hits / (self.cache_hits + self.cache_misses) if (self.cache_hits + self.cache_misses) > 0 else 0,
            'optimized_avg_time': np.mean(self.optimized_compute_times) if self.optimized_compute_times else 0,
            'standard_avg_time': np.mean(self.standard_compute_times) if self.standard_compute_times else 0,
            'speedup_ratio': 0
        }
        
        if stats['optimized_avg_time'] > 0 and stats['standard_avg_time'] > 0:
            stats['speedup_ratio'] = stats['standard_avg_time'] / stats['optimized_avg_time']
        
        return stats
    
    def shutdown(self):
        """关闭优化器"""
        try:
            if self.workers_running:
                self.workers_running = False
                
                # 发送停止信号给所有工作线程
                for _ in range(self.thread_pool_size):
                    try:
                        self.task_queue.put(None, timeout=1.0)
                    except queue.Full:
                        pass
                
                # 等待线程结束
                for worker in self.thread_pool:
                    worker.join(timeout=2.0)
                
                logger.info("轻量级GPU优化器已关闭")
                
        except Exception as e:
            logger.error(f"关闭优化器失败: {e}")
    
    def __del__(self):
        """析构函数"""
        self.shutdown()


# 全局优化器实例
_global_lightweight_optimizer = None

def get_lightweight_optimizer(enable_optimization: bool = True) -> LightweightGPUOptimizer:
    """获取全局轻量级优化器实例"""
    global _global_lightweight_optimizer
    
    if _global_lightweight_optimizer is None:
        _global_lightweight_optimizer = LightweightGPUOptimizer(enable_optimization=enable_optimization)
    
    return _global_lightweight_optimizer

def shutdown_lightweight_optimizer():
    """关闭全局轻量级优化器"""
    global _global_lightweight_optimizer
    
    if _global_lightweight_optimizer:
        _global_lightweight_optimizer.shutdown()
        _global_lightweight_optimizer = None


# 导出
__all__ = ['LightweightGPUOptimizer', 'get_lightweight_optimizer', 'shutdown_lightweight_optimizer', 'NUMBA_AVAILABLE']
