#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
真实信号系统 - 基于深度学习的实时信号生成器
支持模型训练、实时学习和在线更新
"""

import os
import sys
import pickle
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Tuple, Dict, Any, Optional
import logging

# 机器学习库
try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False

try:
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.preprocessing import StandardScaler
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import accuracy_score, classification_report
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False

try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    PYTORCH_AVAILABLE = True
except ImportError:
    PYTORCH_AVAILABLE = False

logger = logging.getLogger("RealSignalSystem")


class RealSignalSystem:
    """
    真实信号系统
    
    特点：
    1. 支持多种机器学习模型
    2. 实时在线学习
    3. 模型持久化
    4. 特征工程
    5. 性能监控
    """
    
    def __init__(self, model_type: str = "lightgbm", model_path: str = ""):
        """
        初始化真实信号系统
        
        Args:
            model_type: 模型类型 ("lightgbm", "random_forest", "neural_network")
            model_path: 模型保存路径
        """
        self.model_type = model_type
        self.model_path = model_path
        self.model = None
        self.scaler = StandardScaler() if SKLEARN_AVAILABLE else None
        self.is_trained = False
        
        # 数据缓存
        self.feature_buffer = []
        self.label_buffer = []
        self.max_buffer_size = 10000
        
        # 在线学习参数
        self.online_learning_enabled = True
        self.retrain_interval = 100  # 每100个样本重训练一次
        self.sample_count = 0
        
        # 性能监控
        self.prediction_history = []
        self.accuracy_history = []
        
        # 初始化模型
        self._init_model()
        
        # 尝试加载已有模型
        if model_path and os.path.exists(model_path):
            self.load_model(model_path)
    
    def _init_model(self):
        """初始化模型"""
        try:
            if self.model_type == "lightgbm" and LIGHTGBM_AVAILABLE:
                self.model = lgb.LGBMClassifier(
                    n_estimators=100,
                    learning_rate=0.1,
                    max_depth=6,
                    random_state=42,
                    verbose=-1
                )
                logger.info("初始化LightGBM模型")
                
            elif self.model_type == "random_forest" and SKLEARN_AVAILABLE:
                self.model = RandomForestClassifier(
                    n_estimators=100,
                    max_depth=10,
                    random_state=42
                )
                logger.info("初始化随机森林模型")
                
            elif self.model_type == "neural_network" and PYTORCH_AVAILABLE:
                self.model = SimpleNeuralNetwork()
                logger.info("初始化神经网络模型")
                
            else:
                logger.warning(f"模型类型 {self.model_type} 不可用，使用默认模型")
                if SKLEARN_AVAILABLE:
                    self.model = RandomForestClassifier(n_estimators=50, random_state=42)
                    self.model_type = "random_forest"
                else:
                    raise ImportError("没有可用的机器学习库")
                    
        except Exception as e:
            logger.error(f"初始化模型失败: {e}")
            self.model = None
    
    def predict(self, features: List[float]) -> Tuple[int, float]:
        """
        预测交易信号
        
        Args:
            features: 特征向量
            
        Returns:
            tuple: (信号值, 置信度)
        """
        try:
            if not self.model or not self.is_trained:
                return self._fallback_prediction(features)
            
            # 预处理特征
            features_array = np.array(features).reshape(1, -1)
            
            if self.scaler:
                features_array = self.scaler.transform(features_array)
            
            # 预测
            if self.model_type == "neural_network":
                prediction, confidence = self._predict_neural_network(features_array)
            else:
                prediction = self.model.predict(features_array)[0]
                
                # 获取置信度
                if hasattr(self.model, 'predict_proba'):
                    proba = self.model.predict_proba(features_array)[0]
                    confidence = max(proba)
                else:
                    confidence = 0.7  # 默认置信度
            
            # 转换预测结果
            if prediction == 0:
                signal = -1  # 卖出
            elif prediction == 1:
                signal = 0   # 持有
            else:
                signal = 1   # 买入
            
            # 记录预测历史
            self.prediction_history.append({
                'timestamp': datetime.now(),
                'features': features,
                'signal': signal,
                'confidence': confidence
            })
            
            return signal, confidence
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return self._fallback_prediction(features)
    
    def _predict_neural_network(self, features_array: np.ndarray) -> Tuple[int, float]:
        """神经网络预测"""
        if not PYTORCH_AVAILABLE:
            return 0, 0.5
        
        try:
            self.model.eval()
            with torch.no_grad():
                features_tensor = torch.FloatTensor(features_array)
                outputs = self.model(features_tensor)
                probabilities = torch.softmax(outputs, dim=1)
                
                predicted_class = torch.argmax(probabilities, dim=1).item()
                confidence = torch.max(probabilities).item()
                
                return predicted_class, confidence
                
        except Exception as e:
            logger.error(f"神经网络预测失败: {e}")
            return 0, 0.5
    
    def _fallback_prediction(self, features: List[float]) -> Tuple[int, float]:
        """备用预测方法"""
        if len(features) < 4:
            return 0, 0.5
        
        # 简单的技术分析逻辑
        price_change = features[0]
        momentum = features[4] if len(features) > 4 else 0
        
        if price_change > 0.01 and momentum > 0:
            return 1, 0.6
        elif price_change < -0.01 and momentum < 0:
            return -1, 0.6
        else:
            return 0, 0.5
    
    def add_training_sample(self, features: List[float], label: int, confidence: float = 1.0):
        """
        添加训练样本
        
        Args:
            features: 特征向量
            label: 标签 (-1: 卖出, 0: 持有, 1: 买入)
            confidence: 样本置信度
        """
        try:
            # 转换标签格式
            if label == -1:
                label_encoded = 0  # 卖出
            elif label == 0:
                label_encoded = 1  # 持有
            else:
                label_encoded = 2  # 买入
            
            # 添加到缓存
            self.feature_buffer.append(features)
            self.label_buffer.append(label_encoded)
            
            # 限制缓存大小
            if len(self.feature_buffer) > self.max_buffer_size:
                self.feature_buffer.pop(0)
                self.label_buffer.pop(0)
            
            self.sample_count += 1
            
            # 检查是否需要重训练
            if (self.online_learning_enabled and 
                self.sample_count % self.retrain_interval == 0 and 
                len(self.feature_buffer) >= 50):
                
                self._retrain_model()
                
        except Exception as e:
            logger.error(f"添加训练样本失败: {e}")
    
    def train_initial_model(self, features_data: List[List[float]], labels: List[int]):
        """
        训练初始模型
        
        Args:
            features_data: 特征数据
            labels: 标签数据
        """
        try:
            if not features_data or not labels:
                logger.warning("训练数据为空")
                return False
            
            # 转换数据格式
            X = np.array(features_data)
            y = np.array(labels)
            
            # 转换标签
            y_encoded = np.where(y == -1, 0, np.where(y == 0, 1, 2))
            
            # 数据预处理
            if self.scaler:
                X = self.scaler.fit_transform(X)
            
            # 分割训练和测试数据
            if len(X) > 10:
                X_train, X_test, y_train, y_test = train_test_split(
                    X, y_encoded, test_size=0.2, random_state=42
                )
            else:
                X_train, X_test = X, X
                y_train, y_test = y_encoded, y_encoded
            
            # 训练模型
            if self.model_type == "neural_network":
                success = self._train_neural_network(X_train, y_train, X_test, y_test)
            else:
                self.model.fit(X_train, y_train)
                success = True
            
            if success:
                self.is_trained = True
                
                # 评估模型
                if len(X_test) > 0:
                    accuracy = self._evaluate_model(X_test, y_test)
                    logger.info(f"模型训练完成，准确率: {accuracy:.3f}")
                
                return True
            else:
                logger.error("模型训练失败")
                return False
                
        except Exception as e:
            logger.error(f"训练初始模型失败: {e}")
            return False
    
    def _train_neural_network(self, X_train, y_train, X_test, y_test) -> bool:
        """训练神经网络"""
        if not PYTORCH_AVAILABLE:
            return False
        
        try:
            # 转换为PyTorch张量
            X_train_tensor = torch.FloatTensor(X_train)
            y_train_tensor = torch.LongTensor(y_train)
            
            # 定义损失函数和优化器
            criterion = nn.CrossEntropyLoss()
            optimizer = optim.Adam(self.model.parameters(), lr=0.001)
            
            # 训练
            self.model.train()
            for epoch in range(100):
                optimizer.zero_grad()
                outputs = self.model(X_train_tensor)
                loss = criterion(outputs, y_train_tensor)
                loss.backward()
                optimizer.step()
                
                if epoch % 20 == 0:
                    logger.info(f"Epoch {epoch}, Loss: {loss.item():.4f}")
            
            return True
            
        except Exception as e:
            logger.error(f"神经网络训练失败: {e}")
            return False
    
    def _retrain_model(self):
        """重训练模型"""
        try:
            if len(self.feature_buffer) < 20:
                return
            
            logger.info("开始在线重训练模型")
            
            # 使用最近的数据重训练
            recent_features = self.feature_buffer[-200:] if len(self.feature_buffer) > 200 else self.feature_buffer
            recent_labels = self.label_buffer[-200:] if len(self.label_buffer) > 200 else self.label_buffer
            
            X = np.array(recent_features)
            y = np.array(recent_labels)
            
            if self.scaler:
                X = self.scaler.transform(X)
            
            # 重训练
            if self.model_type == "neural_network":
                self._train_neural_network(X, y, X, y)
            else:
                self.model.fit(X, y)
            
            # 评估性能
            accuracy = self._evaluate_model(X, y)
            self.accuracy_history.append(accuracy)
            
            logger.info(f"在线重训练完成，准确率: {accuracy:.3f}")
            
        except Exception as e:
            logger.error(f"在线重训练失败: {e}")
    
    def _evaluate_model(self, X_test, y_test) -> float:
        """评估模型性能"""
        try:
            if self.model_type == "neural_network":
                self.model.eval()
                with torch.no_grad():
                    X_tensor = torch.FloatTensor(X_test)
                    outputs = self.model(X_tensor)
                    _, predicted = torch.max(outputs.data, 1)
                    accuracy = (predicted == torch.LongTensor(y_test)).float().mean().item()
            else:
                y_pred = self.model.predict(X_test)
                accuracy = accuracy_score(y_test, y_pred)
            
            return accuracy
            
        except Exception as e:
            logger.error(f"模型评估失败: {e}")
            return 0.0
    
    def save_model(self, path: str) -> bool:
        """保存模型"""
        try:
            model_data = {
                'model': self.model,
                'scaler': self.scaler,
                'model_type': self.model_type,
                'is_trained': self.is_trained,
                'feature_buffer': self.feature_buffer[-1000:],  # 保存最近1000个样本
                'label_buffer': self.label_buffer[-1000:],
                'accuracy_history': self.accuracy_history
            }
            
            with open(path, 'wb') as f:
                pickle.dump(model_data, f)
            
            logger.info(f"模型已保存到: {path}")
            return True
            
        except Exception as e:
            logger.error(f"保存模型失败: {e}")
            return False
    
    def load_model(self, path: str) -> bool:
        """加载模型"""
        try:
            with open(path, 'rb') as f:
                model_data = pickle.load(f)
            
            self.model = model_data['model']
            self.scaler = model_data['scaler']
            self.model_type = model_data['model_type']
            self.is_trained = model_data['is_trained']
            self.feature_buffer = model_data.get('feature_buffer', [])
            self.label_buffer = model_data.get('label_buffer', [])
            self.accuracy_history = model_data.get('accuracy_history', [])
            
            logger.info(f"模型已从 {path} 加载")
            return True
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            'model_type': self.model_type,
            'is_trained': self.is_trained,
            'sample_count': self.sample_count,
            'buffer_size': len(self.feature_buffer),
            'online_learning': self.online_learning_enabled,
            'latest_accuracy': self.accuracy_history[-1] if self.accuracy_history else 0.0,
            'prediction_count': len(self.prediction_history)
        }


class SimpleNeuralNetwork(nn.Module):
    """简单的神经网络模型"""
    
    def __init__(self, input_size: int = 20, hidden_size: int = 64, num_classes: int = 3):
        super(SimpleNeuralNetwork, self).__init__()
        self.fc1 = nn.Linear(input_size, hidden_size)
        self.fc2 = nn.Linear(hidden_size, hidden_size)
        self.fc3 = nn.Linear(hidden_size, num_classes)
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(0.2)
    
    def forward(self, x):
        x = self.relu(self.fc1(x))
        x = self.dropout(x)
        x = self.relu(self.fc2(x))
        x = self.dropout(x)
        x = self.fc3(x)
        return x


# 导出
__all__ = ['RealSignalSystem', 'SimpleNeuralNetwork']
