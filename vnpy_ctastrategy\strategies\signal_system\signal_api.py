#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
信号系统API - 专为VNPY 4.1.0设计
简化版本，确保稳定性和兼容性
"""

import os
import sys
import time
import logging
import numpy as np
from typing import List, Dict, Optional, Tuple, Any
from datetime import datetime
import traceback

# 简化日志配置，避免与VNPY冲突
logger = logging.getLogger("SignalAPI")
logger.setLevel(logging.INFO)


class SignalSystemAPI:
    """
    信号系统API
    
    特点：
    1. 简化的架构，确保稳定性
    2. 兼容VNPY 4.1.0
    3. 支持多种信号生成模式
    4. 自动降级机制
    """
    
    def __init__(self, model_path: str = "", use_mock: bool = False):
        """
        初始化信号系统API
        
        Args:
            model_path: 模型文件路径
            use_mock: 是否使用模拟模式
        """
        self.model_path = model_path
        self.use_mock = use_mock
        self.is_ready = False
        self.signal_system = None
        self.last_prediction = {"signal": 0, "confidence": 0.0}
        self.feature_cache = []
        self.max_cache_size = 100
        
        # 初始化系统
        self._initialize()
    
    def _initialize(self):
        """初始化信号系统"""
        try:
            logger.info("初始化信号系统API")
            
            if self.use_mock:
                self._init_mock_system()
            else:
                # 尝试初始化真实系统
                if not self._init_real_system():
                    logger.warning("真实系统初始化失败，切换到模拟模式")
                    self._init_mock_system()
            
            self.is_ready = True
            logger.info("信号系统API初始化完成")
            
        except Exception as e:
            logger.error(f"初始化失败: {e}")
            self._init_mock_system()
            self.is_ready = True
    
    def _init_real_system(self) -> bool:
        """初始化真实信号系统"""
        try:
            # 尝试导入并初始化真实的信号系统
            current_dir = os.path.dirname(os.path.abspath(__file__))
            
            # 检查DLL文件
            dll_path = os.path.join(current_dir, "signal_system.dll")
            if os.path.exists(dll_path):
                logger.info(f"找到DLL文件: {dll_path}")
                # 这里可以添加DLL加载逻辑
                # 由于当前DLL有兼容性问题，暂时跳过
                return False
            
            # 检查PYD文件
            pyd_path = os.path.join(current_dir, "signal_system.pyd")
            if os.path.exists(pyd_path):
                logger.info(f"找到PYD文件: {pyd_path}")
                # 这里可以添加PYD加载逻辑
                # 由于当前PYD有兼容性问题，暂时跳过
                return False
            
            return False
            
        except Exception as e:
            logger.error(f"初始化真实系统失败: {e}")
            return False
    
    def _init_mock_system(self):
        """初始化模拟系统"""
        try:
            self.signal_system = MockSignalSystem()
            logger.info("模拟信号系统初始化成功")
        except Exception as e:
            logger.error(f"初始化模拟系统失败: {e}")
            self.signal_system = MockSignalSystem()
    
    def is_initialized(self) -> bool:
        """检查系统是否已初始化"""
        return self.is_ready and self.signal_system is not None
    
    def predict(self, features: List[float]) -> Tuple[int, float]:
        """
        预测交易信号
        
        Args:
            features: 特征向量
            
        Returns:
            tuple: (信号值, 置信度)
        """
        try:
            if not self.is_initialized():
                return 0, 0.0
            
            # 缓存特征
            self.feature_cache.append(features)
            if len(self.feature_cache) > self.max_cache_size:
                self.feature_cache.pop(0)
            
            # 获取预测
            signal, confidence = self.signal_system.predict(features)
            
            # 更新最后预测
            self.last_prediction = {"signal": signal, "confidence": confidence}
            
            return signal, confidence
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return 0, 0.0
    
    def reset(self) -> bool:
        """重置系统状态"""
        try:
            self.feature_cache = []
            self.last_prediction = {"signal": 0, "confidence": 0.0}
            
            if hasattr(self.signal_system, 'reset'):
                self.signal_system.reset()
            
            return True
        except Exception as e:
            logger.error(f"重置失败: {e}")
            return False
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        return {
            "api_version": "1.0.0",
            "is_initialized": self.is_initialized(),
            "use_mock": self.use_mock,
            "model_path": self.model_path,
            "feature_cache_size": len(self.feature_cache),
            "last_prediction": self.last_prediction,
            "system_type": type(self.signal_system).__name__ if self.signal_system else "None"
        }
    
    def load_model(self, model_path: str) -> bool:
        """加载新模型"""
        try:
            if not os.path.exists(model_path):
                logger.error(f"模型文件不存在: {model_path}")
                return False
            
            self.model_path = model_path
            logger.info(f"模型路径已更新: {model_path}")
            return True
            
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            return False


class MockSignalSystem:
    """模拟信号系统"""
    
    def __init__(self):
        self.version = "1.0.0-mock"
        self.data_history = []
        self.signal_state = {
            'trend': 0.0,
            'momentum': 0.0,
            'volatility': 0.02,
            'last_signal': 0,
            'signal_duration': 0
        }
    
    def predict(self, features: List[float]) -> Tuple[int, float]:
        """预测信号"""
        try:
            if not features or len(features) == 0:
                return 0, 0.5
            
            # 更新内部状态
            self._update_state(features)
            
            # 生成信号
            signal, confidence = self._generate_signal(features)
            
            return signal, confidence
            
        except Exception as e:
            logger.error(f"模拟预测失败: {e}")
            return 0, 0.5
    
    def _update_state(self, features: List[float]):
        """更新内部状态"""
        try:
            # 使用特征更新趋势和动量
            if len(features) >= 4:
                price_change = features[0]  # 价格变化
                volume_change = features[1] if len(features) > 1 else 0.0
                
                # 更新趋势（指数平滑）
                alpha = 0.3
                self.signal_state['trend'] = alpha * price_change + (1 - alpha) * self.signal_state['trend']
                
                # 更新动量
                if len(features) >= 5:
                    momentum = features[4]
                    self.signal_state['momentum'] = alpha * momentum + (1 - alpha) * self.signal_state['momentum']
                
                # 更新波动率
                if len(self.data_history) > 5:
                    recent_changes = [abs(f[0]) for f in self.data_history[-5:] if len(f) > 0]
                    if recent_changes:
                        self.signal_state['volatility'] = np.std(recent_changes)
            
            # 记录数据
            self.data_history.append(features)
            if len(self.data_history) > 50:
                self.data_history.pop(0)
                
        except Exception as e:
            logger.error(f"更新状态失败: {e}")
    
    def _generate_signal(self, features: List[float]) -> Tuple[int, float]:
        """生成交易信号"""
        try:
            trend = self.signal_state['trend']
            momentum = self.signal_state['momentum']
            volatility = self.signal_state['volatility']
            
            # 基于趋势和动量生成信号
            if trend > 0.005 and momentum > 0:
                # 上升趋势
                signal = 1
                confidence = min(0.9, 0.6 + abs(trend) * 20 + abs(momentum) * 10)
            elif trend < -0.005 and momentum < 0:
                # 下降趋势
                signal = -1
                confidence = min(0.9, 0.6 + abs(trend) * 20 + abs(momentum) * 10)
            else:
                # 震荡或不明确
                signal = 0
                confidence = 0.5
            
            # 根据波动率调整置信度
            if volatility > 0.05:
                confidence *= 0.8  # 高波动率降低置信度
            elif volatility < 0.01:
                confidence *= 1.1  # 低波动率提高置信度
            
            # 限制置信度范围
            confidence = max(0.1, min(0.95, confidence))
            
            # 更新信号持续时间
            if signal == self.signal_state['last_signal']:
                self.signal_state['signal_duration'] += 1
            else:
                self.signal_state['signal_duration'] = 1
                self.signal_state['last_signal'] = signal
            
            # 如果信号持续时间过长，降低置信度
            if self.signal_state['signal_duration'] > 10:
                confidence *= 0.9
            
            return signal, confidence
            
        except Exception as e:
            logger.error(f"生成信号失败: {e}")
            return 0, 0.5
    
    def reset(self):
        """重置系统"""
        self.data_history = []
        self.signal_state = {
            'trend': 0.0,
            'momentum': 0.0,
            'volatility': 0.02,
            'last_signal': 0,
            'signal_duration': 0
        }


# 导出
__all__ = ['SignalSystemAPI', 'MockSignalSystem']
