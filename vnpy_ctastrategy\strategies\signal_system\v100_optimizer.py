#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
V100 GPU优化器 - 为VNPY策略提供安全的GPU加速
设计原则：安全第一，性能第二，完美兼容VNPY
"""

import os
import sys
import threading
import queue
import time
import numpy as np
from typing import List, Tuple, Dict, Any, Optional
import logging

# 安全的GPU库导入
GPU_AVAILABLE = False
TORCH_AVAILABLE = False
CUPY_AVAILABLE = False

try:
    import torch
    if torch.cuda.is_available():
        TORCH_AVAILABLE = True
        GPU_AVAILABLE = True
except ImportError:
    pass

try:
    import cupy as cp
    CUPY_AVAILABLE = True
    GPU_AVAILABLE = True
except ImportError:
    pass

logger = logging.getLogger("V100Optimizer")


class V100SafeOptimizer:
    """
    V100安全优化器
    
    特点：
    1. 完美兼容VNPY - 不影响策略主线程
    2. 优雅降级 - GPU不可用时自动使用CPU
    3. 异步处理 - GPU计算在后台进行
    4. 错误隔离 - GPU错误不影响策略运行
    5. 性能监控 - 实时监控GPU使用情况
    """
    
    def __init__(self, enable_gpu: bool = True):
        """
        初始化V100优化器
        
        Args:
            enable_gpu: 是否启用GPU优化
        """
        self.enable_gpu = enable_gpu and GPU_AVAILABLE
        self.gpu_device = None
        self.gpu_context = None
        
        # 异步处理队列
        self.feature_queue = queue.Queue(maxsize=100)
        self.result_queue = queue.Queue(maxsize=100)
        self.gpu_worker_thread = None
        self.gpu_worker_running = False
        
        # 性能统计
        self.gpu_compute_times = []
        self.cpu_compute_times = []
        self.gpu_error_count = 0
        self.total_computations = 0
        
        # 初始化GPU环境
        self._init_gpu_environment()
        
        # 启动GPU工作线程
        if self.enable_gpu:
            self._start_gpu_worker()
    
    def _init_gpu_environment(self):
        """安全初始化GPU环境"""
        if not self.enable_gpu:
            logger.info("GPU优化已禁用")
            return
        
        try:
            if TORCH_AVAILABLE:
                # 检查CUDA可用性
                if torch.cuda.is_available():
                    self.gpu_device = torch.device('cuda:0')
                    
                    # 测试GPU
                    test_tensor = torch.randn(100, 100).to(self.gpu_device)
                    result = torch.matmul(test_tensor, test_tensor)
                    del test_tensor, result
                    torch.cuda.empty_cache()
                    
                    logger.info(f"PyTorch GPU初始化成功: {torch.cuda.get_device_name(0)}")
                    return
                    
            if CUPY_AVAILABLE:
                # 测试CuPy
                test_array = cp.random.randn(100, 100)
                result = cp.dot(test_array, test_array)
                del test_array, result
                cp.get_default_memory_pool().free_all_blocks()
                
                logger.info("CuPy GPU初始化成功")
                return
                
        except Exception as e:
            logger.warning(f"GPU初始化失败，将使用CPU: {e}")
            self.enable_gpu = False
            self.gpu_device = None
    
    def _start_gpu_worker(self):
        """启动GPU工作线程"""
        if not self.enable_gpu:
            return
        
        try:
            self.gpu_worker_running = True
            self.gpu_worker_thread = threading.Thread(
                target=self._gpu_worker_loop,
                daemon=True,
                name="V100Worker"
            )
            self.gpu_worker_thread.start()
            logger.info("GPU工作线程启动成功")
            
        except Exception as e:
            logger.error(f"GPU工作线程启动失败: {e}")
            self.enable_gpu = False
    
    def _gpu_worker_loop(self):
        """GPU工作线程主循环"""
        while self.gpu_worker_running:
            try:
                # 从队列获取任务
                task = self.feature_queue.get(timeout=1.0)
                if task is None:  # 停止信号
                    break
                
                task_type, data, task_id = task
                
                # 执行GPU计算
                start_time = time.time()
                
                if task_type == "feature_compute":
                    result = self._gpu_compute_features(data)
                elif task_type == "model_train":
                    result = self._gpu_train_model(data)
                elif task_type == "model_predict":
                    result = self._gpu_predict(data)
                else:
                    result = None
                
                compute_time = time.time() - start_time
                self.gpu_compute_times.append(compute_time)
                
                # 返回结果
                self.result_queue.put((task_id, result, compute_time))
                
            except queue.Empty:
                continue
            except Exception as e:
                self.gpu_error_count += 1
                logger.error(f"GPU计算错误: {e}")
                # 返回错误结果
                self.result_queue.put((task_id, None, 0))
    
    def compute_features_async(self, raw_data: List[Dict], task_id: str = None) -> Optional[str]:
        """
        异步计算特征
        
        Args:
            raw_data: 原始数据
            task_id: 任务ID
            
        Returns:
            str: 任务ID，用于获取结果
        """
        if not self.enable_gpu:
            return None
        
        try:
            if task_id is None:
                task_id = f"feature_{int(time.time() * 1000000)}"
            
            # 提交任务到GPU队列
            self.feature_queue.put(("feature_compute", raw_data, task_id), timeout=0.1)
            return task_id
            
        except queue.Full:
            logger.warning("GPU队列已满，使用CPU计算")
            return None
        except Exception as e:
            logger.error(f"提交GPU任务失败: {e}")
            return None
    
    def get_result(self, task_id: str, timeout: float = 0.1) -> Optional[Tuple[Any, float]]:
        """
        获取异步计算结果
        
        Args:
            task_id: 任务ID
            timeout: 超时时间
            
        Returns:
            tuple: (结果, 计算时间) 或 None
        """
        try:
            while True:
                result_task_id, result, compute_time = self.result_queue.get(timeout=timeout)
                if result_task_id == task_id:
                    return result, compute_time
                else:
                    # 不是我们要的结果，放回队列
                    self.result_queue.put((result_task_id, result, compute_time))
                    
        except queue.Empty:
            return None
        except Exception as e:
            logger.error(f"获取GPU结果失败: {e}")
            return None
    
    def _gpu_compute_features(self, raw_data: List[Dict]) -> Optional[np.ndarray]:
        """GPU加速特征计算"""
        try:
            if not raw_data:
                return None
            
            # 转换数据格式
            prices = np.array([d['close'] for d in raw_data])
            volumes = np.array([d['volume'] for d in raw_data])
            
            if TORCH_AVAILABLE and self.gpu_device:
                return self._torch_compute_features(prices, volumes)
            elif CUPY_AVAILABLE:
                return self._cupy_compute_features(prices, volumes)
            else:
                return None
                
        except Exception as e:
            logger.error(f"GPU特征计算失败: {e}")
            return None
    
    def _torch_compute_features(self, prices: np.ndarray, volumes: np.ndarray) -> np.ndarray:
        """使用PyTorch进行GPU特征计算"""
        try:
            # 转移到GPU
            prices_gpu = torch.tensor(prices, dtype=torch.float32, device=self.gpu_device)
            volumes_gpu = torch.tensor(volumes, dtype=torch.float32, device=self.gpu_device)
            
            features = []
            
            # 价格特征
            if len(prices_gpu) > 1:
                price_change = (prices_gpu[-1] / prices_gpu[-2] - 1).item()
                features.append(price_change)
            else:
                features.append(0.0)
            
            # 移动平均特征
            if len(prices_gpu) >= 5:
                sma5 = torch.mean(prices_gpu[-5:])
                sma5_dev = (prices_gpu[-1] / sma5 - 1).item()
                features.append(sma5_dev)
            else:
                features.append(0.0)
            
            if len(prices_gpu) >= 20:
                sma20 = torch.mean(prices_gpu[-20:])
                sma20_dev = (prices_gpu[-1] / sma20 - 1).item()
                features.append(sma20_dev)
            else:
                features.append(0.0)
            
            # 波动率特征
            if len(prices_gpu) >= 10:
                returns = prices_gpu[1:] / prices_gpu[:-1] - 1
                volatility = torch.std(returns[-10:]).item()
                features.append(volatility)
            else:
                features.append(0.0)
            
            # 成交量特征
            if len(volumes_gpu) >= 5:
                vol_mean = torch.mean(volumes_gpu[-5:])
                vol_ratio = (volumes_gpu[-1] / vol_mean - 1).item() if vol_mean > 0 else 0.0
                features.append(vol_ratio)
            else:
                features.append(0.0)
            
            # 动量特征
            if len(prices_gpu) >= 5:
                momentum_5 = (prices_gpu[-1] / prices_gpu[-5] - 1).item()
                features.append(momentum_5)
            else:
                features.append(0.0)
            
            # 补齐到20个特征
            while len(features) < 20:
                features.append(0.0)
            
            # 清理GPU内存
            del prices_gpu, volumes_gpu
            torch.cuda.empty_cache()
            
            return np.array(features[:20])
            
        except Exception as e:
            logger.error(f"PyTorch特征计算失败: {e}")
            return None
    
    def _cupy_compute_features(self, prices: np.ndarray, volumes: np.ndarray) -> np.ndarray:
        """使用CuPy进行GPU特征计算"""
        try:
            # 转移到GPU
            prices_gpu = cp.asarray(prices)
            volumes_gpu = cp.asarray(volumes)
            
            features = []
            
            # 价格特征
            if len(prices_gpu) > 1:
                price_change = float(prices_gpu[-1] / prices_gpu[-2] - 1)
                features.append(price_change)
            else:
                features.append(0.0)
            
            # 移动平均特征
            if len(prices_gpu) >= 5:
                sma5 = cp.mean(prices_gpu[-5:])
                sma5_dev = float(prices_gpu[-1] / sma5 - 1)
                features.append(sma5_dev)
            else:
                features.append(0.0)
            
            if len(prices_gpu) >= 20:
                sma20 = cp.mean(prices_gpu[-20:])
                sma20_dev = float(prices_gpu[-1] / sma20 - 1)
                features.append(sma20_dev)
            else:
                features.append(0.0)
            
            # 波动率特征
            if len(prices_gpu) >= 10:
                returns = prices_gpu[1:] / prices_gpu[:-1] - 1
                volatility = float(cp.std(returns[-10:]))
                features.append(volatility)
            else:
                features.append(0.0)
            
            # 成交量特征
            if len(volumes_gpu) >= 5:
                vol_mean = cp.mean(volumes_gpu[-5:])
                vol_ratio = float(volumes_gpu[-1] / vol_mean - 1) if vol_mean > 0 else 0.0
                features.append(vol_ratio)
            else:
                features.append(0.0)
            
            # 动量特征
            if len(prices_gpu) >= 5:
                momentum_5 = float(prices_gpu[-1] / prices_gpu[-5] - 1)
                features.append(momentum_5)
            else:
                features.append(0.0)
            
            # 补齐到20个特征
            while len(features) < 20:
                features.append(0.0)
            
            # 清理GPU内存
            del prices_gpu, volumes_gpu
            cp.get_default_memory_pool().free_all_blocks()
            
            return np.array(features[:20])
            
        except Exception as e:
            logger.error(f"CuPy特征计算失败: {e}")
            return None
    
    def compute_features_sync(self, raw_data: List[Dict]) -> Optional[np.ndarray]:
        """
        同步计算特征（CPU备用方案）
        
        Args:
            raw_data: 原始数据
            
        Returns:
            np.ndarray: 特征向量
        """
        try:
            start_time = time.time()
            
            if not raw_data:
                return None
            
            # CPU计算
            prices = np.array([d['close'] for d in raw_data])
            volumes = np.array([d['volume'] for d in raw_data])
            
            features = []
            
            # 价格特征
            if len(prices) > 1:
                price_change = prices[-1] / prices[-2] - 1
                features.append(price_change)
            else:
                features.append(0.0)
            
            # 移动平均特征
            if len(prices) >= 5:
                sma5 = np.mean(prices[-5:])
                sma5_dev = prices[-1] / sma5 - 1
                features.append(sma5_dev)
            else:
                features.append(0.0)
            
            if len(prices) >= 20:
                sma20 = np.mean(prices[-20:])
                sma20_dev = prices[-1] / sma20 - 1
                features.append(sma20_dev)
            else:
                features.append(0.0)
            
            # 波动率特征
            if len(prices) >= 10:
                returns = prices[1:] / prices[:-1] - 1
                volatility = np.std(returns[-10:])
                features.append(volatility)
            else:
                features.append(0.0)
            
            # 成交量特征
            if len(volumes) >= 5:
                vol_mean = np.mean(volumes[-5:])
                vol_ratio = volumes[-1] / vol_mean - 1 if vol_mean > 0 else 0.0
                features.append(vol_ratio)
            else:
                features.append(0.0)
            
            # 动量特征
            if len(prices) >= 5:
                momentum_5 = prices[-1] / prices[-5] - 1
                features.append(momentum_5)
            else:
                features.append(0.0)
            
            # 补齐到20个特征
            while len(features) < 20:
                features.append(0.0)
            
            compute_time = time.time() - start_time
            self.cpu_compute_times.append(compute_time)
            
            return np.array(features[:20])
            
        except Exception as e:
            logger.error(f"CPU特征计算失败: {e}")
            return None
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        stats = {
            'gpu_enabled': self.enable_gpu,
            'gpu_available': GPU_AVAILABLE,
            'torch_available': TORCH_AVAILABLE,
            'cupy_available': CUPY_AVAILABLE,
            'total_computations': self.total_computations,
            'gpu_error_count': self.gpu_error_count,
            'gpu_avg_time': np.mean(self.gpu_compute_times) if self.gpu_compute_times else 0,
            'cpu_avg_time': np.mean(self.cpu_compute_times) if self.cpu_compute_times else 0,
            'speedup_ratio': 0
        }
        
        if stats['gpu_avg_time'] > 0 and stats['cpu_avg_time'] > 0:
            stats['speedup_ratio'] = stats['cpu_avg_time'] / stats['gpu_avg_time']
        
        return stats
    
    def shutdown(self):
        """安全关闭优化器"""
        try:
            if self.gpu_worker_running:
                self.gpu_worker_running = False
                # 发送停止信号
                self.feature_queue.put(None)
                
                # 等待线程结束
                if self.gpu_worker_thread:
                    self.gpu_worker_thread.join(timeout=2.0)
                
                logger.info("V100优化器已安全关闭")
                
        except Exception as e:
            logger.error(f"关闭V100优化器失败: {e}")
    
    def __del__(self):
        """析构函数"""
        self.shutdown()


# 全局优化器实例
_global_optimizer = None

def get_v100_optimizer(enable_gpu: bool = True) -> V100SafeOptimizer:
    """获取全局V100优化器实例"""
    global _global_optimizer
    
    if _global_optimizer is None:
        _global_optimizer = V100SafeOptimizer(enable_gpu=enable_gpu)
    
    return _global_optimizer

def shutdown_v100_optimizer():
    """关闭全局V100优化器"""
    global _global_optimizer
    
    if _global_optimizer:
        _global_optimizer.shutdown()
        _global_optimizer = None


# 导出
__all__ = ['V100SafeOptimizer', 'get_v100_optimizer', 'shutdown_v100_optimizer', 'GPU_AVAILABLE']
