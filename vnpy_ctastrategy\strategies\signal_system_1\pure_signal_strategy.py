#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
纯信号生成器策略 - 使用信号生成器作为唯一信号源
专为VNPY 4.1.0设计的简洁高效策略
"""

import os
import sys
import numpy as np
from datetime import datetime
from typing import List, Tuple

# 导入VNPY组件
from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    ArrayManager
)

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 导入信号系统API
try:
    from vnpy_signal_api import SignalSystemAPI
    SIGNAL_API_AVAILABLE = True
except ImportError:
    SIGNAL_API_AVAILABLE = False
    print("警告: 无法导入信号系统API，策略将无法正常运行")


class PureSignalStrategy(CtaTemplate):
    """
    纯信号生成器策略
    
    特点：
    1. 完全依赖信号生成器的输出
    2. 简洁的交易逻辑
    3. 基础的风险控制
    4. 高效的执行机制
    """
    
    author = "Pure Signal System v1.0"
    
    # 策略参数
    signal_threshold = 0.6       # 信号置信度阈值
    position_size = 1            # 固定仓位大小（手数）
    stop_loss_pct = 2.0          # 止损百分比
    take_profit_pct = 4.0        # 止盈百分比
    max_positions = 1            # 最大持仓数量
    
    # 变量
    signal_value = 0             # 当前信号值
    signal_confidence = 0.0      # 当前信号置信度
    entry_price = 0.0            # 入场价格
    last_signal_time = ""        # 最后信号时间
    
    # 参数和变量列表
    parameters = ["signal_threshold", "position_size", "stop_loss_pct", "take_profit_pct", "max_positions"]
    variables = ["signal_value", "signal_confidence", "entry_price", "last_signal_time"]
    
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        """初始化策略"""
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        
        # 创建K线生成器和技术指标管理器
        self.bg = BarGenerator(self.on_bar)
        self.am = ArrayManager(size=50)
        
        # 初始化信号系统
        self.signal_api = None
        self._init_signal_system()
        
        # 交易状态
        self.in_position = False
        self.position_direction = 0  # 1=多头, -1=空头, 0=无仓位
        
        self.write_log("纯信号策略初始化完成")
    
    def _init_signal_system(self):
        """初始化信号系统"""
        if not SIGNAL_API_AVAILABLE:
            self.write_log("错误: 信号系统API不可用")
            return
        
        try:
            # 查找模型文件
            model_path = self._find_model_file()
            
            # 创建信号API
            self.signal_api = SignalSystemAPI(
                model_path=model_path,
                use_alpha=True,  # 优先使用VNPY Alpha
                use_mock=False   # 不使用模拟模式
            )
            
            if self.signal_api.is_initialized():
                info = self.signal_api.get_system_info()
                self.write_log(f"信号系统初始化成功: {info.get('model_type', 'Unknown')}")
            else:
                self.write_log("警告: 信号系统初始化失败，将使用模拟模式")
                
        except Exception as e:
            self.write_log(f"初始化信号系统异常: {e}")
            self.signal_api = None
    
    def _find_model_file(self) -> str:
        """查找模型文件"""
        # 可能的模型文件位置
        possible_paths = [
            os.path.join(current_dir, "model.bin"),
            os.path.join(current_dir, "models", "model.bin"),
            os.path.join(current_dir, "model_checkpoints", "best_model.pt"),
            os.path.join(current_dir, "models", "best_model.pt")
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                self.write_log(f"找到模型文件: {path}")
                return path
        
        self.write_log("未找到模型文件，使用默认配置")
        return ""
    
    def on_init(self):
        """策略初始化回调"""
        self.write_log("策略初始化")
        self.load_bar(10)  # 加载10天历史数据
    
    def on_start(self):
        """策略启动回调"""
        self.write_log("策略启动")
    
    def on_stop(self):
        """策略停止回调"""
        self.write_log("策略停止")
    
    def on_tick(self, tick: TickData):
        """Tick数据回调"""
        self.bg.update_tick(tick)
    
    def on_bar(self, bar: BarData):
        """K线数据回调"""
        # 更新技术指标
        self.am.update_bar(bar)
        if not self.am.inited:
            return
        
        # 检查信号系统是否可用
        if not self.signal_api or not self.signal_api.is_initialized():
            return
        
        # 提取特征
        features = self._extract_features(bar)
        
        # 获取信号
        self.signal_value, self.signal_confidence = self.signal_api.predict(features)
        self.last_signal_time = bar.datetime.strftime("%H:%M:%S")
        
        # 记录信号
        signal_desc = self._get_signal_description()
        self.write_log(f"信号: {signal_desc}")
        
        # 执行交易逻辑
        self._execute_trading_logic(bar)
        
        # 风险控制
        self._risk_control(bar)
        
        # 更新界面
        self.put_event()
    
    def _extract_features(self, bar: BarData) -> List[float]:
        """提取交易特征"""
        try:
            features = []
            
            if self.am.inited:
                close = bar.close_price
                
                # 基础价格特征
                features.extend([
                    (close / bar.open_price - 1),           # 当前K线涨跌幅
                    (bar.high_price / bar.low_price - 1),   # 当前K线振幅
                    (close / self.am.sma(5) - 1),           # 相对5日均线偏离
                    (close / self.am.sma(20) - 1),          # 相对20日均线偏离
                ])
                
                # 动量特征
                if len(self.am.close_array) >= 5:
                    momentum = close / self.am.close_array[-5] - 1
                    features.append(momentum)
                else:
                    features.append(0.0)
                
                # 技术指标特征
                rsi = self.am.rsi(14)
                atr = self.am.atr(14)
                features.extend([
                    (rsi - 50) / 50,                        # 归一化RSI
                    atr / close if close > 0 else 0,        # 相对ATR
                ])
                
                # MACD特征
                try:
                    macd, signal, hist = self.am.macd(12, 26, 9)
                    features.append(hist)
                except:
                    features.append(0.0)
                
                # 成交量特征
                if len(self.am.volume_array) >= 5:
                    vol_ratio = bar.volume / np.mean(self.am.volume_array[-5:])
                    features.append(vol_ratio - 1)
                else:
                    features.append(0.0)
                
                # 布林带特征
                try:
                    boll_up, boll_mid, boll_down = self.am.boll(20, 2)
                    bb_position = (close - boll_down) / (boll_up - boll_down) if (boll_up - boll_down) > 0 else 0.5
                    features.append(bb_position)
                except:
                    features.append(0.5)
            
            # 确保特征数量一致
            while len(features) < 10:
                features.append(0.0)
            
            return features[:10]
            
        except Exception as e:
            self.write_log(f"提取特征失败: {e}")
            return [0.0] * 10
    
    def _get_signal_description(self) -> str:
        """获取信号描述"""
        if self.signal_value == 1:
            return f"多头信号 (置信度: {self.signal_confidence:.3f})"
        elif self.signal_value == -1:
            return f"空头信号 (置信度: {self.signal_confidence:.3f})"
        else:
            return f"中性信号 (置信度: {self.signal_confidence:.3f})"
    
    def _execute_trading_logic(self, bar: BarData):
        """执行交易逻辑"""
        try:
            # 检查信号强度
            if abs(self.signal_confidence) < self.signal_threshold:
                return
            
            current_pos = self.pos
            
            # 开仓逻辑
            if current_pos == 0:  # 无仓位时
                if self.signal_value == 1:  # 多头信号
                    self.buy(bar.close_price, self.position_size)
                    self.entry_price = bar.close_price
                    self.position_direction = 1
                    self.in_position = True
                    self.write_log(f"开多仓: 价格={bar.close_price:.2f}, 数量={self.position_size}")
                    
                elif self.signal_value == -1:  # 空头信号
                    self.sell(bar.close_price, self.position_size)
                    self.entry_price = bar.close_price
                    self.position_direction = -1
                    self.in_position = True
                    self.write_log(f"开空仓: 价格={bar.close_price:.2f}, 数量={self.position_size}")
            
            # 平仓逻辑（信号反转）
            elif current_pos != 0:
                # 信号与当前持仓方向相反时平仓
                if (current_pos > 0 and self.signal_value == -1) or (current_pos < 0 and self.signal_value == 1):
                    if current_pos > 0:
                        self.sell(bar.close_price, abs(current_pos))
                        self.write_log(f"平多仓: 价格={bar.close_price:.2f}")
                    else:
                        self.buy(bar.close_price, abs(current_pos))
                        self.write_log(f"平空仓: 价格={bar.close_price:.2f}")
                    
                    self._reset_position_state()
                    
        except Exception as e:
            self.write_log(f"执行交易逻辑失败: {e}")
    
    def _risk_control(self, bar: BarData):
        """风险控制"""
        if not self.in_position or self.entry_price == 0:
            return
        
        current_price = bar.close_price
        
        # 止损检查
        if self.position_direction == 1:  # 多头
            stop_loss_price = self.entry_price * (1 - self.stop_loss_pct / 100)
            take_profit_price = self.entry_price * (1 + self.take_profit_pct / 100)
            
            if current_price <= stop_loss_price:
                self.sell(current_price, abs(self.pos))
                self.write_log(f"多头止损: 入场={self.entry_price:.2f}, 止损={current_price:.2f}")
                self._reset_position_state()
            elif current_price >= take_profit_price:
                self.sell(current_price, abs(self.pos))
                self.write_log(f"多头止盈: 入场={self.entry_price:.2f}, 止盈={current_price:.2f}")
                self._reset_position_state()
                
        elif self.position_direction == -1:  # 空头
            stop_loss_price = self.entry_price * (1 + self.stop_loss_pct / 100)
            take_profit_price = self.entry_price * (1 - self.take_profit_pct / 100)
            
            if current_price >= stop_loss_price:
                self.buy(current_price, abs(self.pos))
                self.write_log(f"空头止损: 入场={self.entry_price:.2f}, 止损={current_price:.2f}")
                self._reset_position_state()
            elif current_price <= take_profit_price:
                self.buy(current_price, abs(self.pos))
                self.write_log(f"空头止盈: 入场={self.entry_price:.2f}, 止盈={current_price:.2f}")
                self._reset_position_state()
    
    def _reset_position_state(self):
        """重置持仓状态"""
        self.in_position = False
        self.position_direction = 0
        self.entry_price = 0.0
    
    def on_order(self, order: OrderData):
        """订单状态更新回调"""
        pass
    
    def on_trade(self, trade: TradeData):
        """成交回调"""
        # 更新持仓
        self.pos = self.get_pos()
        
        # 如果完全平仓，重置状态
        if self.pos == 0:
            self._reset_position_state()
        
        self.write_log(f"交易执行: {trade.direction} {trade.volume}手 @{trade.price:.2f}")
        self.put_event()
    
    def on_stop_order(self, stop_order: StopOrder):
        """停止单回调"""
        pass
