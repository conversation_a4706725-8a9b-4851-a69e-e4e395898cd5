#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
信号生成器策略测试脚本
用于测试和验证策略功能
"""

import os
import sys
import time
import numpy as np
from datetime import datetime, timedelta

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

def test_signal_api():
    """测试信号系统API"""
    print("=" * 50)
    print("测试信号系统API")
    print("=" * 50)
    
    try:
        from vnpy_signal_api import SignalSystemAPI
        
        # 创建API实例
        api = SignalSystemAPI(use_mock=True)  # 使用模拟模式进行测试
        
        print(f"API初始化状态: {api.is_initialized()}")
        
        # 获取系统信息
        info = api.get_system_info()
        print("系统信息:")
        for key, value in info.items():
            print(f"  {key}: {value}")
        
        # 测试预测功能
        print("\n测试预测功能:")
        test_features = [
            [0.001, 0.02, 0.005, -0.01, 0.003, 0.15, 0.002, 0.1, 0.6, 0.8],
            [-0.002, 0.015, -0.008, 0.02, -0.001, 0.12, -0.005, -0.05, 0.4, 1.2],
            [0.005, 0.025, 0.012, 0.008, 0.007, 0.18, 0.008, 0.15, 0.7, 0.9]
        ]
        
        for i, features in enumerate(test_features):
            signal, confidence = api.predict(features)
            signal_desc = "多头" if signal == 1 else "空头" if signal == -1 else "中性"
            print(f"  测试 {i+1}: 信号={signal_desc}, 置信度={confidence:.3f}")
        
        # 测试重置功能
        print(f"\n重置系统: {api.reset()}")
        
        print("✓ 信号系统API测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 信号系统API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_strategy_import():
    """测试策略导入"""
    print("\n" + "=" * 50)
    print("测试策略导入")
    print("=" * 50)
    
    strategies = [
        ("pure_signal_strategy", "PureSignalStrategy"),
        ("enhanced_signal_strategy", "EnhancedSignalStrategy"),
        ("vnpy_signal_strategy", "VnpySignalStrategy")
    ]
    
    success_count = 0
    
    for module_name, class_name in strategies:
        try:
            module = __import__(module_name)
            strategy_class = getattr(module, class_name)
            print(f"✓ {class_name} 导入成功")
            success_count += 1
        except Exception as e:
            print(f"✗ {class_name} 导入失败: {e}")
    
    print(f"\n策略导入测试完成: {success_count}/{len(strategies)} 成功")
    return success_count == len(strategies)

def test_feature_extraction():
    """测试特征提取"""
    print("\n" + "=" * 50)
    print("测试特征提取")
    print("=" * 50)
    
    try:
        # 模拟K线数据
        class MockBar:
            def __init__(self, open_price, high_price, low_price, close_price, volume):
                self.open_price = open_price
                self.high_price = high_price
                self.low_price = low_price
                self.close_price = close_price
                self.volume = volume
                self.datetime = datetime.now()
        
        # 模拟ArrayManager
        class MockArrayManager:
            def __init__(self):
                self.inited = True
                self.close_array = np.array([100, 101, 102, 103, 104, 105])
                self.volume_array = np.array([1000, 1100, 1200, 1300, 1400, 1500])
            
            def sma(self, n):
                return np.mean(self.close_array[-n:])
            
            def rsi(self, n):
                return 50.0
            
            def atr(self, n):
                return 2.0
            
            def macd(self, fast, slow, signal):
                return 0.5, 0.3, 0.2
            
            def boll(self, n, dev):
                mid = self.sma(n)
                return mid + 2, mid, mid - 2
        
        # 创建测试数据
        bar = MockBar(100, 105, 99, 104, 1500)
        am = MockArrayManager()
        
        # 测试特征提取函数
        from pure_signal_strategy import PureSignalStrategy
        
        # 创建策略实例（仅用于测试特征提取）
        strategy = PureSignalStrategy.__new__(PureSignalStrategy)
        strategy.am = am
        
        features = strategy._extract_features(bar)
        
        print(f"提取的特征数量: {len(features)}")
        print("特征值:")
        for i, feature in enumerate(features):
            print(f"  特征 {i+1}: {feature:.6f}")
        
        print("✓ 特征提取测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 特征提取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_vnpy_alpha_integration():
    """测试VNPY Alpha集成"""
    print("\n" + "=" * 50)
    print("测试VNPY Alpha集成")
    print("=" * 50)
    
    try:
        from vnpy.alpha.dataset import Alpha158Dataset
        from vnpy.alpha.model import LightGBMModel
        from vnpy.alpha.lab import AlphaLab
        
        print("✓ VNPY Alpha模块可用")
        
        # 测试Alpha158数据集
        dataset = Alpha158Dataset()
        print("✓ Alpha158数据集创建成功")
        
        # 测试模型
        model = LightGBMModel()
        print("✓ LightGBM模型创建成功")
        
        # 测试实验室
        lab = AlphaLab()
        print("✓ Alpha实验室创建成功")
        
        print("✓ VNPY Alpha集成测试完成")
        return True
        
    except ImportError:
        print("✗ VNPY Alpha模块不可用")
        return False
    except Exception as e:
        print(f"✗ VNPY Alpha集成测试失败: {e}")
        return False

def test_model_files():
    """测试模型文件"""
    print("\n" + "=" * 50)
    print("测试模型文件")
    print("=" * 50)
    
    model_paths = [
        os.path.join(current_dir, "model.bin"),
        os.path.join(current_dir, "models", "model.bin"),
        os.path.join(current_dir, "model_checkpoints", "best_model.pt"),
        os.path.join(current_dir, "signal_system.dll"),
        os.path.join(current_dir, "python", "signal_system.pyd")
    ]
    
    found_files = []
    for path in model_paths:
        if os.path.exists(path):
            size = os.path.getsize(path)
            print(f"✓ 找到文件: {path} ({size} bytes)")
            found_files.append(path)
        else:
            print(f"✗ 文件不存在: {path}")
    
    print(f"\n找到 {len(found_files)} 个模型/库文件")
    return len(found_files) > 0

def run_comprehensive_test():
    """运行综合测试"""
    print("VNPY 信号生成器策略综合测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {current_dir}")
    
    # 运行各项测试
    tests = [
        ("模型文件检查", test_model_files),
        ("VNPY Alpha集成", test_vnpy_alpha_integration),
        ("信号系统API", test_signal_api),
        ("策略导入", test_strategy_import),
        ("特征提取", test_feature_extraction)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        start_time = time.time()
        try:
            result = test_func()
            elapsed = time.time() - start_time
            results.append((test_name, result, elapsed))
            status = "通过" if result else "失败"
            print(f"{test_name} 测试{status} (耗时: {elapsed:.2f}秒)")
        except Exception as e:
            elapsed = time.time() - start_time
            results.append((test_name, False, elapsed))
            print(f"{test_name} 测试异常: {e} (耗时: {elapsed:.2f}秒)")
    
    # 输出测试总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = sum(1 for _, result, _ in results if result)
    total = len(results)
    
    for test_name, result, elapsed in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{status} {test_name} ({elapsed:.2f}秒)")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！策略可以正常使用。")
    elif passed >= total * 0.7:
        print("⚠️  大部分测试通过，策略基本可用，但可能存在一些问题。")
    else:
        print("❌ 多项测试失败，请检查配置和依赖。")
    
    return passed, total

if __name__ == "__main__":
    try:
        passed, total = run_comprehensive_test()
        
        # 根据测试结果设置退出码
        if passed == total:
            sys.exit(0)  # 所有测试通过
        elif passed >= total * 0.7:
            sys.exit(1)  # 部分测试失败
        else:
            sys.exit(2)  # 大部分测试失败
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(3)
    except Exception as e:
        print(f"\n测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(4)
