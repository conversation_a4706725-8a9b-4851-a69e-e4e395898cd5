#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
VNPY 4.1.0 兼容的信号系统API
集成深度学习信号生成器与VNPY框架
"""

import os
import sys
import time
import ctypes
import logging
import numpy as np
from typing import List, Dict, Optional, Tuple, Any, Union
from datetime import datetime
import traceback

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 尝试导入VNPY alpha模块
try:
    from vnpy.alpha.dataset import Alpha158Dataset
    from vnpy.alpha.model import LightGBMModel, LassoModel, MLPModel
    from vnpy.alpha.lab import AlphaLab
    VNPY_ALPHA_AVAILABLE = True
except ImportError:
    VNPY_ALPHA_AVAILABLE = False

# 尝试导入信号系统包装器
try:
    from python.signal_system_wrapper import SignalSystemWrapper, create_wrapper
    SIGNAL_WRAPPER_AVAILABLE = True
except ImportError:
    try:
        from signal_system_wrapper import SignalSystemWrapper, create_wrapper
        SIGNAL_WRAPPER_AVAILABLE = True
    except ImportError:
        SIGNAL_WRAPPER_AVAILABLE = False

# 配置日志
logger = logging.getLogger("VnpySignalAPI")
handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)
logger.setLevel(logging.INFO)


class SignalSystemAPI:
    """
    VNPY 4.1.0 兼容的信号系统API
    
    功能特点：
    1. 兼容VNPY 4.1.0的vnpy.alpha模块
    2. 支持多种信号生成方式（DLL、PYD、Alpha模型）
    3. 提供统一的接口用于策略调用
    4. 支持在线学习和模型更新
    5. 自动降级到模拟模式
    """
    
    def __init__(self, model_path: str = "", use_alpha: bool = True, use_mock: bool = False):
        """
        初始化信号系统API
        
        Args:
            model_path: 模型文件路径
            use_alpha: 是否优先使用VNPY Alpha模块
            use_mock: 是否强制使用模拟模式
        """
        self.model_path = model_path
        self.use_alpha = use_alpha and VNPY_ALPHA_AVAILABLE
        self.use_mock = use_mock
        
        # 初始化状态
        self.is_ready = False
        self.signal_system = None
        self.alpha_lab = None
        self.current_model = None
        self.last_prediction = {"signal": 0, "confidence": 0.0}
        
        # 特征缓存
        self.feature_cache = []
        self.max_cache_size = 100
        
        # 初始化系统
        self._initialize_system()
    
    def _initialize_system(self):
        """初始化信号系统"""
        try:
            logger.info("开始初始化信号系统API")
            
            if self.use_mock:
                logger.info("强制使用模拟模式")
                self._init_mock_system()
            elif self.use_alpha:
                logger.info("尝试使用VNPY Alpha模块")
                if self._init_alpha_system():
                    logger.info("VNPY Alpha模块初始化成功")
                else:
                    logger.warning("VNPY Alpha模块初始化失败，尝试信号包装器")
                    if self._init_signal_wrapper():
                        logger.info("信号包装器初始化成功")
                    else:
                        logger.warning("信号包装器初始化失败，使用模拟模式")
                        self._init_mock_system()
            else:
                logger.info("尝试使用信号包装器")
                if self._init_signal_wrapper():
                    logger.info("信号包装器初始化成功")
                else:
                    logger.warning("信号包装器初始化失败，使用模拟模式")
                    self._init_mock_system()
            
            self.is_ready = True
            logger.info("信号系统API初始化完成")
            
        except Exception as e:
            logger.error(f"初始化信号系统API失败: {e}")
            logger.error(traceback.format_exc())
            self._init_mock_system()
            self.is_ready = True
    
    def _init_alpha_system(self) -> bool:
        """初始化VNPY Alpha系统"""
        try:
            if not VNPY_ALPHA_AVAILABLE:
                return False
            
            # 创建Alpha实验室
            self.alpha_lab = AlphaLab()
            
            # 如果有模型路径，尝试加载模型
            if self.model_path and os.path.exists(self.model_path):
                logger.info(f"加载Alpha模型: {self.model_path}")
                # 根据模型文件扩展名选择模型类型
                if self.model_path.endswith('.lgb') or 'lgb' in self.model_path.lower():
                    self.current_model = LightGBMModel()
                elif self.model_path.endswith('.lasso') or 'lasso' in self.model_path.lower():
                    self.current_model = LassoModel()
                elif self.model_path.endswith('.mlp') or 'mlp' in self.model_path.lower():
                    self.current_model = MLPModel()
                else:
                    # 默认使用LightGBM
                    self.current_model = LightGBMModel()
                
                # 尝试加载模型
                try:
                    self.current_model.load(self.model_path)
                    logger.info("Alpha模型加载成功")
                except Exception as e:
                    logger.warning(f"加载Alpha模型失败: {e}")
                    self.current_model = None
            
            return True
            
        except Exception as e:
            logger.error(f"初始化VNPY Alpha系统失败: {e}")
            return False
    
    def _init_signal_wrapper(self) -> bool:
        """初始化信号包装器"""
        try:
            if not SIGNAL_WRAPPER_AVAILABLE:
                return False
            
            # 创建信号系统包装器
            self.signal_system = create_wrapper(
                use_mock=False,
                model_path=self.model_path
            )
            
            return True
            
        except Exception as e:
            logger.error(f"初始化信号包装器失败: {e}")
            return False
    
    def _init_mock_system(self):
        """初始化模拟系统"""
        try:
            if SIGNAL_WRAPPER_AVAILABLE:
                self.signal_system = create_wrapper(use_mock=True)
            else:
                # 创建简单的模拟系统
                self.signal_system = SimpleMockSystem()
            
            logger.info("模拟信号系统初始化成功")
            
        except Exception as e:
            logger.error(f"初始化模拟系统失败: {e}")
            self.signal_system = SimpleMockSystem()
    
    def is_initialized(self) -> bool:
        """检查系统是否已初始化"""
        return self.is_ready and self.signal_system is not None
    
    def predict(self, features: List[float]) -> Tuple[int, float]:
        """
        预测交易信号
        
        Args:
            features: 特征向量
            
        Returns:
            tuple: (信号值, 置信度)
                信号值: 1=多头, -1=空头, 0=中性
                置信度: 0.0-1.0
        """
        try:
            if not self.is_initialized():
                logger.warning("信号系统未初始化")
                return 0, 0.0
            
            # 缓存特征
            self.feature_cache.append(features)
            if len(self.feature_cache) > self.max_cache_size:
                self.feature_cache.pop(0)
            
            # 使用Alpha模型预测
            if self.current_model is not None:
                return self._predict_with_alpha(features)
            
            # 使用信号包装器预测
            elif hasattr(self.signal_system, 'predict'):
                return self._predict_with_wrapper(features)
            
            # 使用简单模拟预测
            else:
                return self._predict_with_mock(features)
                
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return 0, 0.0
    
    def _predict_with_alpha(self, features: List[float]) -> Tuple[int, float]:
        """使用VNPY Alpha模型预测"""
        try:
            # 将特征转换为numpy数组
            feature_array = np.array(features).reshape(1, -1)
            
            # 使用模型预测
            prediction = self.current_model.predict(feature_array)
            
            # 转换预测结果为信号格式
            if isinstance(prediction, np.ndarray):
                pred_value = float(prediction[0])
            else:
                pred_value = float(prediction)
            
            # 将预测值转换为信号和置信度
            if pred_value > 0.6:
                signal = 1
                confidence = min(pred_value, 0.95)
            elif pred_value < -0.6:
                signal = -1
                confidence = min(abs(pred_value), 0.95)
            else:
                signal = 0
                confidence = 0.5
            
            self.last_prediction = {"signal": signal, "confidence": confidence}
            return signal, confidence
            
        except Exception as e:
            logger.error(f"Alpha模型预测失败: {e}")
            return 0, 0.0
    
    def _predict_with_wrapper(self, features: List[float]) -> Tuple[int, float]:
        """使用信号包装器预测"""
        try:
            # 更新特征到信号系统
            if hasattr(self.signal_system, 'update'):
                # 假设第一个特征是价格，第二个是成交量
                price = features[0] if len(features) > 0 else 1.0
                volume = features[1] if len(features) > 1 else 1000.0
                self.signal_system.update(price, volume)
            
            # 获取预测结果
            prediction = self.signal_system.predict()
            
            # 解析预测结果
            signal_str = prediction.get('signal', 'NEUTRAL')
            confidence = prediction.get('confidence', 0.0)
            
            # 转换信号格式
            if signal_str == 'LONG':
                signal = 1
            elif signal_str == 'SHORT':
                signal = -1
            else:
                signal = 0
            
            self.last_prediction = {"signal": signal, "confidence": confidence}
            return signal, confidence
            
        except Exception as e:
            logger.error(f"信号包装器预测失败: {e}")
            return 0, 0.0
    
    def _predict_with_mock(self, features: List[float]) -> Tuple[int, float]:
        """使用模拟系统预测"""
        try:
            # 简单的模拟逻辑
            if len(features) >= 2:
                # 基于前两个特征的简单逻辑
                trend = features[0]  # 假设是趋势指标
                momentum = features[1]  # 假设是动量指标
                
                if trend > 0.01 and momentum > 0:
                    signal = 1
                    confidence = min(0.8, 0.5 + abs(trend) * 10)
                elif trend < -0.01 and momentum < 0:
                    signal = -1
                    confidence = min(0.8, 0.5 + abs(trend) * 10)
                else:
                    signal = 0
                    confidence = 0.5
            else:
                signal = 0
                confidence = 0.5
            
            self.last_prediction = {"signal": signal, "confidence": confidence}
            return signal, confidence
            
        except Exception as e:
            logger.error(f"模拟预测失败: {e}")
            return 0, 0.0


class SimpleMockSystem:
    """简单的模拟信号系统"""
    
    def __init__(self):
        self.version = "1.0.0-simple-mock"
        self.data_points = []
    
    def update(self, price: float, volume: float, timestamp: int = None, symbol: str = ""):
        """更新市场数据"""
        if timestamp is None:
            timestamp = int(time.time() * 1000)
        
        self.data_points.append({
            'price': price,
            'volume': volume,
            'timestamp': timestamp,
            'symbol': symbol
        })
        
        # 保持最近100个数据点
        if len(self.data_points) > 100:
            self.data_points.pop(0)
        
        return True
    
    def predict(self) -> Dict:
        """预测市场趋势"""
        if len(self.data_points) < 2:
            return {
                'signal': 'NEUTRAL',
                'confidence': 0.5,
                'timestamp': int(time.time() * 1000)
            }
        
        # 简单的趋势判断
        recent_prices = [dp['price'] for dp in self.data_points[-5:]]
        if len(recent_prices) >= 2:
            trend = (recent_prices[-1] - recent_prices[0]) / recent_prices[0]
            
            if trend > 0.005:
                signal = 'LONG'
                confidence = min(0.8, 0.6 + abs(trend) * 20)
            elif trend < -0.005:
                signal = 'SHORT'
                confidence = min(0.8, 0.6 + abs(trend) * 20)
            else:
                signal = 'NEUTRAL'
                confidence = 0.5
        else:
            signal = 'NEUTRAL'
            confidence = 0.5
        
        return {
            'signal': signal,
            'confidence': confidence,
            'timestamp': int(time.time() * 1000)
        }
    
    def reset(self):
        """重置系统"""
        self.data_points = []


    def load_model(self, model_path: str) -> bool:
        """
        加载新模型

        Args:
            model_path: 模型文件路径

        Returns:
            bool: 加载是否成功
        """
        try:
            if not os.path.exists(model_path):
                logger.error(f"模型文件不存在: {model_path}")
                return False

            self.model_path = model_path

            # 如果使用Alpha模块，重新加载模型
            if self.use_alpha and VNPY_ALPHA_AVAILABLE:
                return self._load_alpha_model(model_path)

            # 如果使用信号包装器，尝试重新初始化
            elif SIGNAL_WRAPPER_AVAILABLE:
                try:
                    self.signal_system = create_wrapper(
                        use_mock=False,
                        model_path=model_path
                    )
                    return True
                except Exception as e:
                    logger.error(f"重新加载信号包装器失败: {e}")
                    return False

            return False

        except Exception as e:
            logger.error(f"加载模型失败: {e}")
            return False

    def _load_alpha_model(self, model_path: str) -> bool:
        """加载Alpha模型"""
        try:
            # 根据模型文件扩展名选择模型类型
            if model_path.endswith('.lgb') or 'lgb' in model_path.lower():
                self.current_model = LightGBMModel()
            elif model_path.endswith('.lasso') or 'lasso' in model_path.lower():
                self.current_model = LassoModel()
            elif model_path.endswith('.mlp') or 'mlp' in model_path.lower():
                self.current_model = MLPModel()
            else:
                # 默认使用LightGBM
                self.current_model = LightGBMModel()

            # 加载模型
            self.current_model.load(model_path)
            logger.info(f"Alpha模型加载成功: {model_path}")
            return True

        except Exception as e:
            logger.error(f"加载Alpha模型失败: {e}")
            self.current_model = None
            return False

    def online_train(self, features_history: List[List[float]], label: int, confidence: float) -> bool:
        """
        在线学习

        Args:
            features_history: 历史特征数据
            label: 标签 (1=上涨, -1=下跌, 0=横盘)
            confidence: 标签置信度

        Returns:
            bool: 训练是否成功
        """
        try:
            if not self.is_initialized():
                return False

            # 如果使用Alpha模块，进行在线训练
            if self.current_model is not None and hasattr(self.current_model, 'partial_fit'):
                try:
                    # 准备训练数据
                    X = np.array(features_history)
                    y = np.array([label] * len(features_history))

                    # 在线训练
                    self.current_model.partial_fit(X, y)
                    logger.info(f"在线训练完成，样本数: {len(features_history)}")
                    return True

                except Exception as e:
                    logger.error(f"Alpha模型在线训练失败: {e}")
                    return False

            # 如果使用信号包装器，尝试在线学习
            elif hasattr(self.signal_system, 'online_learn'):
                try:
                    return self.signal_system.online_learn(features_history, label, confidence)
                except Exception as e:
                    logger.error(f"信号包装器在线学习失败: {e}")
                    return False

            # 模拟系统不支持在线学习
            logger.info("当前系统不支持在线学习")
            return False

        except Exception as e:
            logger.error(f"在线学习失败: {e}")
            return False

    def reset(self) -> bool:
        """
        重置系统状态

        Returns:
            bool: 重置是否成功
        """
        try:
            # 清空特征缓存
            self.feature_cache = []
            self.last_prediction = {"signal": 0, "confidence": 0.0}

            # 重置信号系统
            if hasattr(self.signal_system, 'reset'):
                self.signal_system.reset()

            logger.info("信号系统重置完成")
            return True

        except Exception as e:
            logger.error(f"重置系统失败: {e}")
            return False

    def get_system_info(self) -> Dict[str, Any]:
        """
        获取系统信息

        Returns:
            dict: 系统信息
        """
        info = {
            "api_version": "1.0.0",
            "is_initialized": self.is_initialized(),
            "use_alpha": self.use_alpha,
            "use_mock": self.use_mock,
            "model_path": self.model_path,
            "feature_cache_size": len(self.feature_cache),
            "last_prediction": self.last_prediction,
            "vnpy_alpha_available": VNPY_ALPHA_AVAILABLE,
            "signal_wrapper_available": SIGNAL_WRAPPER_AVAILABLE
        }

        # 添加系统特定信息
        if self.current_model is not None:
            info["model_type"] = type(self.current_model).__name__

        if hasattr(self.signal_system, 'get_version'):
            info["signal_system_version"] = self.signal_system.get_version()
        elif hasattr(self.signal_system, 'version'):
            info["signal_system_version"] = self.signal_system.version

        return info

    def extract_alpha158_features(self, price_data: Dict[str, List[float]]) -> List[float]:
        """
        提取Alpha158特征

        Args:
            price_data: 价格数据字典，包含 'open', 'high', 'low', 'close', 'volume'

        Returns:
            list: Alpha158特征向量
        """
        try:
            if not VNPY_ALPHA_AVAILABLE:
                logger.warning("VNPY Alpha模块不可用，无法提取Alpha158特征")
                return []

            # 创建Alpha158数据集
            dataset = Alpha158Dataset()

            # 转换数据格式
            import pandas as pd
            df = pd.DataFrame(price_data)

            # 提取特征
            features = dataset.extract_features(df)

            if isinstance(features, np.ndarray):
                return features.tolist()
            elif isinstance(features, list):
                return features
            else:
                return []

        except Exception as e:
            logger.error(f"提取Alpha158特征失败: {e}")
            return []


# 导出主要类
__all__ = ['SignalSystemAPI', 'SimpleMockSystem']
