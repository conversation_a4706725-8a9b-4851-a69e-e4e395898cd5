#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试商品代码提取功能
"""

import sys
from datetime import datetime

def test_commodity_extraction():
    """测试商品代码提取"""
    print("=" * 60)
    print("测试商品代码提取功能")
    print("=" * 60)
    
    try:
        from real_ml_strategy import RealMLStrategy
        
        # 创建策略实例用于测试
        strategy = RealMLStrategy.__new__(RealMLStrategy)
        
        # 测试用例
        test_cases = [
            ("rb2510.SHFE", "rb"),
            ("rb2501.SHFE", "rb"),
            ("cu2501.SHFE", "cu"),
            ("au2501.SHFE", "au"),
            ("ag2501.SHFE", "ag"),
            ("zn2501.SHFE", "zn"),
            ("al2501.SHFE", "al"),
            ("ni2501.SHFE", "ni"),
            ("IF2501.CFFEX", "if"),
            ("IC2501.CFFEX", "ic"),
            ("IH2501.CFFEX", "ih"),
            ("TS2501.CFFEX", "ts"),
            ("TF2501.CFFEX", "tf"),
            ("T2501.CFFEX", "t"),
            ("m2501.DCE", "m"),
            ("y2501.DCE", "y"),
            ("a2501.DCE", "a"),
            ("c2501.DCE", "c"),
            ("cs2501.DCE", "cs"),
            ("p2501.DCE", "p"),
            ("l2501.DCE", "l"),
            ("v2501.DCE", "v"),
            ("pp2501.DCE", "pp"),
            ("j2501.DCE", "j"),
            ("jm2501.DCE", "jm"),
            ("i2501.DCE", "i"),
            ("MA2501.CZCE", "ma"),
            ("TA2501.CZCE", "ta"),
            ("CF2501.CZCE", "cf"),
            ("SR2501.CZCE", "sr"),
            ("RM2501.CZCE", "rm"),
            ("OI2501.CZCE", "oi"),
            ("FG2501.CZCE", "fg"),
            ("ZC2501.CZCE", "zc"),
            ("SF2501.CZCE", "sf"),
            ("SM2501.CZCE", "sm"),
        ]
        
        print("测试商品代码提取:")
        print("-" * 40)
        
        success_count = 0
        total_count = len(test_cases)
        
        for vt_symbol, expected in test_cases:
            try:
                result = strategy._extract_commodity_code(vt_symbol)
                if result == expected:
                    status = "✓"
                    success_count += 1
                else:
                    status = "✗"
                
                print(f"{status} {vt_symbol:15} -> {result:8} (期望: {expected})")
                
            except Exception as e:
                print(f"✗ {vt_symbol:15} -> 错误: {e}")
        
        print("-" * 40)
        print(f"测试结果: {success_count}/{total_count} 通过")
        
        if success_count == total_count:
            print("🎉 所有测试通过!")
            return True
        else:
            print("⚠️ 部分测试失败")
            return False
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_path_generation():
    """测试模型路径生成"""
    print("\n" + "=" * 60)
    print("测试模型路径生成")
    print("=" * 60)
    
    try:
        import os
        from real_ml_strategy import RealMLStrategy
        
        # 模拟策略初始化
        strategy = RealMLStrategy.__new__(RealMLStrategy)
        strategy.model_type = "lightgbm"
        
        test_symbols = [
            "rb2510.SHFE",
            "cu2501.SHFE", 
            "IF2501.CFFEX",
            "m2501.DCE",
            "MA2501.CZCE"
        ]
        
        print("测试模型路径生成:")
        print("-" * 60)
        
        for vt_symbol in test_symbols:
            strategy.vt_symbol = vt_symbol
            
            # 模拟路径生成逻辑
            current_dir = os.path.dirname(os.path.abspath(__file__))
            model_dir = os.path.join(current_dir, "signal_system", "models")
            
            commodity_code = strategy._extract_commodity_code(vt_symbol)
            model_path = os.path.join(model_dir, f"{commodity_code}_{strategy.model_type}_model.pkl")
            
            print(f"合约: {vt_symbol}")
            print(f"商品: {commodity_code}")
            print(f"路径: {model_path}")
            print("-" * 30)
        
        print("✓ 模型路径生成测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 模型路径生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_sharing():
    """测试模型共享机制"""
    print("\n" + "=" * 60)
    print("测试模型共享机制")
    print("=" * 60)
    
    print("模型共享说明:")
    print("- rb2510.SHFE 和 rb2501.SHFE 共享 rb_lightgbm_model.pkl")
    print("- cu2510.SHFE 和 cu2501.SHFE 共享 cu_lightgbm_model.pkl")
    print("- 同一商品的不同合约使用相同的模型")
    print("- 模型会根据所有相关合约的数据进行训练和更新")
    
    # 模拟场景
    scenarios = [
        {
            "description": "螺纹钢主力合约切换",
            "contracts": ["rb2501.SHFE", "rb2505.SHFE", "rb2510.SHFE"],
            "shared_model": "rb_lightgbm_model.pkl"
        },
        {
            "description": "铜期货合约",
            "contracts": ["cu2501.SHFE", "cu2502.SHFE", "cu2503.SHFE"],
            "shared_model": "cu_lightgbm_model.pkl"
        },
        {
            "description": "股指期货",
            "contracts": ["IF2501.CFFEX", "IF2502.CFFEX", "IF2503.CFFEX"],
            "shared_model": "if_lightgbm_model.pkl"
        }
    ]
    
    print("\n模型共享场景:")
    print("-" * 40)
    
    for scenario in scenarios:
        print(f"场景: {scenario['description']}")
        print(f"合约: {', '.join(scenario['contracts'])}")
        print(f"共享模型: {scenario['shared_model']}")
        print("-" * 20)
    
    print("✓ 模型共享机制测试完成")
    return True

def run_commodity_tests():
    """运行商品代码测试"""
    print("VNPY 商品代码提取测试")
    print("=" * 70)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 运行测试
    tests = [
        ("商品代码提取", test_commodity_extraction),
        ("模型路径生成", test_model_path_generation),
        ("模型共享机制", test_model_sharing)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            status = "通过" if result else "失败"
            print(f"\n{test_name} 测试{status}")
        except Exception as e:
            results.append((test_name, False))
            print(f"\n{test_name} 测试异常: {e}")
    
    # 输出总结
    print("\n" + "=" * 70)
    print("测试总结")
    print("=" * 70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！商品代码提取功能正常。")
        print("\n功能说明:")
        print("1. 自动从合约代码提取商品代码")
        print("2. 同一商品的不同合约共享模型")
        print("3. 如 rb2510.SHFE -> rb_lightgbm_model.pkl")
        print("4. 模型会根据该商品的历史数据训练")
    else:
        print("⚠️ 部分测试失败，请检查实现。")
    
    return passed, total

if __name__ == "__main__":
    try:
        passed, total = run_commodity_tests()
        
        if passed == total:
            sys.exit(0)
        else:
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(3)
    except Exception as e:
        print(f"\n测试异常: {e}")
        sys.exit(4)
