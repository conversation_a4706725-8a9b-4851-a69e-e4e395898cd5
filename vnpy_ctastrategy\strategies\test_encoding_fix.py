#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试编码修复
"""

import sys
import re
from datetime import datetime

def test_emoji_removal():
    """测试emoji符号移除"""
    print("=" * 60)
    print("测试emoji符号移除")
    print("=" * 60)
    
    try:
        # 读取V100策略文件
        with open('v100_tick_ml_strategy.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查常见的emoji符号
        emoji_patterns = [
            r'✅',  # 绿色勾号
            r'❌',  # 红色叉号
            r'🚀',  # 火箭
            r'🎉',  # 庆祝
            r'⚠️',  # 警告
            r'💡',  # 灯泡
            r'📊',  # 图表
            r'🔧',  # 扳手
            r'⭐',  # 星星
        ]
        
        found_emojis = []
        for pattern in emoji_patterns:
            matches = re.findall(pattern, content)
            if matches:
                found_emojis.extend([(pattern, len(matches))])
        
        if found_emojis:
            print("发现emoji符号:")
            for emoji, count in found_emojis:
                print(f"  {emoji}: {count}个")
            return False
        else:
            print("✓ 未发现emoji符号，编码安全")
            return True
            
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def test_strategy_import():
    """测试策略导入"""
    print("\n" + "=" * 60)
    print("测试策略导入")
    print("=" * 60)
    
    try:
        from v100_tick_ml_strategy import V100TickMLStrategy
        print("✓ V100TickMLStrategy导入成功")
        print(f"  作者: {V100TickMLStrategy.author}")
        return True
    except Exception as e:
        print(f"导入失败: {e}")
        return False

def test_log_messages():
    """测试日志消息"""
    print("\n" + "=" * 60)
    print("测试日志消息")
    print("=" * 60)
    
    try:
        from v100_tick_ml_strategy import V100TickMLStrategy
        
        # 创建策略实例
        strategy = V100TickMLStrategy.__new__(V100TickMLStrategy)
        
        # 模拟属性
        strategy.gpu_enabled = True
        strategy.tick_count = 1000
        strategy.generated_bars = 10
        strategy.trade_count = 5
        strategy.gpu_compute_count = 8
        strategy.cpu_compute_count = 2
        strategy.avg_gpu_time = 0.001
        strategy.avg_cpu_time = 0.005
        strategy.cache_hit_count = 15
        strategy.cache_miss_count = 5
        strategy.win_count = 3
        strategy.total_pnl = 0.025
        
        # 收集日志消息
        log_messages = []
        def mock_write_log(msg):
            log_messages.append(msg)
            print(f"[LOG] {msg}")
        
        strategy.write_log = mock_write_log
        
        # 测试性能统计输出
        strategy._output_performance_stats()
        
        # 检查日志消息中是否有非ASCII字符
        problematic_messages = []
        for msg in log_messages:
            try:
                msg.encode('gbk')
            except UnicodeEncodeError as e:
                problematic_messages.append((msg, str(e)))
        
        if problematic_messages:
            print(f"\n发现 {len(problematic_messages)} 个编码问题:")
            for msg, error in problematic_messages:
                print(f"  消息: {msg}")
                print(f"  错误: {error}")
            return False
        else:
            print(f"\n✓ 所有 {len(log_messages)} 条日志消息编码正常")
            return True
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chinese_encoding():
    """测试中文编码"""
    print("\n" + "=" * 60)
    print("测试中文编码")
    print("=" * 60)
    
    test_messages = [
        "V100优化器初始化成功",
        "GPU设备: cuda:0",
        "V100 GPU加速已启用",
        "V100模型训练成功!",
        "准确率: 0.756",
        "V100模型已保存",
        "可以开始V100加速ML交易!",
        "V100性能统计",
        "Tick总数: 1000",
        "生成K线: 10根",
        "交易次数: 5",
        "GPU计算: 8次",
        "CPU计算: 2次",
        "V100加速比: 5.0倍",
        "缓存命中率: 75%",
        "交易胜率: 60%"
    ]
    
    try:
        for msg in test_messages:
            try:
                # 测试GBK编码
                msg.encode('gbk')
                print(f"✓ {msg}")
            except UnicodeEncodeError as e:
                print(f"✗ {msg} - 编码错误: {e}")
                return False
        
        print(f"\n✓ 所有 {len(test_messages)} 条消息GBK编码正常")
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        return False

def run_encoding_tests():
    """运行编码测试"""
    print("V100策略编码修复验证")
    print("=" * 70)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("修复VNPY日志系统的Unicode编码错误")
    
    # 运行测试
    tests = [
        ("emoji符号移除", test_emoji_removal),
        ("策略导入", test_strategy_import),
        ("日志消息编码", test_log_messages),
        ("中文编码", test_chinese_encoding)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            status = "通过" if result else "失败"
            print(f"\n{test_name} 测试{status}")
        except Exception as e:
            results.append((test_name, False))
            print(f"\n{test_name} 测试异常: {e}")
    
    # 输出总结
    print("\n" + "=" * 70)
    print("编码修复验证总结")
    print("=" * 70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 编码修复成功！")
        print("\n修复内容:")
        print("✓ 移除了所有emoji符号")
        print("✓ 所有日志消息支持GBK编码")
        print("✓ 保持了中文字符的正常显示")
        print("✓ 解决了VNPY日志系统的Unicode错误")
        print("\n现在策略可以在VNPY中正常运行，不会出现编码错误！")
    else:
        print("⚠️ 部分测试失败，请检查编码问题。")
    
    return passed, total

if __name__ == "__main__":
    try:
        passed, total = run_encoding_tests()
        
        if passed == total:
            sys.exit(0)
        else:
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(3)
    except Exception as e:
        print(f"\n测试异常: {e}")
        sys.exit(4)
