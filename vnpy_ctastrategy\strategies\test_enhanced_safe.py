#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试安全版增强策略
"""

import sys
from datetime import datetime

def test_enhanced_safe_import():
    """测试安全版增强策略导入"""
    print("=" * 50)
    print("测试安全版增强策略导入")
    print("=" * 50)
    
    try:
        from enhanced_signal_strategy_safe import EnhancedSignalStrategySafe
        print("✓ EnhancedSignalStrategySafe 导入成功")
        print(f"  作者: {EnhancedSignalStrategySafe.author}")
        print(f"  参数数量: {len(EnhancedSignalStrategySafe.parameters)}")
        print(f"  变量数量: {len(EnhancedSignalStrategySafe.variables)}")
        
        # 检查参数
        print("  参数列表:")
        for i, param in enumerate(EnhancedSignalStrategySafe.parameters):
            print(f"    {i+1}. {param}")
        
        return True
        
    except Exception as e:
        print(f"✗ EnhancedSignalStrategySafe 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_alpha_module_integration():
    """测试Alpha模块集成"""
    print("\n" + "=" * 50)
    print("测试Alpha模块集成")
    print("=" * 50)
    
    try:
        # 测试VNPY Alpha模块导入
        try:
            from vnpy.alpha.dataset import Alpha158Dataset
            from vnpy.alpha.lab import AlphaLab
            print("✓ VNPY Alpha模块可用")
            
            # 测试Alpha158数据集
            dataset = Alpha158Dataset()
            print("✓ Alpha158数据集创建成功")
            
            # 测试Alpha实验室
            lab = AlphaLab()
            print("✓ Alpha实验室创建成功")
            
            alpha_available = True
            
        except ImportError:
            print("⚠️  VNPY Alpha模块不可用 (这是正常的，如果没有安装Alpha模块)")
            alpha_available = False
        
        # 测试策略中的Alpha集成
        from enhanced_signal_strategy_safe import EnhancedSignalStrategySafe
        
        # 检查Alpha相关参数
        alpha_params = [p for p in EnhancedSignalStrategySafe.parameters if 'alpha' in p.lower()]
        print(f"✓ Alpha相关参数: {alpha_params}")
        
        return True
        
    except Exception as e:
        print(f"✗ Alpha模块集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_features():
    """测试增强特征功能"""
    print("\n" + "=" * 50)
    print("测试增强特征功能")
    print("=" * 50)
    
    try:
        from enhanced_signal_strategy_safe import EnhancedSignalStrategySafe
        
        # 模拟策略实例
        strategy = EnhancedSignalStrategySafe.__new__(EnhancedSignalStrategySafe)
        
        # 初始化必要的属性
        strategy.am = MockArrayManager()
        strategy.use_alpha_features = True
        strategy.alpha_dataset = None
        strategy.feature_window = 30
        strategy.price_data = {
            'open': [100, 101, 102],
            'high': [105, 106, 107],
            'low': [99, 100, 101],
            'close': [104, 105, 106],
            'volume': [1000, 1100, 1200]
        }
        
        # 模拟K线数据
        bar = MockBar()
        
        # 测试特征提取
        features = strategy._extract_enhanced_features(bar)
        
        print(f"✓ 提取的特征数量: {len(features)}")
        print("  特征值预览:")
        for i, feature in enumerate(features[:5]):
            print(f"    特征 {i+1}: {feature:.6f}")
        
        # 测试简单信号生成
        signal, confidence = strategy._simple_signal(features)
        signal_desc = "多头" if signal == 1 else "空头" if signal == -1 else "中性"
        print(f"✓ 简单信号生成: {signal_desc}, 置信度: {confidence:.3f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 增强特征功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_risk_management():
    """测试风险管理功能"""
    print("\n" + "=" * 50)
    print("测试风险管理功能")
    print("=" * 50)
    
    try:
        from enhanced_signal_strategy_safe import EnhancedSignalStrategySafe
        
        # 模拟策略实例
        strategy = EnhancedSignalStrategySafe.__new__(EnhancedSignalStrategySafe)
        
        # 初始化风险管理相关属性
        strategy.daily_trades = 0
        strategy.max_daily_trades = 10
        strategy.daily_pnl = 0.0
        strategy.daily_loss_limit = 0.02
        strategy.max_drawdown_value = 0.0
        strategy.max_drawdown = 0.05
        
        # 测试交易条件检查
        can_trade = strategy._can_trade()
        print(f"✓ 初始交易条件检查: {can_trade}")
        
        # 模拟达到交易限制
        strategy.daily_trades = 10
        can_trade = strategy._can_trade()
        print(f"✓ 达到交易次数限制后: {can_trade}")
        
        # 模拟达到亏损限制
        strategy.daily_trades = 5
        strategy.daily_pnl = -0.03
        can_trade = strategy._can_trade()
        print(f"✓ 达到亏损限制后: {can_trade}")
        
        # 测试仓位计算
        strategy.position_ratio = 0.2
        strategy.signal_confidence = 0.75
        strategy.max_position_ratio = 0.5
        
        position_size = strategy._calculate_position_size(4000.0)
        print(f"✓ 仓位计算: {position_size}手")
        
        return True
        
    except Exception as e:
        print(f"✗ 风险管理功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

# 模拟类
class MockArrayManager:
    def __init__(self):
        self.inited = True
        self.open_array = [100, 101, 102, 103, 104]
        self.close_array = [104, 105, 106, 107, 108]
        self.volume_array = [1000, 1100, 1200, 1300, 1400]
    
    def sma(self, n):
        return sum(self.close_array[-n:]) / n
    
    def rsi(self, n):
        return 55.0
    
    def atr(self, n):
        return 2.5
    
    def macd(self, fast, slow, signal):
        return 0.8, 0.5, 0.3
    
    def boll(self, n, dev):
        mid = self.sma(n)
        return mid + 2, mid, mid - 2

class MockBar:
    def __init__(self):
        self.open_price = 100.0
        self.high_price = 105.0
        self.low_price = 99.0
        self.close_price = 104.0
        self.volume = 1500
        self.datetime = datetime.now()

def run_enhanced_safe_test():
    """运行安全版增强策略测试"""
    print("VNPY 安全版增强信号策略测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python版本: {sys.version}")
    
    # 运行测试
    tests = [
        ("安全版增强策略导入", test_enhanced_safe_import),
        ("Alpha模块集成", test_alpha_module_integration),
        ("增强特征功能", test_enhanced_features),
        ("风险管理功能", test_risk_management)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "通过" if result else "失败"
            print(f"{test_name} 测试{status}")
        except Exception as e:
            results.append((test_name, False))
            print(f"{test_name} 测试异常: {e}")
    
    # 输出总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！安全版增强策略可以正常使用。")
        print("\n使用建议:")
        print("1. 在VNPY中使用策略类名: EnhancedSignalStrategySafe")
        print("2. 该版本包含14个可配置参数")
        print("3. 支持Alpha158特征工程（如果VNPY Alpha模块可用）")
        print("4. 具有智能风险管理和自适应调整功能")
    elif passed >= total * 0.75:
        print("⚠️  大部分测试通过，基本可用。")
    else:
        print("❌ 多项测试失败，请检查配置。")
    
    return passed, total

if __name__ == "__main__":
    try:
        passed, total = run_enhanced_safe_test()
        
        if passed == total:
            sys.exit(0)
        elif passed >= total * 0.75:
            sys.exit(1)
        else:
            sys.exit(2)
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(3)
    except Exception as e:
        print(f"\n测试异常: {e}")
        sys.exit(4)
