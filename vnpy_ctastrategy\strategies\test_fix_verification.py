#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
验证修复后的策略
"""

import sys
from datetime import datetime

def test_enhanced_safe_fix():
    """测试增强版安全策略修复"""
    print("=" * 50)
    print("测试增强版安全策略修复")
    print("=" * 50)
    
    try:
        from enhanced_signal_strategy_safe import EnhancedSignalStrategySafe
        print("✓ EnhancedSignalStrategySafe 导入成功")
        
        # 检查是否有get_pos方法调用
        import inspect
        source = inspect.getsource(EnhancedSignalStrategySafe)
        
        if 'get_pos()' in source:
            print("✗ 仍然存在 get_pos() 调用")
            return False
        else:
            print("✓ 已移除所有 get_pos() 调用")
        
        # 检查on_start方法
        on_start_source = inspect.getsource(EnhancedSignalStrategySafe.on_start)
        if 'self.pos *' in on_start_source:
            print("✓ on_start 方法已修复，使用 self.pos")
        else:
            print("⚠️  on_start 方法可能需要检查")
        
        # 检查on_trade方法
        on_trade_source = inspect.getsource(EnhancedSignalStrategySafe.on_trade)
        if 'get_pos()' not in on_trade_source:
            print("✓ on_trade 方法已修复")
        else:
            print("✗ on_trade 方法仍有问题")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signal_safe_fix():
    """测试基础安全策略修复"""
    print("\n" + "=" * 50)
    print("测试基础安全策略修复")
    print("=" * 50)
    
    try:
        from signal_strategy_safe import SignalStrategySafe
        print("✓ SignalStrategySafe 导入成功")
        
        # 检查是否有get_pos方法调用
        import inspect
        source = inspect.getsource(SignalStrategySafe)
        
        if 'get_pos()' in source:
            print("✗ 仍然存在 get_pos() 调用")
            return False
        else:
            print("✓ 已移除所有 get_pos() 调用")
        
        # 检查on_trade方法
        on_trade_source = inspect.getsource(SignalStrategySafe.on_trade)
        if 'get_pos()' not in on_trade_source:
            print("✓ on_trade 方法已修复")
        else:
            print("✗ on_trade 方法仍有问题")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_vnpy_compatibility():
    """测试VNPY兼容性"""
    print("\n" + "=" * 50)
    print("测试VNPY兼容性")
    print("=" * 50)
    
    try:
        # 测试VNPY组件导入
        from vnpy_ctastrategy import CtaTemplate
        print("✓ VNPY CTA组件导入成功")
        
        # 检查CtaTemplate的pos属性
        if hasattr(CtaTemplate, 'pos'):
            print("✓ CtaTemplate 有 pos 属性")
        else:
            print("⚠️  CtaTemplate 可能没有 pos 属性")
        
        # 测试策略继承
        from enhanced_signal_strategy_safe import EnhancedSignalStrategySafe
        from signal_strategy_safe import SignalStrategySafe
        
        if issubclass(EnhancedSignalStrategySafe, CtaTemplate):
            print("✓ EnhancedSignalStrategySafe 正确继承 CtaTemplate")
        
        if issubclass(SignalStrategySafe, CtaTemplate):
            print("✓ SignalStrategySafe 正确继承 CtaTemplate")
        
        return True
        
    except Exception as e:
        print(f"✗ VNPY兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_strategy_methods():
    """测试策略方法"""
    print("\n" + "=" * 50)
    print("测试策略方法")
    print("=" * 50)
    
    try:
        from enhanced_signal_strategy_safe import EnhancedSignalStrategySafe
        
        # 检查必要的方法
        required_methods = [
            'on_init', 'on_start', 'on_stop', 
            'on_tick', 'on_bar', 'on_trade', 'on_order'
        ]
        
        for method in required_methods:
            if hasattr(EnhancedSignalStrategySafe, method):
                print(f"✓ 找到方法: {method}")
            else:
                print(f"✗ 缺少方法: {method}")
                return False
        
        # 测试策略实例化（模拟）
        strategy = EnhancedSignalStrategySafe.__new__(EnhancedSignalStrategySafe)
        
        # 设置基本属性
        strategy.pos = 0  # 模拟pos属性
        
        print("✓ 策略实例化测试成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 策略方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_fix_verification():
    """运行修复验证测试"""
    print("VNPY 策略修复验证测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("修复内容: 将 get_pos() 改为 pos 属性")
    
    # 运行测试
    tests = [
        ("增强版安全策略修复", test_enhanced_safe_fix),
        ("基础安全策略修复", test_signal_safe_fix),
        ("VNPY兼容性", test_vnpy_compatibility),
        ("策略方法", test_strategy_methods)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "通过" if result else "失败"
            print(f"{test_name} 测试{status}")
        except Exception as e:
            results.append((test_name, False))
            print(f"{test_name} 测试异常: {e}")
    
    # 输出总结
    print("\n" + "=" * 60)
    print("修复验证总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有修复验证通过！策略现在可以正常运行。")
        print("\n修复说明:")
        print("1. 将 self.get_pos() 改为 self.pos")
        print("2. pos 是 VNPY 框架自动维护的属性")
        print("3. 无需手动调用 get_pos() 方法")
        print("\n现在可以在VNPY中正常使用:")
        print("- EnhancedSignalStrategySafe (增强版)")
        print("- SignalStrategySafe (基础版)")
    elif passed >= total * 0.75:
        print("⚠️  大部分修复验证通过，基本可用。")
    else:
        print("❌ 多项修复验证失败，需要进一步检查。")
    
    return passed, total

if __name__ == "__main__":
    try:
        passed, total = run_fix_verification()
        
        if passed == total:
            sys.exit(0)
        elif passed >= total * 0.75:
            sys.exit(1)
        else:
            sys.exit(2)
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(3)
    except Exception as e:
        print(f"\n测试异常: {e}")
        sys.exit(4)
