#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GPU安装验证测试
"""

import sys
import time
from datetime import datetime

def test_pytorch_installation():
    """测试PyTorch安装"""
    print("=" * 60)
    print("测试PyTorch安装")
    print("=" * 60)
    
    try:
        import torch
        print("✅ PyTorch导入成功")
        print(f"   版本: {torch.__version__}")
        
        # 检查CUDA
        cuda_available = torch.cuda.is_available()
        print(f"   CUDA可用: {cuda_available}")
        
        if cuda_available:
            print(f"   GPU数量: {torch.cuda.device_count()}")
            print(f"   当前GPU: {torch.cuda.current_device()}")
            print(f"   GPU名称: {torch.cuda.get_device_name(0)}")
            print(f"   GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
            
            # 测试GPU计算
            print("\n🚀 测试GPU计算性能...")
            device = torch.device('cuda:0')
            
            # GPU测试
            start_time = time.time()
            x = torch.randn(1000, 1000, device=device)
            y = torch.randn(1000, 1000, device=device)
            z = torch.matmul(x, y)
            torch.cuda.synchronize()
            gpu_time = time.time() - start_time
            
            # CPU测试
            start_time = time.time()
            x_cpu = torch.randn(1000, 1000)
            y_cpu = torch.randn(1000, 1000)
            z_cpu = torch.matmul(x_cpu, y_cpu)
            cpu_time = time.time() - start_time
            
            speedup = cpu_time / gpu_time
            print(f"   GPU计算时间: {gpu_time:.4f}秒")
            print(f"   CPU计算时间: {cpu_time:.4f}秒")
            print(f"   GPU加速比: {speedup:.2f}倍")
            
            # 清理GPU内存
            del x, y, z, x_cpu, y_cpu, z_cpu
            torch.cuda.empty_cache()
            
            return True, speedup
        else:
            print("⚠️ CUDA不可用")
            return True, 1.0
            
    except ImportError as e:
        print(f"❌ PyTorch导入失败: {e}")
        return False, 0
    except Exception as e:
        print(f"❌ GPU测试失败: {e}")
        return False, 0

def test_cupy_installation():
    """测试CuPy安装"""
    print("\n" + "=" * 60)
    print("测试CuPy安装")
    print("=" * 60)
    
    try:
        import cupy as cp
        print("✅ CuPy导入成功")
        print(f"   版本: {cp.__version__}")
        
        # 测试CuPy计算
        print("\n🚀 测试CuPy计算性能...")
        
        # GPU测试
        start_time = time.time()
        x_gpu = cp.random.randn(1000, 1000)
        y_gpu = cp.random.randn(1000, 1000)
        z_gpu = cp.dot(x_gpu, y_gpu)
        cp.cuda.Stream.null.synchronize()
        cupy_time = time.time() - start_time
        
        # CPU测试
        import numpy as np
        start_time = time.time()
        x_cpu = np.random.randn(1000, 1000)
        y_cpu = np.random.randn(1000, 1000)
        z_cpu = np.dot(x_cpu, y_cpu)
        numpy_time = time.time() - start_time
        
        speedup = numpy_time / cupy_time
        print(f"   CuPy计算时间: {cupy_time:.4f}秒")
        print(f"   NumPy计算时间: {numpy_time:.4f}秒")
        print(f"   CuPy加速比: {speedup:.2f}倍")
        
        # 清理GPU内存
        del x_gpu, y_gpu, z_gpu
        cp.get_default_memory_pool().free_all_blocks()
        
        return True, speedup
        
    except ImportError:
        print("⚠️ CuPy未安装")
        return False, 0
    except Exception as e:
        print(f"❌ CuPy测试失败: {e}")
        return False, 0

def test_v100_strategy_compatibility():
    """测试V100策略兼容性"""
    print("\n" + "=" * 60)
    print("测试V100策略兼容性")
    print("=" * 60)
    
    try:
        # 测试V100优化器
        from signal_system.v100_optimizer import get_v100_optimizer, GPU_AVAILABLE
        print("✅ V100优化器导入成功")
        print(f"   GPU可用性: {GPU_AVAILABLE}")
        
        # 创建优化器实例
        optimizer = get_v100_optimizer(enable_gpu=True)
        print(f"   优化器启用: {optimizer.enable_gpu}")
        
        if optimizer.enable_gpu:
            print(f"   GPU设备: {optimizer.gpu_device}")
        
        # 测试V100策略
        from v100_tick_ml_strategy import V100TickMLStrategy
        print("✅ V100TickMLStrategy导入成功")
        print(f"   作者: {V100TickMLStrategy.author}")
        
        return True
        
    except ImportError as e:
        print(f"⚠️ V100策略导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ V100策略测试失败: {e}")
        return False

def install_cupy_if_needed():
    """如果需要，安装CuPy"""
    print("\n" + "=" * 60)
    print("检查CuPy安装")
    print("=" * 60)
    
    try:
        import cupy
        print("✅ CuPy已安装")
        return True
    except ImportError:
        print("⚠️ CuPy未安装，正在安装...")
        try:
            import subprocess
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", 
                "cupy-cuda12x"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ CuPy安装成功")
                return True
            else:
                print(f"❌ CuPy安装失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ CuPy安装异常: {e}")
            return False

def run_gpu_tests():
    """运行GPU测试"""
    print("VNPY GPU环境验证测试")
    print("=" * 70)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("验证V100 GPU环境配置")
    
    results = {}
    
    # 测试PyTorch
    pytorch_ok, pytorch_speedup = test_pytorch_installation()
    results['pytorch'] = (pytorch_ok, pytorch_speedup)
    
    # 安装并测试CuPy
    cupy_installed = install_cupy_if_needed()
    if cupy_installed:
        cupy_ok, cupy_speedup = test_cupy_installation()
        results['cupy'] = (cupy_ok, cupy_speedup)
    else:
        results['cupy'] = (False, 0)
    
    # 测试V100策略兼容性
    v100_ok = test_v100_strategy_compatibility()
    results['v100_strategy'] = (v100_ok, 0)
    
    # 输出总结
    print("\n" + "=" * 70)
    print("GPU环境测试总结")
    print("=" * 70)
    
    all_passed = True
    
    for test_name, (passed, speedup) in results.items():
        status = "✅ 通过" if passed else "❌ 失败"
        if speedup > 1:
            print(f"{status} {test_name} (加速比: {speedup:.2f}倍)")
        else:
            print(f"{status} {test_name}")
        
        if not passed:
            all_passed = False
    
    print(f"\n总体结果: {'🎉 所有测试通过' if all_passed else '⚠️ 部分测试失败'}")
    
    if all_passed:
        print("\n🚀 V100 GPU环境配置完成！")
        print("您现在可以使用以下策略：")
        print("- V100TickMLStrategy (GPU加速版本)")
        print("- TickMLStrategy (CPU版本)")
        print("\n推荐使用V100TickMLStrategy以获得最佳性能！")
    else:
        print("\n💡 建议：")
        if not results['pytorch'][0]:
            print("- 等待PyTorch安装完成")
        if not results['cupy'][0]:
            print("- 手动安装CuPy: pip install cupy-cuda12x")
        if not results['v100_strategy'][0]:
            print("- 检查V100策略文件是否存在")
    
    return all_passed

if __name__ == "__main__":
    try:
        success = run_gpu_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(3)
    except Exception as e:
        print(f"\n测试异常: {e}")
        sys.exit(4)
