#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试新文件结构的策略
"""

import os
import sys
import numpy as np
from datetime import datetime

def test_signal_system_import():
    """测试信号系统导入"""
    print("=" * 50)
    print("测试信号系统导入")
    print("=" * 50)
    
    try:
        from signal_system import SignalSystemAPI
        print("✓ 从signal_system包导入SignalSystemAPI成功")
        
        # 创建API实例
        api = SignalSystemAPI(use_mock=True)
        print(f"✓ API初始化状态: {api.is_initialized()}")
        
        # 获取系统信息
        info = api.get_system_info()
        print("系统信息:")
        for key, value in info.items():
            print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"✗ 信号系统导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_strategy_import():
    """测试策略导入"""
    print("\n" + "=" * 50)
    print("测试策略导入")
    print("=" * 50)
    
    strategies = [
        ("signal_strategy", "SignalStrategy"),
        ("enhanced_signal_strategy", "EnhancedSignalStrategy")
    ]
    
    success_count = 0
    
    for module_name, class_name in strategies:
        try:
            module = __import__(module_name)
            strategy_class = getattr(module, class_name)
            print(f"✓ {class_name} 导入成功")
            print(f"  作者: {strategy_class.author}")
            print(f"  参数数量: {len(strategy_class.parameters)}")
            success_count += 1
        except Exception as e:
            print(f"✗ {class_name} 导入失败: {e}")
    
    print(f"\n策略导入测试完成: {success_count}/{len(strategies)} 成功")
    return success_count == len(strategies)

def test_signal_prediction():
    """测试信号预测功能"""
    print("\n" + "=" * 50)
    print("测试信号预测功能")
    print("=" * 50)
    
    try:
        from signal_system import SignalSystemAPI
        
        api = SignalSystemAPI(use_mock=True)
        
        # 测试不同的市场情况
        test_cases = [
            {
                "name": "强势上涨",
                "features": [0.02, 0.03, 0.015, 0.02, 0.025, 0.2, 0.005, 0.15, 0.8, 1.5]
            },
            {
                "name": "强势下跌", 
                "features": [-0.025, 0.035, -0.02, -0.025, -0.03, 0.18, -0.008, -0.1, 0.2, 1.8]
            },
            {
                "name": "横盘整理",
                "features": [0.001, 0.01, 0.002, -0.001, 0.001, 0.05, 0.0, 0.02, 0.5, 0.8]
            }
        ]
        
        print("信号预测测试:")
        for case in test_cases:
            signal, confidence = api.predict(case["features"])
            signal_desc = "多头" if signal == 1 else "空头" if signal == -1 else "中性"
            
            print(f"  {case['name']}:")
            print(f"    信号: {signal_desc}")
            print(f"    置信度: {confidence:.3f}")
        
        print("✓ 信号预测功能测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 信号预测功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_vnpy_integration():
    """测试VNPY集成"""
    print("\n" + "=" * 50)
    print("测试VNPY集成")
    print("=" * 50)
    
    try:
        # 测试VNPY组件导入
        from vnpy_ctastrategy import CtaTemplate, BarGenerator, ArrayManager
        print("✓ VNPY CTA组件导入成功")
        
        # 测试策略类创建
        from signal_strategy import SignalStrategy
        print("✓ SignalStrategy类导入成功")
        
        # 检查策略属性
        print(f"  策略作者: {SignalStrategy.author}")
        print(f"  参数列表: {SignalStrategy.parameters}")
        print(f"  变量列表: {SignalStrategy.variables}")
        
        print("✓ VNPY集成测试完成")
        return True
        
    except Exception as e:
        print(f"✗ VNPY集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_structure():
    """测试文件结构"""
    print("\n" + "=" * 50)
    print("测试文件结构")
    print("=" * 50)
    
    # 获取当前目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 检查必要文件
    required_files = [
        "signal_strategy.py",
        "enhanced_signal_strategy.py",
        os.path.join("signal_system", "__init__.py"),
        os.path.join("signal_system", "signal_api.py")
    ]
    
    missing_files = []
    for file_path in required_files:
        full_path = os.path.join(current_dir, file_path)
        if os.path.exists(full_path):
            print(f"✓ 找到文件: {file_path}")
        else:
            print(f"✗ 缺少文件: {file_path}")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n缺少 {len(missing_files)} 个文件")
        return False
    else:
        print("\n✓ 所有必要文件都存在")
        return True

def run_comprehensive_test():
    """运行综合测试"""
    print("VNPY 信号策略新文件结构测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python版本: {sys.version}")
    
    # 运行测试
    tests = [
        ("文件结构检查", test_file_structure),
        ("信号系统导入", test_signal_system_import),
        ("策略导入", test_strategy_import),
        ("信号预测功能", test_signal_prediction),
        ("VNPY集成", test_vnpy_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "通过" if result else "失败"
            print(f"{test_name} 测试{status}")
        except Exception as e:
            results.append((test_name, False))
            print(f"{test_name} 测试异常: {e}")
    
    # 输出总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！新文件结构工作正常。")
        print("\n使用说明:")
        print("1. 在VNPY中可以直接导入以下策略:")
        print("   - SignalStrategy (基础版)")
        print("   - EnhancedSignalStrategy (增强版)")
        print("2. 策略会自动从signal_system包导入API")
        print("3. 支持文件放在signal_system文件夹中")
    elif passed >= total * 0.75:
        print("⚠️  大部分测试通过，基本可用。")
    else:
        print("❌ 多项测试失败，请检查文件结构。")
    
    return passed, total

if __name__ == "__main__":
    try:
        passed, total = run_comprehensive_test()
        
        if passed == total:
            sys.exit(0)
        elif passed >= total * 0.75:
            sys.exit(1)
        else:
            sys.exit(2)
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(3)
    except Exception as e:
        print(f"\n测试异常: {e}")
        sys.exit(4)
