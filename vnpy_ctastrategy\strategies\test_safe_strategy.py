#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试安全版策略
"""

import sys
from datetime import datetime

def test_safe_strategy_import():
    """测试安全版策略导入"""
    print("=" * 50)
    print("测试安全版策略导入")
    print("=" * 50)
    
    try:
        from signal_strategy_safe import SignalStrategySafe
        print("✓ SignalStrategySafe 导入成功")
        print(f"  作者: {SignalStrategySafe.author}")
        print(f"  参数数量: {len(SignalStrategySafe.parameters)}")
        print(f"  变量数量: {len(SignalStrategySafe.variables)}")
        return True
        
    except Exception as e:
        print(f"✗ SignalStrategySafe 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signal_system_integration():
    """测试信号系统集成"""
    print("\n" + "=" * 50)
    print("测试信号系统集成")
    print("=" * 50)
    
    try:
        from signal_system import SignalSystemAPI
        
        # 创建API实例
        api = SignalSystemAPI(use_mock=True)
        print(f"✓ 信号系统初始化: {api.is_initialized()}")
        
        # 测试预测
        test_features = [0.01, 0.02, 0.005, 0.008, 0.012, 0.15, 0.002, 0.1, 0.6, 0.8]
        signal, confidence = api.predict(test_features)
        signal_desc = "多头" if signal == 1 else "空头" if signal == -1 else "中性"
        print(f"✓ 信号预测: {signal_desc}, 置信度: {confidence:.3f}")
        
        return True
        
    except Exception as e:
        print(f"✗ 信号系统集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_vnpy_compatibility():
    """测试VNPY兼容性"""
    print("\n" + "=" * 50)
    print("测试VNPY兼容性")
    print("=" * 50)
    
    try:
        # 测试VNPY组件导入
        from vnpy_ctastrategy import CtaTemplate, BarGenerator, ArrayManager
        print("✓ VNPY CTA组件导入成功")
        
        # 测试策略类继承
        from signal_strategy_safe import SignalStrategySafe
        
        # 检查是否正确继承CtaTemplate
        if issubclass(SignalStrategySafe, CtaTemplate):
            print("✓ SignalStrategySafe 正确继承 CtaTemplate")
        else:
            print("✗ SignalStrategySafe 继承关系错误")
            return False
        
        # 检查必要的方法
        required_methods = ['on_init', 'on_start', 'on_stop', 'on_tick', 'on_bar', 'on_trade', 'on_order']
        for method in required_methods:
            if hasattr(SignalStrategySafe, method):
                print(f"✓ 找到方法: {method}")
            else:
                print(f"✗ 缺少方法: {method}")
                return False
        
        print("✓ VNPY兼容性测试完成")
        return True
        
    except Exception as e:
        print(f"✗ VNPY兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_log_safety():
    """测试日志安全性"""
    print("\n" + "=" * 50)
    print("测试日志安全性")
    print("=" * 50)
    
    try:
        from signal_strategy_safe import SignalStrategySafe
        
        # 模拟策略实例（仅用于测试）
        strategy = SignalStrategySafe.__new__(SignalStrategySafe)
        
        # 测试格式化方法
        strategy.signal_value = 1
        strategy.signal_confidence = 0.756
        
        desc = strategy._format_signal_desc()
        print(f"✓ 信号描述格式化: {desc}")
        
        # 测试其他可能的格式化问题
        test_values = [
            (1, 0.756),
            (-1, 0.623),
            (0, 0.500),
            (1, 0.999),
            (-1, 0.001)
        ]
        
        for signal, confidence in test_values:
            strategy.signal_value = signal
            strategy.signal_confidence = confidence
            desc = strategy._format_signal_desc()
            print(f"  信号={signal}, 置信度={confidence:.3f} -> {desc}")
        
        print("✓ 日志安全性测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 日志安全性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_safe_strategy_test():
    """运行安全版策略测试"""
    print("VNPY 安全版信号策略测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python版本: {sys.version}")
    
    # 运行测试
    tests = [
        ("安全版策略导入", test_safe_strategy_import),
        ("信号系统集成", test_signal_system_integration),
        ("VNPY兼容性", test_vnpy_compatibility),
        ("日志安全性", test_log_safety)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "通过" if result else "失败"
            print(f"{test_name} 测试{status}")
        except Exception as e:
            results.append((test_name, False))
            print(f"{test_name} 测试异常: {e}")
    
    # 输出总结
    print("\n" + "=" * 60)
    print("测试总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！安全版策略可以正常使用。")
        print("\n使用建议:")
        print("1. 在VNPY中使用策略类名: SignalStrategySafe")
        print("2. 该版本避免了日志格式化问题")
        print("3. 推荐用于生产环境")
    elif passed >= total * 0.75:
        print("⚠️  大部分测试通过，基本可用。")
    else:
        print("❌ 多项测试失败，请检查配置。")
    
    return passed, total

if __name__ == "__main__":
    try:
        passed, total = run_safe_strategy_test()
        
        if passed == total:
            sys.exit(0)
        elif passed >= total * 0.75:
            sys.exit(1)
        else:
            sys.exit(2)
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(3)
    except Exception as e:
        print(f"\n测试异常: {e}")
        sys.exit(4)
