#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试Tick ML策略
"""

import sys
from datetime import datetime

def test_tick_ml_strategy():
    """测试Tick ML策略"""
    print("=" * 60)
    print("测试Tick ML策略")
    print("=" * 60)
    
    try:
        from tick_ml_strategy import TickMLStrategy
        print("✓ TickMLStrategy 导入成功")
        print(f"  作者: {TickMLStrategy.author}")
        print(f"  参数数量: {len(TickMLStrategy.parameters)}")
        print(f"  变量数量: {len(TickMLStrategy.variables)}")
        
        # 检查Tick模式特有参数
        tick_params = ['ticks_per_bar', 'min_bars_to_train', 'tick_count', 'generated_bars']
        for param in tick_params:
            if param in TickMLStrategy.parameters or param in TickMLStrategy.variables:
                print(f"✓ 找到Tick参数: {param}")
            else:
                print(f"✗ 缺少Tick参数: {param}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tick_data_processing():
    """测试Tick数据处理"""
    print("\n" + "=" * 60)
    print("测试Tick数据处理")
    print("=" * 60)
    
    try:
        from tick_ml_strategy import TickMLStrategy
        
        # 创建策略实例用于测试
        strategy = TickMLStrategy.__new__(TickMLStrategy)
        strategy.ticks_per_bar = 50
        strategy.current_bar_ticks = []
        strategy.generated_bar_history = []
        strategy.generated_bars = 0
        strategy.max_bar_history = 100
        
        # 模拟Tick数据
        from collections import namedtuple
        MockTick = namedtuple('MockTick', ['datetime', 'last_price', 'volume', 'bid_price_1', 'ask_price_1'])
        
        # 生成模拟tick数据
        base_price = 4000.0
        for i in range(100):
            price = base_price + (i % 10 - 5) * 0.5  # 价格在3997.5-4002.5之间波动
            tick = MockTick(
                datetime=datetime.now(),
                last_price=price,
                volume=100 + i % 50,
                bid_price_1=price - 0.5,
                ask_price_1=price + 0.5
            )
            strategy.current_bar_ticks.append(tick)
            
            # 每50个tick生成一根K线
            if len(strategy.current_bar_ticks) >= strategy.ticks_per_bar:
                strategy._generate_bar_from_current_ticks()
        
        print(f"✓ 生成了 {strategy.generated_bars} 根K线")
        print(f"✓ K线历史数据: {len(strategy.generated_bar_history)} 条")
        
        if strategy.generated_bars > 0:
            last_bar = strategy.generated_bar_history[-1]
            print(f"  最后一根K线: O={last_bar['open']:.2f}, H={last_bar['high']:.2f}, L={last_bar['low']:.2f}, C={last_bar['close']:.2f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Tick数据处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_path_generation():
    """测试Tick模型路径生成"""
    print("\n" + "=" * 60)
    print("测试Tick模型路径生成")
    print("=" * 60)
    
    try:
        import os
        from tick_ml_strategy import TickMLStrategy
        
        strategy = TickMLStrategy.__new__(TickMLStrategy)
        strategy.model_type = "lightgbm"
        
        test_symbols = [
            "rb2510.SHFE",
            "cu2501.SHFE",
            "IF2501.CFFEX"
        ]
        
        print("Tick模型路径生成:")
        print("-" * 40)
        
        for vt_symbol in test_symbols:
            commodity_code = strategy._extract_commodity_code(vt_symbol)
            
            current_dir = os.path.dirname(os.path.abspath(__file__))
            model_dir = os.path.join(current_dir, "signal_system", "models")
            model_path = os.path.join(model_dir, f"{commodity_code}_{strategy.model_type}_tick_model.pkl")
            
            print(f"合约: {vt_symbol}")
            print(f"商品: {commodity_code}")
            print(f"Tick模型: {os.path.basename(model_path)}")
            print("-" * 20)
        
        print("说明:")
        print("- Tick模型文件名包含 '_tick_model.pkl' 后缀")
        print("- 与普通模型区分开，避免冲突")
        print("- 专门针对Tick数据训练优化")
        
        return True
        
    except Exception as e:
        print(f"✗ 模型路径测试失败: {e}")
        return False

def test_vnpy_integration():
    """测试VNPY集成"""
    print("\n" + "=" * 60)
    print("测试VNPY集成")
    print("=" * 60)
    
    try:
        # 检查VNPY组件
        from vnpy_ctastrategy import CtaTemplate
        print("✓ VNPY CTA组件可用")
        
        # 检查策略继承
        from tick_ml_strategy import TickMLStrategy
        if issubclass(TickMLStrategy, CtaTemplate):
            print("✓ TickMLStrategy 正确继承 CtaTemplate")
        else:
            print("✗ TickMLStrategy 继承关系错误")
            return False
        
        # 检查必要方法
        required_methods = ['on_init', 'on_start', 'on_stop', 'on_tick', 'on_trade']
        for method in required_methods:
            if hasattr(TickMLStrategy, method):
                print(f"✓ 找到方法: {method}")
            else:
                print(f"✗ 缺少方法: {method}")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ VNPY集成测试失败: {e}")
        return False

def compare_strategies():
    """比较不同策略版本"""
    print("\n" + "=" * 60)
    print("策略版本比较")
    print("=" * 60)
    
    strategies = [
        {
            "name": "RealMLStrategy",
            "file": "real_ml_strategy.py",
            "description": "使用历史数据 + Tick数据",
            "pros": ["完整历史数据训练", "更准确的模型"],
            "cons": ["需要RQData", "启动较慢"]
        },
        {
            "name": "TickMLStrategy", 
            "file": "tick_ml_strategy.py",
            "description": "仅使用Tick数据",
            "pros": ["无需历史数据", "快速启动", "不依赖License"],
            "cons": ["训练数据有限", "需要更多时间积累"]
        }
    ]
    
    for strategy in strategies:
        print(f"策略: {strategy['name']}")
        print(f"文件: {strategy['file']}")
        print(f"描述: {strategy['description']}")
        print("优点:")
        for pro in strategy['pros']:
            print(f"  + {pro}")
        print("缺点:")
        for con in strategy['cons']:
            print(f"  - {con}")
        print("-" * 30)
    
    print("推荐使用:")
    print("- 如果RQData可用: RealMLStrategy")
    print("- 如果License过期: TickMLStrategy ⭐")
    
    return True

def run_tick_ml_tests():
    """运行Tick ML测试"""
    print("VNPY Tick机器学习策略测试")
    print("=" * 70)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("专为RQData License过期设计")
    
    # 运行测试
    tests = [
        ("Tick ML策略", test_tick_ml_strategy),
        ("Tick数据处理", test_tick_data_processing),
        ("模型路径生成", test_model_path_generation),
        ("VNPY集成", test_vnpy_integration),
        ("策略版本比较", compare_strategies)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            status = "通过" if result else "失败"
            print(f"\n{test_name} 测试{status}")
        except Exception as e:
            results.append((test_name, False))
            print(f"\n{test_name} 测试异常: {e}")
    
    # 输出总结
    print("\n" + "=" * 70)
    print("测试总结")
    print("=" * 70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！Tick ML策略可以正常使用。")
        print("\n使用说明:")
        print("1. 在VNPY中使用策略类名: TickMLStrategy")
        print("2. 无需RQData License，仅使用实时Tick数据")
        print("3. 每100个Tick自动生成一根K线用于训练")
        print("4. 收集50根K线后自动开始模型训练")
        print("5. 适合License过期或数据源受限的情况")
        print("\n推荐配置:")
        print("- ticks_per_bar: 100 (每100个tick生成一根K线)")
        print("- min_bars_to_train: 50 (50根K线开始训练)")
        print("- model_type: lightgbm")
        print("- online_learning: True")
    else:
        print("⚠️ 部分测试失败，请检查问题。")
    
    return passed, total

if __name__ == "__main__":
    try:
        passed, total = run_tick_ml_tests()
        
        if passed == total:
            sys.exit(0)
        else:
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(3)
    except Exception as e:
        print(f"\n测试异常: {e}")
        sys.exit(4)
