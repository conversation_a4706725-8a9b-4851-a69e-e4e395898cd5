#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试V100优化策略
"""

import sys
import time
from datetime import datetime

def test_v100_optimizer():
    """测试V100优化器"""
    print("=" * 60)
    print("测试V100优化器")
    print("=" * 60)
    
    try:
        from signal_system.v100_optimizer import V100SafeOptimizer, GPU_AVAILABLE
        print("✓ V100优化器导入成功")
        print(f"  GPU可用性: {GPU_AVAILABLE}")
        
        # 创建优化器
        optimizer = V100SafeOptimizer(enable_gpu=True)
        print(f"✓ 优化器创建成功，GPU启用: {optimizer.enable_gpu}")
        
        if optimizer.enable_gpu:
            print(f"  GPU设备: {optimizer.gpu_device}")
        
        # 测试特征计算
        test_data = []
        for i in range(100):
            test_data.append({
                'close': 4000 + i * 0.1,
                'volume': 1000 + i * 10,
                'datetime': datetime.now()
            })
        
        print("测试同步特征计算...")
        start_time = time.time()
        features = optimizer.compute_features_sync(test_data)
        sync_time = time.time() - start_time
        
        if features is not None:
            print(f"✓ 同步计算成功，耗时: {sync_time:.4f}秒")
            print(f"  特征数量: {len(features)}")
            print(f"  特征示例: {features[:5]}")
        else:
            print("✗ 同步计算失败")
        
        # 测试异步计算
        if optimizer.enable_gpu:
            print("测试异步特征计算...")
            task_id = optimizer.compute_features_async(test_data)
            if task_id:
                print(f"✓ 异步任务提交成功: {task_id}")
                
                # 等待结果
                result = optimizer.get_result(task_id, timeout=1.0)
                if result:
                    async_features, async_time = result
                    print(f"✓ 异步计算成功，耗时: {async_time:.4f}秒")
                    
                    if sync_time > 0 and async_time > 0:
                        speedup = sync_time / async_time
                        print(f"  加速比: {speedup:.2f}倍")
                else:
                    print("⚠️ 异步计算超时")
            else:
                print("⚠️ 异步任务提交失败")
        
        # 获取性能统计
        stats = optimizer.get_performance_stats()
        print("性能统计:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # 关闭优化器
        optimizer.shutdown()
        print("✓ 优化器已安全关闭")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_v100_strategy():
    """测试V100策略"""
    print("\n" + "=" * 60)
    print("测试V100策略")
    print("=" * 60)
    
    try:
        from v100_tick_ml_strategy import V100TickMLStrategy
        print("✓ V100TickMLStrategy 导入成功")
        print(f"  作者: {V100TickMLStrategy.author}")
        print(f"  参数数量: {len(V100TickMLStrategy.parameters)}")
        print(f"  变量数量: {len(V100TickMLStrategy.variables)}")
        
        # 检查V100特有参数
        v100_params = [
            'enable_v100', 'gpu_async_compute', 'gpu_feature_cache',
            'gpu_enabled', 'gpu_compute_count', 'cpu_compute_count',
            'avg_gpu_time', 'avg_cpu_time', 'gpu_speedup'
        ]
        
        for param in v100_params:
            if param in V100TickMLStrategy.parameters or param in V100TickMLStrategy.variables:
                print(f"✓ 找到V100参数: {param}")
            else:
                print(f"✗ 缺少V100参数: {param}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gpu_libraries():
    """测试GPU库"""
    print("\n" + "=" * 60)
    print("测试GPU库")
    print("=" * 60)
    
    # 测试PyTorch
    try:
        import torch
        print("✓ PyTorch 可用")
        print(f"  版本: {torch.__version__}")
        
        if torch.cuda.is_available():
            print("✓ CUDA 可用")
            print(f"  GPU数量: {torch.cuda.device_count()}")
            print(f"  当前GPU: {torch.cuda.current_device()}")
            print(f"  GPU名称: {torch.cuda.get_device_name(0)}")
            
            # 测试GPU计算
            test_tensor = torch.randn(1000, 1000).cuda()
            start_time = time.time()
            result = torch.matmul(test_tensor, test_tensor)
            gpu_time = time.time() - start_time
            print(f"  GPU计算测试: {gpu_time:.4f}秒")
            
            # 清理
            del test_tensor, result
            torch.cuda.empty_cache()
        else:
            print("✗ CUDA 不可用")
            
    except ImportError:
        print("✗ PyTorch 不可用")
    except Exception as e:
        print(f"⚠️ PyTorch 测试异常: {e}")
    
    # 测试CuPy
    try:
        import cupy as cp
        print("✓ CuPy 可用")
        print(f"  版本: {cp.__version__}")
        
        # 测试GPU计算
        test_array = cp.random.randn(1000, 1000)
        start_time = time.time()
        result = cp.dot(test_array, test_array)
        cupy_time = time.time() - start_time
        print(f"  CuPy计算测试: {cupy_time:.4f}秒")
        
        # 清理
        del test_array, result
        cp.get_default_memory_pool().free_all_blocks()
        
    except ImportError:
        print("✗ CuPy 不可用")
    except Exception as e:
        print(f"⚠️ CuPy 测试异常: {e}")
    
    return True

def test_vnpy_compatibility():
    """测试VNPY兼容性"""
    print("\n" + "=" * 60)
    print("测试VNPY兼容性")
    print("=" * 60)
    
    try:
        # 检查VNPY组件
        from vnpy_ctastrategy import CtaTemplate
        print("✓ VNPY CTA组件可用")
        
        # 检查策略继承
        from v100_tick_ml_strategy import V100TickMLStrategy
        if issubclass(V100TickMLStrategy, CtaTemplate):
            print("✓ V100TickMLStrategy 正确继承 CtaTemplate")
        else:
            print("✗ V100TickMLStrategy 继承关系错误")
            return False
        
        # 检查必要方法
        required_methods = ['on_init', 'on_start', 'on_stop', 'on_tick', 'on_trade']
        for method in required_methods:
            if hasattr(V100TickMLStrategy, method):
                print(f"✓ 找到方法: {method}")
            else:
                print(f"✗ 缺少方法: {method}")
                return False
        
        # 检查V100特有方法
        v100_methods = [
            '_init_v100_optimizer', '_extract_features_v100', 
            '_execute_v100_ml_trading', '_train_model_from_generated_bars'
        ]
        
        for method in v100_methods:
            if hasattr(V100TickMLStrategy, method):
                print(f"✓ 找到V100方法: {method}")
            else:
                print(f"✗ 缺少V100方法: {method}")
        
        return True
        
    except Exception as e:
        print(f"✗ VNPY兼容性测试失败: {e}")
        return False

def compare_strategies():
    """比较策略版本"""
    print("\n" + "=" * 60)
    print("策略版本比较")
    print("=" * 60)
    
    strategies = [
        {
            "name": "TickMLStrategy",
            "file": "tick_ml_strategy.py",
            "description": "基础Tick策略",
            "features": ["Tick数据", "CPU计算", "基础ML"],
            "performance": "标准"
        },
        {
            "name": "V100TickMLStrategy",
            "file": "v100_tick_ml_strategy.py", 
            "description": "V100优化Tick策略",
            "features": ["Tick数据", "GPU加速", "异步计算", "特征缓存", "性能监控"],
            "performance": "高性能"
        }
    ]
    
    for strategy in strategies:
        print(f"策略: {strategy['name']}")
        print(f"文件: {strategy['file']}")
        print(f"描述: {strategy['description']}")
        print("特性:")
        for feature in strategy['features']:
            print(f"  • {feature}")
        print(f"性能: {strategy['performance']}")
        print("-" * 30)
    
    print("推荐使用:")
    print("- 有V100 GPU: V100TickMLStrategy ⭐")
    print("- 无GPU或CPU: TickMLStrategy")
    
    return True

def run_v100_tests():
    """运行V100测试"""
    print("VNPY V100优化策略测试")
    print("=" * 70)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("专为V100 GPU优化设计")
    
    # 运行测试
    tests = [
        ("GPU库可用性", test_gpu_libraries),
        ("V100优化器", test_v100_optimizer),
        ("V100策略", test_v100_strategy),
        ("VNPY兼容性", test_vnpy_compatibility),
        ("策略版本比较", compare_strategies)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            status = "通过" if result else "失败"
            print(f"\n{test_name} 测试{status}")
        except Exception as e:
            results.append((test_name, False))
            print(f"\n{test_name} 测试异常: {e}")
    
    # 输出总结
    print("\n" + "=" * 70)
    print("测试总结")
    print("=" * 70)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！V100策略可以正常使用。")
        print("\n使用说明:")
        print("1. 在VNPY中使用策略类名: V100TickMLStrategy")
        print("2. 自动检测V100 GPU并启用加速")
        print("3. GPU不可用时自动降级到CPU")
        print("4. 支持异步计算和特征缓存")
        print("5. 实时监控GPU性能提升")
        print("\n推荐配置:")
        print("- enable_v100: True (启用V100优化)")
        print("- gpu_async_compute: True (异步GPU计算)")
        print("- gpu_feature_cache: True (特征缓存)")
        print("- fallback_to_cpu: True (自动降级)")
    else:
        print("⚠️ 部分测试失败，请检查GPU环境。")
    
    return passed, total

if __name__ == "__main__":
    try:
        passed, total = run_v100_tests()
        
        if passed == total:
            sys.exit(0)
        else:
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(3)
    except Exception as e:
        print(f"\n测试异常: {e}")
        sys.exit(4)
