#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Tick数据机器学习策略 - 专门使用Tick数据进行训练和交易
不依赖历史数据，仅使用实时Tick数据构建模型
"""

import os
import sys
import numpy as np
from datetime import datetime, timedelta
from typing import List, Tuple, Dict, Any
from collections import deque

# 导入VNPY组件
from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    ArrayManager
)

# 导入真实ML系统
try:
    from .signal_system.real_ml_system import RealMLSignalSystem
    ML_SYSTEM_AVAILABLE = True
except ImportError:
    try:
        from signal_system.real_ml_system import RealMLSignalSystem
        ML_SYSTEM_AVAILABLE = True
    except ImportError:
        ML_SYSTEM_AVAILABLE = False


class TickMLStrategy(CtaTemplate):
    """
    Tick数据机器学习策略
    
    特点：
    1. 仅使用Tick数据，不依赖历史数据
    2. 实时构建K线数据进行训练
    3. 快速启动，无需等待历史数据加载
    4. 适合License过期或数据源受限的情况
    """
    
    author = "Tick ML Strategy v1.0"
    
    # 策略参数
    signal_threshold = 0.65      # 信号置信度阈值
    position_size = 1            # 基础仓位大小
    stop_loss_pct = 2.0          # 止损百分比
    take_profit_pct = 4.0        # 止盈百分比
    max_position = 3             # 最大持仓手数
    
    # Tick模式参数
    model_type = "lightgbm"      # 模型类型
    ticks_per_bar = 100          # 多少个tick生成一根K线
    min_bars_to_train = 50       # 最少K线数开始训练
    online_learning = True       # 在线学习开关
    
    # 风险控制参数
    daily_max_trades = 10        # 日最大交易次数
    max_loss_pct = 5.0           # 最大亏损百分比
    
    # 变量
    signal_value = 0
    signal_confidence = 0.0
    entry_price = 0.0
    daily_trades = 0
    daily_pnl = 0.0
    model_accuracy = 0.0
    training_samples = 0
    prediction_count = 0
    tick_count = 0
    generated_bars = 0
    
    # 参数和变量列表
    parameters = [
        "signal_threshold", "position_size", "stop_loss_pct", "take_profit_pct",
        "max_position", "model_type", "ticks_per_bar", "min_bars_to_train",
        "online_learning", "daily_max_trades", "max_loss_pct"
    ]
    variables = [
        "signal_value", "signal_confidence", "entry_price", "daily_trades",
        "daily_pnl", "model_accuracy", "training_samples", "prediction_count",
        "tick_count", "generated_bars"
    ]
    
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        """初始化策略"""
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        
        # 创建K线生成器和技术指标管理器
        self.bg = BarGenerator(self.on_bar, 1, self.on_1min_bar)
        self.am = ArrayManager(size=200)
        
        # Tick数据处理
        self.tick_buffer = deque(maxlen=self.ticks_per_bar * 2)
        self.current_bar_ticks = []
        self.last_bar_time = None
        
        # 生成的K线数据
        self.generated_bar_history = []
        self.max_bar_history = 500
        
        # 初始化ML系统
        self.ml_system = None
        self._init_ml_system()
        
        # 交易状态
        self.position_direction = 0
        self.last_trade_time = None
        
        # 性能统计
        self.trade_results = []
        self.total_pnl = 0.0
        self.win_count = 0
        self.trade_count = 0
        
        # 在线学习
        self.last_bar_close = 0.0
        
        self.write_log("Tick ML策略初始化完成")
    
    def _init_ml_system(self):
        """初始化ML系统"""
        if not ML_SYSTEM_AVAILABLE:
            self.write_log("错误: ML系统不可用")
            return
        
        try:
            # 创建ML系统
            self.ml_system = RealMLSignalSystem(model_type=self.model_type)
            
            # 设置模型保存路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            model_dir = os.path.join(current_dir, "signal_system", "models")
            os.makedirs(model_dir, exist_ok=True)
            
            # 提取商品代码
            commodity_code = self._extract_commodity_code(self.vt_symbol)
            self.model_path = os.path.join(model_dir, f"{commodity_code}_{self.model_type}_tick_model.pkl")
            
            self.write_log("商品代码: " + commodity_code)
            self.write_log("Tick模型路径: " + self.model_path)
            
            # 尝试加载已有模型
            if os.path.exists(self.model_path):
                if self.ml_system.load_model(self.model_path):
                    model_info = self.ml_system.get_model_info()
                    self.model_accuracy = model_info.get('latest_accuracy', 0.0)
                    self.training_samples = model_info.get('training_samples', 0)
                    self.write_log("成功加载Tick模型")
                    self.write_log("模型准确率: " + "{:.3f}".format(self.model_accuracy))
                else:
                    self.write_log("加载Tick模型失败")
            else:
                self.write_log("未找到Tick模型，将从实时数据训练")
            
        except Exception as e:
            self.write_log("初始化ML系统异常: " + str(e))
            self.ml_system = None
    
    def _extract_commodity_code(self, vt_symbol: str) -> str:
        """提取商品代码"""
        try:
            symbol = vt_symbol.split('.')[0]
            commodity_code = ""
            for char in symbol:
                if char.isalpha():
                    commodity_code += char.lower()
                else:
                    break
            return commodity_code if commodity_code else symbol.lower()
        except:
            return vt_symbol.lower()
    
    def on_init(self):
        """策略初始化回调"""
        self.write_log("Tick ML策略初始化")
        self.write_log("使用Tick数据模式，无需历史数据")
        self.write_log("每" + str(self.ticks_per_bar) + "个Tick生成一根K线")
        self.write_log("收集" + str(self.min_bars_to_train) + "根K线后开始训练")
    
    def on_start(self):
        """策略启动回调"""
        self.write_log("Tick ML策略启动")
        self.write_log("开始收集Tick数据...")
        
        if self.ml_system and self.ml_system.is_trained:
            self.write_log("使用已训练模型，可立即开始交易")
        else:
            self.write_log("等待收集足够数据进行模型训练")
    
    def on_stop(self):
        """策略停止回调"""
        self.write_log("Tick ML策略停止")
        
        # 保存模型
        if self.ml_system and self.ml_system.is_trained:
            self.ml_system.save_model(self.model_path)
            self.write_log("Tick模型已保存")
        
        # 输出统计信息
        self.write_log("Tick统计: 总计" + str(self.tick_count) + "个")
        self.write_log("生成K线: " + str(self.generated_bars) + "根")
        
        if self.trade_count > 0:
            win_rate = self.win_count / self.trade_count
            avg_pnl = self.total_pnl / self.trade_count
            self.write_log("交易统计: 胜率" + "{:.2%}".format(win_rate) + 
                         ", 平均盈亏" + "{:.4f}".format(avg_pnl))
    
    def on_tick(self, tick: TickData):
        """Tick数据回调"""
        # 更新计数
        self.tick_count += 1
        
        # 添加到缓存
        self.tick_buffer.append({
            'datetime': tick.datetime,
            'last_price': tick.last_price,
            'volume': tick.volume,
            'bid_price_1': tick.bid_price_1,
            'ask_price_1': tick.ask_price_1,
            'high_price': max(tick.bid_price_1, tick.ask_price_1, tick.last_price),
            'low_price': min(tick.bid_price_1, tick.ask_price_1, tick.last_price)
        })
        
        # 添加到当前K线的tick集合
        self.current_bar_ticks.append(tick)
        
        # 检查是否需要生成新K线
        if len(self.current_bar_ticks) >= self.ticks_per_bar:
            self._generate_bar_from_current_ticks()
        
        # 更新BarGenerator（用于技术指标计算）
        self.bg.update_tick(tick)
    
    def _generate_bar_from_current_ticks(self):
        """从当前tick集合生成K线"""
        try:
            if not self.current_bar_ticks:
                return
            
            # 提取价格数据
            prices = [t.last_price for t in self.current_bar_ticks if t.last_price > 0]
            if not prices:
                return
            
            # 计算OHLCV
            open_price = prices[0]
            high_price = max(prices)
            low_price = min(prices)
            close_price = prices[-1]
            volume = sum(t.volume for t in self.current_bar_ticks if t.volume > 0)
            
            if volume == 0:
                volume = len(self.current_bar_ticks)  # 使用tick数量作为成交量
            
            # 创建K线数据
            bar_dict = {
                'datetime': self.current_bar_ticks[-1].datetime,
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': volume
            }
            
            # 添加到历史数据
            self.generated_bar_history.append(bar_dict)
            self.generated_bars += 1
            
            # 限制历史数据大小
            if len(self.generated_bar_history) > self.max_bar_history:
                self.generated_bar_history.pop(0)
            
            # 输出进度
            if self.generated_bars % 10 == 0:
                self.write_log("已生成K线: " + str(self.generated_bars) + "根")
            
            # 检查是否可以开始训练
            if (self.ml_system and not self.ml_system.is_trained and
                len(self.generated_bar_history) >= self.min_bars_to_train):
                self.write_log("K线数据充足，开始训练模型...")
                self._train_model_from_generated_bars()

            # 如果模型已训练，进行预测和交易
            elif self.ml_system and self.ml_system.is_trained:
                self._execute_tick_ml_trading(bar_dict)
            
            # 在线学习
            if self.online_learning and self.last_bar_close > 0:
                self._online_learning_from_bar(bar_dict)
            
            self.last_bar_close = close_price
            
            # 清空当前tick集合
            self.current_bar_ticks = []
            
            # 更新界面
            self.put_event()
            
        except Exception as e:
            # 安全的错误处理
            if hasattr(self, 'cta_engine') and self.cta_engine:
                self.write_log("生成K线失败: " + str(e))
    
    def _train_model_from_generated_bars(self):
        """从生成的K线训练模型"""
        try:
            self.write_log("=" * 40)
            self.write_log("开始从Tick生成的K线训练模型")
            self.write_log("数据量: " + str(len(self.generated_bar_history)) + "根K线")
            
            # 提取特征
            features = self.ml_system.extract_features_from_bars(self.generated_bar_history)
            if len(features) < 20:
                self.write_log("特征数据不足，延迟训练")
                return
            
            # 生成标签
            labels = self.ml_system.generate_labels_from_bars(self.generated_bar_history)
            if len(labels) != len(features):
                self.write_log("特征标签不匹配")
                return
            
            # 训练模型
            if self.ml_system.train_model(features, labels):
                model_info = self.ml_system.get_model_info()
                self.model_accuracy = model_info.get('latest_accuracy', 0.0)
                self.training_samples = model_info.get('training_samples', 0)
                
                self.write_log("🎉 Tick模型训练成功!")
                self.write_log("准确率: " + "{:.3f}".format(self.model_accuracy))
                self.write_log("样本数: " + str(self.training_samples))
                
                # 保存模型
                if self.ml_system.save_model(self.model_path):
                    self.write_log("✅ Tick模型已保存")
                
                self.write_log("🚀 可以开始基于Tick的ML交易!")
            else:
                self.write_log("❌ Tick模型训练失败")
            
            self.write_log("=" * 40)
            
        except Exception as e:
            self.write_log("训练Tick模型异常: " + str(e))
    
    def _execute_tick_ml_trading(self, bar_dict: Dict):
        """执行基于Tick的ML交易"""
        try:
            # 提取特征进行预测
            if len(self.generated_bar_history) < 30:
                return
            
            recent_bars = self.generated_bar_history[-30:]
            features_list = self.ml_system.extract_features_from_bars(recent_bars)
            
            if not features_list:
                return
            
            current_features = features_list[-1]
            self.signal_value, self.signal_confidence = self.ml_system.predict(current_features)
            self.prediction_count += 1
            
            # 检查交易条件
            if not self._can_trade():
                return
            
            if abs(self.signal_confidence) < self.signal_threshold:
                return
            
            current_pos = self.pos
            current_price = bar_dict['close']
            
            # 开仓逻辑
            if current_pos == 0:
                if self.signal_value == 1:
                    self.buy(current_price, self.position_size)
                    self.entry_price = current_price
                    self.position_direction = 1
                    self.daily_trades += 1
                    self.write_log("Tick ML买入: " + "{:.2f}".format(current_price) + 
                                 ", 置信度: " + "{:.3f}".format(self.signal_confidence))
                    
                elif self.signal_value == -1:
                    self.sell(current_price, self.position_size)
                    self.entry_price = current_price
                    self.position_direction = -1
                    self.daily_trades += 1
                    self.write_log("Tick ML卖出: " + "{:.2f}".format(current_price) + 
                                 ", 置信度: " + "{:.3f}".format(self.signal_confidence))
            
            # 平仓逻辑
            elif current_pos != 0:
                should_close = False
                close_reason = ""
                
                # 信号反转
                if current_pos > 0 and self.signal_value == -1:
                    should_close = True
                    close_reason = "信号反转"
                elif current_pos < 0 and self.signal_value == 1:
                    should_close = True
                    close_reason = "信号反转"
                
                # 止损止盈
                if not should_close:
                    pnl_pct = self._calculate_pnl_pct(current_price)
                    if pnl_pct <= -self.stop_loss_pct:
                        should_close = True
                        close_reason = "止损"
                    elif pnl_pct >= self.take_profit_pct:
                        should_close = True
                        close_reason = "止盈"
                
                if should_close:
                    if current_pos > 0:
                        self.sell(current_price, abs(current_pos))
                    else:
                        self.buy(current_price, abs(current_pos))
                    
                    self.write_log("Tick平仓: " + close_reason + ", 价格: " + "{:.2f}".format(current_price))
                    self._reset_position()
                    self.daily_trades += 1
                    
        except Exception as e:
            self.write_log("执行Tick ML交易失败: " + str(e))
    
    def _online_learning_from_bar(self, bar_dict: Dict):
        """从K线进行在线学习"""
        try:
            if self.last_bar_close <= 0:
                return
            
            # 计算收益率
            actual_return = (bar_dict['close'] - self.last_bar_close) / self.last_bar_close
            
            # 提取特征
            if len(self.generated_bar_history) >= 30:
                recent_bars = self.generated_bar_history[-31:-1]
                features_list = self.ml_system.extract_features_from_bars(recent_bars)
                
                if features_list:
                    last_features = features_list[-1]
                    self.ml_system.add_online_sample(last_features, actual_return)
                    
        except Exception as e:
            self.write_log("Tick在线学习失败: " + str(e))
    
    def _can_trade(self) -> bool:
        """检查是否可以交易"""
        return (self.daily_trades < self.daily_max_trades and 
                abs(self.daily_pnl) < self.max_loss_pct / 100)
    
    def _calculate_pnl_pct(self, current_price: float) -> float:
        """计算盈亏百分比"""
        if self.entry_price == 0:
            return 0.0
        
        if self.position_direction == 1:
            return (current_price - self.entry_price) / self.entry_price * 100
        else:
            return (self.entry_price - current_price) / self.entry_price * 100
    
    def _reset_position(self):
        """重置持仓状态"""
        self.position_direction = 0
        self.entry_price = 0.0
    
    def on_1min_bar(self, bar: BarData):
        """1分钟K线回调（由BarGenerator生成，用于技术指标）"""
        self.am.update_bar(bar)
    
    def on_bar(self, bar: BarData):
        """K线回调"""
        pass
    
    def on_order(self, order: OrderData):
        """订单回调"""
        pass
    
    def on_trade(self, trade: TradeData):
        """成交回调"""
        self.trade_count += 1
        
        if self.entry_price > 0:
            if trade.direction.value == "多":
                pnl = (trade.price - self.entry_price) / self.entry_price
            else:
                pnl = (self.entry_price - trade.price) / self.entry_price
            
            self.total_pnl += pnl
            self.trade_results.append(pnl)
            
            if pnl > 0:
                self.win_count += 1
        
        if self.pos == 0:
            self._reset_position()
        
        self.write_log("Tick交易: " + str(trade.direction) + " " + str(trade.volume) + "手 @" + "{:.2f}".format(trade.price))
        
        # 更新模型信息
        if self.ml_system:
            model_info = self.ml_system.get_model_info()
            self.model_accuracy = model_info.get('latest_accuracy', 0.0)
            self.training_samples = model_info.get('training_samples', 0)
        
        self.put_event()
    
    def on_stop_order(self, stop_order: StopOrder):
        """停止单回调"""
        pass
