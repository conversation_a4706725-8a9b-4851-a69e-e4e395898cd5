#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
V100优化Tick ML策略 - 完美适配VNPY的GPU加速策略
设计原则：VNPY兼容性第一，GPU性能第二
"""

import os
import sys
import numpy as np
from datetime import datetime, timedelta
from typing import List, Tuple, Dict, Any
from collections import deque

# 导入VNPY组件
from vnpy_ctastrategy import (
    CtaTemplate,
    StopOrder,
    TickData,
    BarData,
    TradeData,
    OrderData,
    BarGenerator,
    ArrayManager
)

# 安全导入V100优化器
try:
    from .signal_system.v100_optimizer import get_v100_optimizer, GPU_AVAILABLE
    from .signal_system.real_ml_system import RealMLSignalSystem
    V100_AVAILABLE = True
    ML_SYSTEM_AVAILABLE = True
except ImportError:
    try:
        from signal_system.v100_optimizer import get_v100_optimizer, GPU_AVAILABLE
        from signal_system.real_ml_system import RealMLSignalSystem
        V100_AVAILABLE = True
        ML_SYSTEM_AVAILABLE = True
    except ImportError:
        V100_AVAILABLE = False
        ML_SYSTEM_AVAILABLE = False


class V100TickMLStrategy(CtaTemplate):
    """
    V100优化Tick ML策略
    
    特点：
    1. 完美兼容VNPY - 不影响策略主线程运行
    2. 智能GPU加速 - 自动检测并使用V100
    3. 优雅降级 - GPU不可用时自动使用CPU
    4. 异步计算 - GPU计算不阻塞交易决策
    5. 错误隔离 - GPU错误不影响策略稳定性
    """
    
    author = "V100 Tick ML Strategy v1.0"
    
    # 策略参数
    signal_threshold = 0.65      # 信号置信度阈值
    position_size = 1            # 基础仓位大小
    stop_loss_pct = 2.0          # 止损百分比
    take_profit_pct = 4.0        # 止盈百分比
    max_position = 3             # 最大持仓手数
    
    # V100优化参数
    enable_v100 = True           # 启用V100优化
    gpu_async_compute = True     # 异步GPU计算
    gpu_feature_cache = True     # GPU特征缓存
    fallback_to_cpu = True       # 自动降级到CPU
    
    # Tick模式参数
    model_type = "lightgbm"      # 模型类型
    ticks_per_bar = 100          # 多少个tick生成一根K线
    min_bars_to_train = 50       # 最少K线数开始训练
    online_learning = True       # 在线学习开关
    
    # 风险控制参数
    daily_max_trades = 10        # 日最大交易次数
    max_loss_pct = 5.0           # 最大亏损百分比
    
    # 变量
    signal_value = 0
    signal_confidence = 0.0
    entry_price = 0.0
    daily_trades = 0
    daily_pnl = 0.0
    model_accuracy = 0.0
    training_samples = 0
    prediction_count = 0
    tick_count = 0
    generated_bars = 0
    
    # V100性能变量
    gpu_enabled = False
    gpu_compute_count = 0
    cpu_compute_count = 0
    avg_gpu_time = 0.0
    avg_cpu_time = 0.0
    gpu_speedup = 1.0
    
    # 参数和变量列表
    parameters = [
        "signal_threshold", "position_size", "stop_loss_pct", "take_profit_pct",
        "max_position", "enable_v100", "gpu_async_compute", "gpu_feature_cache",
        "fallback_to_cpu", "model_type", "ticks_per_bar", "min_bars_to_train",
        "online_learning", "daily_max_trades", "max_loss_pct"
    ]
    variables = [
        "signal_value", "signal_confidence", "entry_price", "daily_trades",
        "daily_pnl", "model_accuracy", "training_samples", "prediction_count",
        "tick_count", "generated_bars", "gpu_enabled", "gpu_compute_count",
        "cpu_compute_count", "avg_gpu_time", "avg_cpu_time", "gpu_speedup"
    ]
    
    def __init__(self, cta_engine, strategy_name, vt_symbol, setting):
        """初始化策略"""
        super().__init__(cta_engine, strategy_name, vt_symbol, setting)
        
        # 创建K线生成器和技术指标管理器
        self.bg = BarGenerator(self.on_bar, 1, self.on_1min_bar)
        self.am = ArrayManager(size=200)
        
        # V100优化器
        self.v100_optimizer = None
        self._init_v100_optimizer()
        
        # Tick数据处理
        self.tick_buffer = deque(maxlen=self.ticks_per_bar * 2)
        self.current_bar_ticks = []
        self.last_bar_time = None
        
        # 生成的K线数据
        self.generated_bar_history = []
        self.max_bar_history = 500
        
        # 特征缓存（GPU优化）
        self.feature_cache = {}
        self.cache_hit_count = 0
        self.cache_miss_count = 0
        
        # 异步计算任务
        self.pending_gpu_tasks = {}
        self.last_feature_compute_time = 0
        
        # 初始化ML系统
        self.ml_system = None
        self._init_ml_system()
        
        # 交易状态
        self.position_direction = 0
        self.last_trade_time = None
        
        # 性能统计
        self.trade_results = []
        self.total_pnl = 0.0
        self.win_count = 0
        self.trade_count = 0
        
        # 在线学习
        self.last_bar_close = 0.0
        
        self.write_log("V100 Tick ML策略初始化完成")
    
    def _init_v100_optimizer(self):
        """初始化V100优化器"""
        if not self.enable_v100 or not V100_AVAILABLE:
            self.write_log("V100优化已禁用或不可用")
            return
        
        try:
            self.v100_optimizer = get_v100_optimizer(enable_gpu=True)
            
            if self.v100_optimizer.enable_gpu:
                self.gpu_enabled = True
                self.write_log("V100优化器初始化成功")
                self.write_log("GPU设备: " + str(self.v100_optimizer.gpu_device))
            else:
                self.write_log("V100不可用，将使用CPU计算")
                
        except Exception as e:
            self.write_log("V100优化器初始化失败: " + str(e))
            self.v100_optimizer = None
    
    def _init_ml_system(self):
        """初始化ML系统"""
        if not ML_SYSTEM_AVAILABLE:
            self.write_log("错误: ML系统不可用")
            return
        
        try:
            # 创建ML系统
            self.ml_system = RealMLSignalSystem(model_type=self.model_type)
            
            # 设置模型保存路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            model_dir = os.path.join(current_dir, "signal_system", "models")
            os.makedirs(model_dir, exist_ok=True)
            
            # 提取商品代码
            commodity_code = self._extract_commodity_code(self.vt_symbol)
            model_suffix = "v100_tick" if self.gpu_enabled else "tick"
            self.model_path = os.path.join(model_dir, f"{commodity_code}_{self.model_type}_{model_suffix}_model.pkl")
            
            self.write_log("商品代码: " + commodity_code)
            self.write_log("V100模型路径: " + self.model_path)
            
            # 尝试加载已有模型
            if os.path.exists(self.model_path):
                if self.ml_system.load_model(self.model_path):
                    model_info = self.ml_system.get_model_info()
                    self.model_accuracy = model_info.get('latest_accuracy', 0.0)
                    self.training_samples = model_info.get('training_samples', 0)
                    self.write_log("成功加载V100模型")
                    self.write_log("模型准确率: " + "{:.3f}".format(self.model_accuracy))
                else:
                    self.write_log("加载V100模型失败")
            else:
                self.write_log("未找到V100模型，将从实时数据训练")
            
        except Exception as e:
            self.write_log("初始化ML系统异常: " + str(e))
            self.ml_system = None
    
    def _extract_commodity_code(self, vt_symbol: str) -> str:
        """提取商品代码"""
        try:
            symbol = vt_symbol.split('.')[0]
            commodity_code = ""
            for char in symbol:
                if char.isalpha():
                    commodity_code += char.lower()
                else:
                    break
            return commodity_code if commodity_code else symbol.lower()
        except:
            return vt_symbol.lower()
    
    def on_init(self):
        """策略初始化回调"""
        self.write_log("V100 Tick ML策略初始化")
        self.write_log("GPU状态: " + ("启用" if self.gpu_enabled else "禁用"))
        self.write_log("使用Tick数据模式，无需历史数据")
        self.write_log("每" + str(self.ticks_per_bar) + "个Tick生成一根K线")
        self.write_log("收集" + str(self.min_bars_to_train) + "根K线后开始训练")
    
    def on_start(self):
        """策略启动回调"""
        self.write_log("V100 Tick ML策略启动")
        self.write_log("开始收集Tick数据...")
        
        if self.gpu_enabled:
            self.write_log("V100 GPU加速已启用")
        
        if self.ml_system and self.ml_system.is_trained:
            self.write_log("使用已训练模型，可立即开始交易")
        else:
            self.write_log("等待收集足够数据进行模型训练")
    
    def on_stop(self):
        """策略停止回调"""
        self.write_log("V100 Tick ML策略停止")
        
        # 保存模型
        if self.ml_system and self.ml_system.is_trained:
            self.ml_system.save_model(self.model_path)
            self.write_log("V100模型已保存")
        
        # 输出性能统计
        self._output_performance_stats()
        
        # 关闭V100优化器
        if self.v100_optimizer:
            try:
                # 获取最终性能统计
                stats = self.v100_optimizer.get_performance_stats()
                self.gpu_speedup = stats.get('speedup_ratio', 1.0)
                self.write_log("V100性能提升: " + "{:.2f}".format(self.gpu_speedup) + "倍")
            except:
                pass
    
    def on_tick(self, tick: TickData):
        """Tick数据回调"""
        # 更新计数
        self.tick_count += 1
        
        # 添加到缓存
        tick_dict = {
            'datetime': tick.datetime,
            'last_price': tick.last_price,
            'volume': tick.volume,
            'bid_price_1': tick.bid_price_1,
            'ask_price_1': tick.ask_price_1,
            'high_price': max(tick.bid_price_1, tick.ask_price_1, tick.last_price),
            'low_price': min(tick.bid_price_1, tick.ask_price_1, tick.last_price)
        }
        
        self.tick_buffer.append(tick_dict)
        self.current_bar_ticks.append(tick)
        
        # 检查是否需要生成新K线
        if len(self.current_bar_ticks) >= self.ticks_per_bar:
            self._generate_bar_from_current_ticks()
        
        # 更新BarGenerator（用于技术指标计算）
        self.bg.update_tick(tick)
    
    def _generate_bar_from_current_ticks(self):
        """从当前tick集合生成K线"""
        try:
            if not self.current_bar_ticks:
                return
            
            # 提取价格数据
            prices = [t.last_price for t in self.current_bar_ticks if t.last_price > 0]
            if not prices:
                return
            
            # 计算OHLCV
            open_price = prices[0]
            high_price = max(prices)
            low_price = min(prices)
            close_price = prices[-1]
            volume = sum(t.volume for t in self.current_bar_ticks if t.volume > 0)
            
            if volume == 0:
                volume = len(self.current_bar_ticks)
            
            # 创建K线数据
            bar_dict = {
                'datetime': self.current_bar_ticks[-1].datetime,
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': volume
            }
            
            # 添加到历史数据
            self.generated_bar_history.append(bar_dict)
            self.generated_bars += 1
            
            # 限制历史数据大小
            if len(self.generated_bar_history) > self.max_bar_history:
                self.generated_bar_history.pop(0)
            
            # 输出进度
            if self.generated_bars % 10 == 0:
                self.write_log("已生成K线: " + str(self.generated_bars) + "根")
                if self.gpu_enabled:
                    self.write_log("GPU计算: " + str(self.gpu_compute_count) + 
                                 ", CPU计算: " + str(self.cpu_compute_count))
            
            # 检查是否可以开始训练
            if (self.ml_system and not self.ml_system.is_trained and 
                len(self.generated_bar_history) >= self.min_bars_to_train):
                self.write_log("K线数据充足，开始训练模型...")
                self._train_model_from_generated_bars()
            
            # 如果模型已训练，进行预测和交易
            elif self.ml_system and self.ml_system.is_trained:
                self._execute_v100_ml_trading(bar_dict)
            
            # 在线学习
            if self.online_learning and self.last_bar_close > 0:
                self._online_learning_from_bar(bar_dict)
            
            self.last_bar_close = close_price
            
            # 清空当前tick集合
            self.current_bar_ticks = []
            
            # 更新界面
            self.put_event()
            
        except Exception as e:
            self.write_log("生成K线失败: " + str(e))
    
    def _extract_features_v100(self, bar_history: List[Dict]) -> List[float]:
        """V100加速特征提取"""
        try:
            if not self.v100_optimizer or not self.gpu_enabled:
                return self._extract_features_cpu(bar_history)
            
            # 检查缓存
            cache_key = str(len(bar_history)) + "_" + str(hash(str(bar_history[-1])))
            if self.gpu_feature_cache and cache_key in self.feature_cache:
                self.cache_hit_count += 1
                return self.feature_cache[cache_key]
            
            self.cache_miss_count += 1
            
            if self.gpu_async_compute:
                # 异步GPU计算
                task_id = self.v100_optimizer.compute_features_async(bar_history)
                if task_id:
                    # 等待结果（短时间）
                    result = self.v100_optimizer.get_result(task_id, timeout=0.01)
                    if result:
                        features, compute_time = result
                        if features is not None:
                            self.gpu_compute_count += 1
                            self.avg_gpu_time = (self.avg_gpu_time * (self.gpu_compute_count - 1) + compute_time) / self.gpu_compute_count
                            
                            # 缓存结果
                            if self.gpu_feature_cache:
                                self.feature_cache[cache_key] = features.tolist()
                            
                            return features.tolist()
            
            # 同步GPU计算或CPU备用
            features = self.v100_optimizer.compute_features_sync(bar_history)
            if features is not None:
                self.gpu_compute_count += 1
                return features.tolist()
            else:
                # 降级到CPU
                return self._extract_features_cpu(bar_history)
                
        except Exception as e:
            self.write_log("V100特征提取失败: " + str(e))
            return self._extract_features_cpu(bar_history)
    
    def _extract_features_cpu(self, bar_history: List[Dict]) -> List[float]:
        """CPU特征提取（备用方案）"""
        try:
            if len(bar_history) < 5:
                return [0.0] * 20
            
            self.cpu_compute_count += 1
            
            # 使用ML系统的特征提取
            features_list = self.ml_system.extract_features_from_bars(bar_history)
            if features_list:
                return features_list[-1]
            else:
                return [0.0] * 20
                
        except Exception as e:
            self.write_log("CPU特征提取失败: " + str(e))
            return [0.0] * 20

    def _train_model_from_generated_bars(self):
        """从生成的K线训练模型"""
        try:
            self.write_log("=" * 40)
            self.write_log("开始V100加速模型训练")
            self.write_log("数据量: " + str(len(self.generated_bar_history)) + "根K线")

            # 提取特征（使用V100加速）
            features = self.ml_system.extract_features_from_bars(self.generated_bar_history)
            if len(features) < 20:
                self.write_log("特征数据不足，延迟训练")
                return

            # 生成标签
            labels = self.ml_system.generate_labels_from_bars(self.generated_bar_history)
            if len(labels) != len(features):
                self.write_log("特征标签不匹配")
                return

            # 训练模型
            if self.ml_system.train_model(features, labels):
                model_info = self.ml_system.get_model_info()
                self.model_accuracy = model_info.get('latest_accuracy', 0.0)
                self.training_samples = model_info.get('training_samples', 0)

                self.write_log("V100模型训练成功!")
                self.write_log("准确率: " + "{:.3f}".format(self.model_accuracy))
                self.write_log("样本数: " + str(self.training_samples))

                # 保存模型
                if self.ml_system.save_model(self.model_path):
                    self.write_log("V100模型已保存")

                self.write_log("可以开始V100加速ML交易!")
            else:
                self.write_log("V100模型训练失败")

            self.write_log("=" * 40)

        except Exception as e:
            self.write_log("训练V100模型异常: " + str(e))

    def _execute_v100_ml_trading(self, bar_dict: Dict):
        """执行V100加速的ML交易"""
        try:
            # 提取特征进行预测（使用V100加速）
            if len(self.generated_bar_history) < 30:
                return

            recent_bars = self.generated_bar_history[-30:]
            current_features = self._extract_features_v100(recent_bars)

            if not current_features:
                return

            self.signal_value, self.signal_confidence = self.ml_system.predict(current_features)
            self.prediction_count += 1

            # 检查交易条件
            if not self._can_trade():
                return

            if abs(self.signal_confidence) < self.signal_threshold:
                return

            current_pos = self.pos
            current_price = bar_dict['close']

            # 开仓逻辑
            if current_pos == 0:
                if self.signal_value == 1:
                    self.buy(current_price, self.position_size)
                    self.entry_price = current_price
                    self.position_direction = 1
                    self.daily_trades += 1
                    self.write_log("V100 ML买入: " + "{:.2f}".format(current_price) +
                                 ", 置信度: " + "{:.3f}".format(self.signal_confidence))

                elif self.signal_value == -1:
                    self.sell(current_price, self.position_size)
                    self.entry_price = current_price
                    self.position_direction = -1
                    self.daily_trades += 1
                    self.write_log("V100 ML卖出: " + "{:.2f}".format(current_price) +
                                 ", 置信度: " + "{:.3f}".format(self.signal_confidence))

            # 平仓逻辑
            elif current_pos != 0:
                should_close = False
                close_reason = ""

                # 信号反转
                if current_pos > 0 and self.signal_value == -1:
                    should_close = True
                    close_reason = "信号反转"
                elif current_pos < 0 and self.signal_value == 1:
                    should_close = True
                    close_reason = "信号反转"

                # 止损止盈
                if not should_close:
                    pnl_pct = self._calculate_pnl_pct(current_price)
                    if pnl_pct <= -self.stop_loss_pct:
                        should_close = True
                        close_reason = "止损"
                    elif pnl_pct >= self.take_profit_pct:
                        should_close = True
                        close_reason = "止盈"

                if should_close:
                    if current_pos > 0:
                        self.sell(current_price, abs(current_pos))
                    else:
                        self.buy(current_price, abs(current_pos))

                    self.write_log("V100平仓: " + close_reason + ", 价格: " + "{:.2f}".format(current_price))
                    self._reset_position()
                    self.daily_trades += 1

        except Exception as e:
            self.write_log("执行V100 ML交易失败: " + str(e))

    def _online_learning_from_bar(self, bar_dict: Dict):
        """从K线进行在线学习"""
        try:
            if self.last_bar_close <= 0:
                return

            # 计算收益率
            actual_return = (bar_dict['close'] - self.last_bar_close) / self.last_bar_close

            # 提取特征（使用V100加速）
            if len(self.generated_bar_history) >= 30:
                recent_bars = self.generated_bar_history[-31:-1]
                last_features = self._extract_features_v100(recent_bars)

                if last_features:
                    self.ml_system.add_online_sample(last_features, actual_return)

        except Exception as e:
            self.write_log("V100在线学习失败: " + str(e))

    def _can_trade(self) -> bool:
        """检查是否可以交易"""
        return (self.daily_trades < self.daily_max_trades and
                abs(self.daily_pnl) < self.max_loss_pct / 100)

    def _calculate_pnl_pct(self, current_price: float) -> float:
        """计算盈亏百分比"""
        if self.entry_price == 0:
            return 0.0

        if self.position_direction == 1:
            return (current_price - self.entry_price) / self.entry_price * 100
        else:
            return (self.entry_price - current_price) / self.entry_price * 100

    def _reset_position(self):
        """重置持仓状态"""
        self.position_direction = 0
        self.entry_price = 0.0

    def on_1min_bar(self, bar: BarData):
        """1分钟K线回调（由BarGenerator生成，用于技术指标）"""
        self.am.update_bar(bar)

    def on_bar(self, bar: BarData):
        """K线回调"""
        pass

    def on_order(self, order: OrderData):
        """订单回调"""
        pass

    def on_trade(self, trade: TradeData):
        """成交回调"""
        self.trade_count += 1

        if self.entry_price > 0:
            if trade.direction.value == "多":
                pnl = (trade.price - self.entry_price) / self.entry_price
            else:
                pnl = (self.entry_price - trade.price) / self.entry_price

            self.total_pnl += pnl
            self.trade_results.append(pnl)

            if pnl > 0:
                self.win_count += 1

        if self.pos == 0:
            self._reset_position()

        self.write_log("V100交易: " + str(trade.direction) + " " + str(trade.volume) + "手 @" + "{:.2f}".format(trade.price))

        # 更新模型信息
        if self.ml_system:
            model_info = self.ml_system.get_model_info()
            self.model_accuracy = model_info.get('latest_accuracy', 0.0)
            self.training_samples = model_info.get('training_samples', 0)

        # 更新V100性能统计
        if self.v100_optimizer:
            try:
                stats = self.v100_optimizer.get_performance_stats()
                self.gpu_speedup = stats.get('speedup_ratio', 1.0)
            except:
                pass

        self.put_event()

    def on_stop_order(self, stop_order: StopOrder):
        """停止单回调"""
        pass

    def _output_performance_stats(self):
        """输出性能统计"""
        try:
            self.write_log("=" * 50)
            self.write_log("V100性能统计")
            self.write_log("=" * 50)

            # 基础统计
            self.write_log("Tick总数: " + str(self.tick_count))
            self.write_log("生成K线: " + str(self.generated_bars) + "根")
            self.write_log("交易次数: " + str(self.trade_count))

            # GPU统计
            if self.gpu_enabled:
                self.write_log("GPU计算: " + str(self.gpu_compute_count) + "次")
                self.write_log("CPU计算: " + str(self.cpu_compute_count) + "次")
                self.write_log("GPU平均时间: " + "{:.4f}".format(self.avg_gpu_time) + "秒")
                self.write_log("CPU平均时间: " + "{:.4f}".format(self.avg_cpu_time) + "秒")

                if self.avg_cpu_time > 0 and self.avg_gpu_time > 0:
                    speedup = self.avg_cpu_time / self.avg_gpu_time
                    self.write_log("V100加速比: " + "{:.2f}".format(speedup) + "倍")

                # 缓存统计
                if self.gpu_feature_cache:
                    total_cache = self.cache_hit_count + self.cache_miss_count
                    if total_cache > 0:
                        hit_rate = self.cache_hit_count / total_cache
                        self.write_log("缓存命中率: " + "{:.2%}".format(hit_rate))

            # 交易统计
            if self.trade_count > 0:
                win_rate = self.win_count / self.trade_count
                avg_pnl = self.total_pnl / self.trade_count
                self.write_log("交易胜率: " + "{:.2%}".format(win_rate))
                self.write_log("平均盈亏: " + "{:.4f}".format(avg_pnl))

            self.write_log("=" * 50)

        except Exception as e:
            self.write_log("输出性能统计失败: " + str(e))
